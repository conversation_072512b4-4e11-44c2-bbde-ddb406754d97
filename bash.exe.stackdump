Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x1FEBA
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210285FF9, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9B80  0002100690B4 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9E60  00021006A49D (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8BCF80000 ntdll.dll
7FF8BCD50000 KERNEL32.DLL
7FF8BA930000 KERNELBASE.dll
7FF8BB9C0000 USER32.dll
7FF8BA5A0000 win32u.dll
7FF8BCE30000 GDI32.dll
7FF8BA2E0000 gdi32full.dll
7FF8BA700000 msvcp_win.dll
7FF8BA190000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8BB010000 advapi32.dll
7FF8BCAC0000 msvcrt.dll
7FF8BB560000 sechost.dll
7FF8BB610000 RPCRT4.dll
7FF8B96D0000 CRYPTBASE.DLL
7FF8BA660000 bcryptPrimitives.dll
7FF8BC860000 IMM32.DLL
