{"name": "CloudPos", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/checkbox": "^0.5.20", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/material-top-tabs": "^7.2.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "axios": "^1.9.0", "lucide-react-native": "^0.511.0", "react": "19.0.0", "react-native": "0.79.1", "react-native-bluetooth-classic": "^1.73.0-rc.13", "react-native-calendars": "^1.1312.1", "react-native-element-dropdown": "^2.12.4", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.27.1", "react-native-pager-view": "^6.8.1", "react-native-paper": "^5.14.0", "react-native-permissions": "^5.4.1", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-tab-view": "^4.0.10", "react-native-thermal-receipt-printer": "^1.2.0-rc.2", "react-native-ui-datepicker": "^3.1.2", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}