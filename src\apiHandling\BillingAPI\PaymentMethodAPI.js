export const getPaymentMethodsAPI = async (branchId, token) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/branchpayment?BranchID=${branchId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        if (!response.ok) {
            return [];
        }
        const data = await response.json();
        return data;
    } catch (error) {
        return [];
    }
};
