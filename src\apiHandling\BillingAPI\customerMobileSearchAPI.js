import axios from 'axios';

export const customerMobileSearchAPI = async (branchId, customerMobile, bearerToken) => {
  const baseUrl = 'https://retailuat.abisaio.com:9001/api/POSCustomer';
  const url = `${baseUrl}/MOBILE/${customerMobile}`;

  console.log(url);

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data?.customers) {
      return {
        success: true,
        count: response.data.count,
        customers: response.data.customers,
      };
    } else {
      return {
        success: false,
        message: 'No customers found.',
      };
    }
  } catch (error) {
    console.error('Error fetching customer data:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
