// src/apiHandling/fetchBillDetailsAPI.js
import axios from 'axios';

export const fetchBillDetails = async (branchId, saleId, bearerToken) => {
    try {
        const response = await axios.get(
            `https://retailuat.abisibg.com/api/v1/fetchsale2`,
            {
                params: {
                    BranchID: branchId,
                    SaleID: saleId,
                },
                headers: {
                    Authorization: `Bearer ${bearerToken}`,
                },
            }
        );
        return response.data;
    } catch (error) {
        console.error('Error fetching bill details:', error);
        return null;
    }
};
