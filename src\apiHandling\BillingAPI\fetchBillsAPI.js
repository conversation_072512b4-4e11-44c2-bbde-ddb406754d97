// fetchBillsAPI.js
import axios from 'axios';

export const fetchBillsByDateNew = async (branchId, stage, fromDate, toDate, bearerToken) => {
    try {
        const baseURL = `https://retailuat.abisibg.com/api/v1/fetchsale`;

        // Build query string manually
        const queryParams = new URLSearchParams({
            BranchId: branchId,
            stage,
            FromDate: fromDate,
            ToDate: toDate,
        }).toString();

        const fullURL = `${baseURL}?${queryParams}`;
        //console.log('Fetching URL:', fullURL);

        const response = await axios.get(fullURL, {
            headers: {
                Authorization: `Bearer ${bearerToken}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('Error fetching sales bills:', error);
        return [];
    }
};
