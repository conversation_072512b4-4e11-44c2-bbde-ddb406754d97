// timeSlotAPI.js

export const fetchTimeSlots = async (branchId, bearerToken) => {
    try {
        const apiUrl = `https://retailuat.abisibg.com//api/v1/timeslot?branchId=${branchId}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${bearerToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Filter out deleted time slots and return the data
        const activeTimeSlots = data.filter(slot => slot.Deleted === 'N');

        return {
            success: true,
            timeSlots: activeTimeSlots
        };
    } catch (error) {
        console.error('Error fetching time slots:', error);
        return {
            success: false,
            message: error.message || 'Failed to fetch time slots',
            timeSlots: []
        };
    }
};