import axios from 'axios';

/**
 * Fetches the trip list from the API
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID
 * @param {string} businessDate - Business date in YYYYMMDD format
 * @param {string} nextBusinessDate - Next business date in YYYYMMDD format (business date + 1 day)
 * @returns {Promise<Object>} - Trip list response
 */
export const fetchTripList = async (bearerToken, branchId, businessDate, nextBusinessDate) => {
  try {
    const url = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${businessDate}/${nextBusinessDate}`;
    
    console.log('Trip API URL:', url);

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data) {
      return {
        success: true,
        trips: response.data,
      };
    } else {
      return {
        success: false,
        message: 'No trips found.',
        trips: [],
      };
    }
  } catch (error) {
    console.error('Error fetching trip data:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong while fetching trips.',
      trips: [],
    };
  }
};
