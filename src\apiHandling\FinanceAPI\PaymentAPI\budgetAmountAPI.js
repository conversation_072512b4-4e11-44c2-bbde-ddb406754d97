// budgetAmountAPI.js

import axios from 'axios';

/**
 * Fetches budget amount for the selected expense group
 * @param {string} bearerToken - Auth token
 * @param {string} branchId - Branch ID
 * @param {string} expenseGroupId - Expense Group ID
 * @param {string} businessDateCode - Date in YYYYMMDD format
 * @returns {Promise<number>} - Budget amount
 */
export const fetchBudgetAmount = async (bearerToken, branchId, expenseGroupId, businessDateCode) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/getbudget?branchId=${branchId}&expenseGroupId=${expenseGroupId}&businessDateCode=${businessDateCode}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    if (response.data && response.data.length > 0) {
      return response.data[0].BudgetAmount ?? 0;
    }

    return 0;
  } catch (error) {
    console.error('Error fetching budget amount:', error);
    return 0;
  }
};
