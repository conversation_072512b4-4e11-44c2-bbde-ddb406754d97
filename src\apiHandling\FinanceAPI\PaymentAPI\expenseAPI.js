// expenseAPI.js

import axios from 'axios';

/**
 * Fetches expense groups for a branch
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID
 * @returns {Promise<Array>} - List of expense groups
 */
export const fetchExpenseGroups = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchexpensegroup?BranchId=${branchId}&ExpenseGroupID=ALL`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    return response.data.map(group => ({
      ExpenseGroupId: group.ExpenseGroupId,
      ExpenseGroupCode: group.ExpenseGroupCode,
      ExpenseGroupName: group.ExpenseGroupName,
      label: group.ExpenseGroupName,
      value: group.ExpenseGroupId,
    }));
  } catch (error) {
    console.error('Error fetching expense groups:', error);
    return [];
  }
};

/**
 * Fetches expense heads for a given expense group
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID
 * @param {string} expenseGroupId - Selected expense group ID
 * @returns {Promise<Array>} - List of expense heads
 */
export const fetchExpenseHeads = async (bearerToken, branchId, expenseGroupId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchexpensehead?BranchId=${branchId}&ExpenseGroupID=${expenseGroupId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    return response.data.map(head => ({
      ExpenseId: head.ExpenseId,
      ExpenseCode: head.ExpenseCode,
      ExpenseName: head.ExpenseName,
      label: head.ExpenseName,
      value: head.ExpenseId,
    }));
  } catch (error) {
    console.error('Error fetching expense heads:', error);
    return [];
  }
};
