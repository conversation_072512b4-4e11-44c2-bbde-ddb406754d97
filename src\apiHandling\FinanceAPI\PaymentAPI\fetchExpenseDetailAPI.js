// API function to fetch expense details
export const fetchExpenseDetail = async (bearerToken, branchId, dailyExpenseHdrId) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/expensedetail?branchID=${branchId}&DailyexpenseHdrId=${dailyExpenseHdrId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching expense detail:', error);
        throw error;
    }
};
