// paymentMethodsAPI.js

import axios from 'axios';

/**
 * Fetches payment methods from the API
 * @param {string} bearerToken - Bearer auth token
 * @param {string} branchId 
 * @returns {Promise<Array>} - List of payment methods
 */
export const fetchPaymentMethods = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchpayment?BranchID=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    return response.data.map(method => ({
      PaymentGatewayId: method.PaymentGatewayId,
      PaymentGatewayName: method.PaymentGatewayName,
      PaymentMethodId: method.PaymentMethodId,
      label: method.PaymentGatewayName,
      value: method.PaymentGatewayId,
    }));
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return [];
  }
};
