// currentCashBalanceAPI.js
import axios from 'axios';

/**
 * Fetches the current cash balance for a branch and business date.
 * @param {string} bearerToken - The bearer authentication token
 * @param {string} branchId - The branch ID
 * @param {string} businessDateCode - The business date in YYYYMMDD format
 * @returns {Promise<string>} - Current balance as a string
 */
export const fetchCurrentCashBalance = async (bearerToken, branchId, businessDateCode) => {
  try {
    const url = `https://retailuat.abisibg.com/api/v1/getbranchcashbalance?BranchID=${branchId}&BusinessDayCode=${businessDateCode}&PaymentMethod=ALL`;
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
      },
    });

    if (Array.isArray(response.data) && response.data.length > 0 && response.data[0].CurrentBalance !== undefined) {
      return response.data[0].CurrentBalance.toString();
    } else {
      console.error('Invalid response for current balance:', response.data);
      return '0';
    }
  } catch (error) {
    console.error('Error fetching current balance:', error);
    return '0';
  }
};
