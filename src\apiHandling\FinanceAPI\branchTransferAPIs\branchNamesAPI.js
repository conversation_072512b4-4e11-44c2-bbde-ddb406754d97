// branchNamesAPI.js
import axios from 'axios';

/**
 * Fetches branch list from the API
 * @param {string} bearerToken - The bearer authentication token
 * @returns {Promise<Array<{ label: string, value: string, branchId: string, branchName: string }>>}
 */
export const fetchBranchNames = async (bearerToken) => {
  try {
    const response = await axios.get(
      'https://retailuat.abisaio.com:9001/api/Company/GetBranchList/ALL/RTYPE/LAZIZ',
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    if (Array.isArray(response.data)) {
      return response.data.map((branch) => ({
        label: `${branch.branchID}, ${branch.branchName}`,
        value: `${branch.branchID}`, // required by Dropdown component
        branchId: branch.branchID,
        branchName: branch.branchName,
      }));
    } else {
      console.error('Unexpected response format from branch list API:', response.data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching branch list:', error);
    return [];
  }
};
