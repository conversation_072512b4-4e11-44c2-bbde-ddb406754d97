// COMMENTED OUT - Large customer APIs causing performance issues
// Will use BillingScreen customer search APIs instead

/*
import axios from 'axios';

/**
 * Fetches HO customers from the API
 * @param {string} branchId - The branch ID
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Object>} - API response with customer data
 */
/*
export const fetchHOCustomers = async (branchId, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/bycustomersearch?branchID=${branchId}&CustID=ALL&CustomerType=CS&CustomerName=ALL&MobileNo=ALL&ShowNonZeroBalanceOnly=1`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status === 200) {
      return {
        success: true,
        customers: response.data || [],
      };
    } else {
      return {
        success: false,
        message: 'No HO customers found.',
        customers: [],
      };
    }
  } catch (error) {
    console.error('Error fetching HO customers:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong while fetching HO customers.',
      customers: [],
    };
  }
};
*/

/*
/**
 * Fetches Sillak customers from the API
 * @param {string} branchId - The branch ID
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Object>} - API response with customer data
 */
/*
export const fetchSillakCustomers = async (branchId, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/bycustomertype?branchID=${branchId}&CustID=ALL&CustomerType=POS&CustomerName=ALL&MobileNo=ALL&ShowNonZeroBalanceOnly=1`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status === 200) {
      return {
        success: true,
        customers: response.data || [],
      };
    } else {
      return {
        success: false,
        message: 'No Sillak customers found.',
        customers: [],
      };
    }
  } catch (error) {
    console.error('Error fetching Sillak customers:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong while fetching Sillak customers.',
      customers: [],
    };
  }
};
*/
