// fetchBillReportsAPI.js

import axios from 'axios';

/**
 * Fetches bill reports data from the API
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @param {number} posTerminal - POS terminal number
 * @param {string} fromDate - From date in YYYYMMDD format
 * @param {string} toDate - To date in YYYYMMDD format
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of bill report objects
 */
export const fetchBillReports = async (branchId, posTerminal, fromDate, toDate, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/reportbilling`,
      {
        params: {
          option: 1,
          BranchId: branchId,
          POSTerminal: posTerminal,
          FromDate: fromDate,
          ToDate: toDate,
          ChannelID: 'VAN'
        },
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const reports = response.data || [];
    console.log('Fetched bill reports:', reports);
    return reports;
  } catch (error) {
    console.error('Error fetching bill reports:', error);
    throw error;
  }
};

/**
 * Fetches branch details for a given branch ID
 * @param {string} branchId - The branch ID
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Object>} - Branch details object
 */
export const fetchBranchDetails = async (branchId, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchdetails`,
      {
        params: { Branchid: branchId },
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const branchData = response?.data?.[0] || {};
    console.log('Fetched branch details:', branchData);
    return branchData;
  } catch (error) {
    console.error('Error fetching branch details:', error);
    return {};
  }
};
