// FetchDayRateReportAPI.js

import axios from 'axios';

/**
 * Converts a JavaScript Date object to YYYYMMDD format.
 * @param {string} dateStr - Business date in format "DD-MM-YYYY"
 * @returns {string} - Formatted date as "YYYYMMDD"
 */
const formatToYYYYMMDD = (dateStr) => {
  const [day, month, year] = dateStr.split('-');
  return `${year}${month}${day}`;
};

/**
 * Fetches item rates for a given branch and business date
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID
 * @returns {Promise<Array>} - Returns an array of rate objects with rate > 0
 */
export const fetchDayRateReport = async (bearerToken, branchId) => {
  try {
    // Step 1: Fetch business date
    const businessDateRes = await axios.get(
      `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const businessDateRaw = businessDateRes?.data?.[0]?.BusinessDateCode;
    if (!businessDateRaw) throw new Error('Business date not found');
    const businessDateObj = new Date(businessDateRaw);
    const dd = String(businessDateObj.getDate()).padStart(2, '0');
    const mm = String(businessDateObj.getMonth() + 1).padStart(2, '0');
    const yyyy = businessDateObj.getFullYear();
    const formattedYYYYMMDD = `${yyyy}${mm}${dd}`;

    // Step 2: Fetch rate data
    const rateResponse = await axios.get(
      `https://retailuat.abisaio.com:9001/api/BranchChannelRate/00001/${branchId}/${formattedYYYYMMDD}/VAN`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const fullData = rateResponse.data || [];

    // ✅ Filter only items with rate > 0
    const filteredData = fullData.filter((item) => item.rate > 0);

    return filteredData;
  } catch (error) {
    console.error('Error fetching day rate report:', error);
    return [];
  }
};
