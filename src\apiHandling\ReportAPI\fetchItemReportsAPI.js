// fetchItemReportsAPI.js

import axios from 'axios';

/**
 * Fetches item reports data from the API
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @param {number} posTerminal - POS terminal number
 * @param {string} fromDate - From date in YYYYMMDD format
 * @param {string} toDate - To date in YYYYMMDD format
 * @param {string} channelId - Channel ID (e.g. "VAN")
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of item report objects
 */
export const fetchItemReports = async (branchId, posTerminal, fromDate, toDate, channelId, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/vanbillitemreport`,
      {
        params: {
          BranchId: branchId,
          POSTerminal: posTerminal,
          FromDate: fromDate,
          ToDate: toDate,
          ChannelID: channelId
        },
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const reports = response.data || [];
    console.log('Fetched item reports:', reports);
    return reports;
  } catch (error) {
    console.error('Error fetching item reports:', error);
    throw error;
  }
};
