import axios from 'axios';

/**
 * Fetches credit balance data from the API
 * @param {string} branchId - The branch ID
 * @param {string} customerId - The customer ID
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of credit balance objects
 */
export const fetchCreditBalance = async (branchId, customerId, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisaio.com:9001/api/Ledger/GetCreditBalance/${branchId}/${customerId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    console.log('Credit balance API response:', response.data);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching credit balance:', error);
    throw error;
  }
};

/**
 * Fetches credit ledger data from the API
 * @param {string} branchId - The branch ID
 * @param {string} customerId - The customer ID
 * @param {string} fromDate - From date in YYYYMMDD format
 * @param {string} toDate - To date in YYYYMMDD format
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of credit ledger objects
 */
export const fetchCreditLedger = async (branchId, customerId, fromDate, toDate, bearerToken) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisaio.com:9001/api/Ledger/Get/${branchId}/${customerId}/${fromDate}/${toDate}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    console.log('Credit ledger API response:', response.data);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching credit ledger:', error);
    throw error;
  }
};

/**
 * Format date to YYYYMMDD format
 * @param {Date} date - The date object
 * @returns {string} - Formatted date string
 */
export const formatDateToYYYYMMDD = (date) => {
  if (!date) return '';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};
