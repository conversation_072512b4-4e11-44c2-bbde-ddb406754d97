// fetchStockReportAPI.js

import axios from 'axios';

/**
 * Fetches stock report data from the API and filters it
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID (e.g. "L101")
 * @returns {Promise<Array>} - Filtered array of stock report items
 */
export const fetchStockReport = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/fetchstock?BranchID=${branchId}&BinId=OKBIN&ItemStatusID=OK&ItemCategoryId=ALL&Channel=ALL&ItemID=ALL&BatchNumber=ALL`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const fullData = response.data || [];

    // ✅ Filter only those items with StockQty > 0 and StockAltQty >= 0
    const filteredData = fullData.filter(
      (item) => item.StockQty > 0 && item.StockAltQty >= 0
    );

    return filteredData;
  } catch (error) {
    console.error('Error fetching stock report:', error);
    return [];
  }
};
