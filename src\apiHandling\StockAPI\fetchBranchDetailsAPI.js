// fetchBranchDetailsAPI.js

import axios from 'axios';

/**
 * Fetches branch details data from the API.
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @returns {Promise<Array>} - Array of branch detail objects
 */
export const fetchBranchDetails = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchdetails?Branchid=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const branchDetails = response.data || [];

    console.log('Fetched branch details:', branchDetails); // Log for debugging

    return branchDetails;
  } catch (error) {
    console.error('Error fetching branch details:', error);
    return [];
  }
};
