// fetchBranchListAPI.js

import axios from 'axios';

/**
 * Fetches branch list data from the API.
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of branch objects
 */
export const fetchBranchList = async (bearerToken) => {
  try {
    const response = await axios.get(
      'https://retailuat.abisaio.com:9001/api/Company/GetBranchList/ALL/RTYPE/LAZIZ',
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const branches = response.data || [];

    console.log('Fetched branch list:', branches); // Log for debugging

    return branches;
  } catch (error) {
    console.error('Error fetching branch list:', error);
    return [];
  }
};
