// fetchEmployeeAPI.js

import axios from 'axios';

/**
 * Fetches employee list data from the API.
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @returns {Promise<Array>} - Array of employee objects
 */
export const fetchEmployeeList = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/contractemplist?BranchID=${branchId}&CompanyCode=ALL&vehicleName=ALL`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const employees = response.data || [];

    console.log('Fetched employee list:', employees); // Log for debugging

    return employees;
  } catch (error) {
    console.error('Error fetching employee list:', error);
    return [];
  }
};
