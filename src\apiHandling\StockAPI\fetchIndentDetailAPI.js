// API function to fetch indent details
export const fetchIndentDetail = async (bearerToken, branchId, indentId, withBranchType) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/indentdetail?BranchId=${branchId}&IndentID=${indentId}&WithBranchType=${withBranchType}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching indent detail:', error);
        throw error;
    }
};
