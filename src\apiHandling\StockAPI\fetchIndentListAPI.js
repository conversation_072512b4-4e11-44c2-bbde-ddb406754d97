// API function to fetch indent list
export const fetchIndentList = async (bearerToken, branchId, fromDate, toDate) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/indentlist?BranchId=${branchId}&FromDate=${fromDate}&ToDate=${toDate}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching indent list:', error);
        throw error;
    }
};
