// fetchItemListAPI.js

import axios from 'axios';

/**
 * Fetches item list data from the API.
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @returns {Promise<Array>} - Array of item objects
 */
export const fetchItemList = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/itemlist?branchId=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const items = response.data || [];

    //console.log('Fetched item list:', items); // ✅ Log full raw output

    return items;
  } catch (error) {
    console.error('Error fetching item list:', error);
    return [];
  }
};
