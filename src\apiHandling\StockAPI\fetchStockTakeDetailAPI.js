// API function to fetch stock take details
export const fetchStockTakeDetail = async (bearerToken, branchId, stockAdjustId) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/stockadjustmentdetail?BranchId=${branchId}&StockAdjustId=${stockAdjustId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching stock take detail:', error);
        throw error;
    }
};
