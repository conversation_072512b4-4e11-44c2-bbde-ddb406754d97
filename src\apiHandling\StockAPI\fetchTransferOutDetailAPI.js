// API function to fetch transfer out details
export const fetchTransferOutDetail = async (bearerToken, despatchBranchId, trOutId) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/intransitdetail?DespatchBranchId=${despatchBranchId}&TrOutID=${trOutId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching transfer out detail:', error);
        throw error;
    }
};
