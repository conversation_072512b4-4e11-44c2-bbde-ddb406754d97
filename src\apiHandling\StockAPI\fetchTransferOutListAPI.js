// API function to fetch transfer out list
export const fetchTransferOutList = async (bearerToken, inwardBranchId, fromDate, toDate) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/intransitlist?InwardBranchId=${inwardBranchId}&TransTypeID=TROUT&Stage=CL&FromDate=${fromDate}&ToDate=${toDate}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching transfer out list:', error);
        throw error;
    }
};
