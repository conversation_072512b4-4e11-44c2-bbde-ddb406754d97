// fetchVehicleAPI.js

import axios from 'axios';

/**
 * Fetches vehicle list data from the API.
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID (e.g. "L102")
 * @returns {Promise<Array>} - Array of vehicle objects
 */
export const fetchVehicleList = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/fetchvehicle?BranchID=${branchId}&companyCode=ALL`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const vehicles = response.data || [];

    console.log('Fetched vehicle list:', vehicles); // Log for debugging

    return vehicles;
  } catch (error) {
    console.error('Error fetching vehicle list:', error);
    return [];
  }
};
