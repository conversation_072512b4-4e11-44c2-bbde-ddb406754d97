// API function to fetch wastage details
export const fetchWastageDetail = async (bearerToken, branchId, wastageId) => {
    try {
        const response = await fetch(
            `https://retailuat.abisibg.com/api/v1/wastagedetail?BranchId=${branchId}&WastageId=${wastageId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${bearerToken}`,
                    'Content-Type': 'application/json',
                },
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching wastage detail:', error);
        throw error;
    }
};
