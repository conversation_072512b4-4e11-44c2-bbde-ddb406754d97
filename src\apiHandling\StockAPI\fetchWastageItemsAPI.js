// fetchWastageItemsAPI.js

import axios from 'axios';

/**
 * Fetches wastage items data based on wastage reason ID.
 * @param {string} bearerToken - The authentication token
 * @param {string} wastageReasonId - The wastage reason ID (e.g., "ACCDT")
 * @returns {Promise<Array>} - Array of wastage item objects
 */
export const fetchWastageItems = async (bearerToken, wastageReasonId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/wastage?WastageReasonId=${wastageReasonId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const wastageItems = response.data || [];

    console.log('Fetched wastage items for reason ID:', wastageReasonId, wastageItems);

    return wastageItems;
  } catch (error) {
    console.error('Error fetching wastage items:', error);
    return [];
  }
};
