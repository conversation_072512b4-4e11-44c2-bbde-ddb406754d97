// fetchWastageReasonListAPI.js

import axios from 'axios';

/**
 * Fetches wastage reason list data from the API.
 * @param {string} bearerToken - The authentication token
 * @returns {Promise<Array>} - Array of wastage reason objects
 */
export const fetchWastageReasonList = async (bearerToken) => {
  try {
    const response = await axios.get(
      'https://retailuat.abisibg.com/api/v1/wastagereasonlist',
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const wastageReasons = response.data || [];

    console.log('Fetched wastage reason list:', wastageReasons);

    return wastageReasons;
  } catch (error) {
    console.error('Error fetching wastage reason list:', error);
    return [];
  }
};
