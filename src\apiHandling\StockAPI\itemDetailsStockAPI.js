// itemDetailStockAPI.js

import axios from 'axios';

/**
 * Fetches detailed item info by PartNumber (ItemID).
 * @param {string} bearerToken
 * @param {string} itemId
 * @returns {Promise<Object|null>}
 */
export const fetchItemDetail = async (bearerToken, itemId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/itemdetail?PartNumber=${itemId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    const details = response.data?.[0];
    console.log('Fetched item detail:', details);
    return details || null;
  } catch (error) {
    console.error('Error fetching item detail:', error);
    return null;
  }
};

/**
 * Fetches stock data for a given item and branch.
 * @param {string} bearerToken
 * @param {string} branchId
 * @param {string} itemId
 * @returns {Promise<Array>} - array of stock entries
 */
export const fetchItemStock = async (bearerToken, branchId, itemId) => {
  try {
    const url = `https://retailuat.abisibg.com/api/v1/fetchstock?BranchID=${branchId}&BinId=OKBIN&ItemStatusID=OK&ItemCategoryId=ALL&Channel=ALL&ItemID=${itemId}&BatchNumber=ALL`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
      },
    });

    console.log('Fetched item stock:', response.data);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching item stock:', error);
    return [];
  }
};
