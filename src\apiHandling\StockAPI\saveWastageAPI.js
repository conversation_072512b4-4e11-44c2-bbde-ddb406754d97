import axios from 'axios';

/**
 * Fetch business date for the given branch.
 * @param {string} bearerToken
 * @param {string} branchId
 * @returns {Promise<string>} ISO string of business date
 */
export const fetchBusinessDate = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );
    return response.data?.[0]?.BusinessDateCode || new Date().toISOString();
  } catch (error) {
    console.error('❌ Error fetching business date:', error);
    return new Date().toISOString();
  }
};

/**
 * Save wastage data.
 * @param {string} bearerToken
 * @param {object} payload
 * @returns {Promise<object>} API response
 */
export const saveWastage = async (bearerToken, payload) => {
  try {
    console.log('📤 Sending Payload to /api/Wastage2:\n', JSON.stringify(payload, null, 2));

    const response = await axios.post(
      'https://retailuat.abisaio.com:9001/api/Wastage2',
      payload,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ API Response:\n', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Error saving wastage data:', error);
    throw error;
  }
};
