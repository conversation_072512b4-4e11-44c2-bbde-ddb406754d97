import axios from 'axios';
import { local_signage } from '../../const';

/**
 * Fetches the current business day from the API
 * @param {string} bearerToken - The authentication token
 * @param {string} branchId - The branch ID
 * @returns {Promise<string>} - Formatted business date (DD-MM-YYYY)
 */
export const fetchBusinessDay = async (bearerToken, branchId) => {
  try {
    const response = await axios.get(
      `${local_signage}/api/v1/currentbusinessday?BranchId=${branchId}`,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    // Check if response data exists and has the expected format
    if (response.data && response.data.length > 0 && response.data[0].BusinessDateCode) {
      // Parse the date string from the API
      const businessDateString = response.data[0].BusinessDateCode;
      const businessDate = new Date(businessDateString);
      
      // Format the date as DD-MM-YYYY
      const day = String(businessDate.getDate()).padStart(2, '0');
      const month = String(businessDate.getMonth() + 1).padStart(2, '0'); // Months are 0-based
      const year = businessDate.getFullYear();
      
      return `${day}-${month}-${year}`;
    } else {
      console.error('Invalid response format from business day API:', response.data);
      return 'Date not available';
    }
  } catch (error) {
    console.error('Error fetching business day:', error);
    return 'Date not available';
  }
};