import React, { useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ActivityIndicator,
    Animated,
} from 'react-native';

const CustomLoader = ({ 
    isVisible = true, 
    message = 'Loading...', 
    size = 'large',
    color = '#02096A',
    backgroundColor = 'rgba(255, 255, 255, 0.9)',
    containerStyle = {},
    textStyle = {},
    showAnimation = true
}) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        if (isVisible && showAnimation) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    tension: 50,
                    friction: 7,
                    useNativeDriver: true,
                }),
            ]).start();
        } else if (!isVisible) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 0.8,
                    duration: 200,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [isVisible, fadeAnim, scaleAnim, showAnimation]);

    if (!isVisible) return null;

    return (
        <Animated.View 
            style={[
                styles.loaderContainer, 
                { backgroundColor },
                { opacity: showAnimation ? fadeAnim : 1 },
                containerStyle
            ]}
        >
            <Animated.View 
                style={[
                    styles.loaderContent,
                    { 
                        transform: showAnimation 
                            ? [{ scale: scaleAnim }]
                            : [{ scale: 1 }]
                    }
                ]}
            >
                {/* Animated pulse background */}
                <View style={styles.pulseContainer}>
                    <View style={[styles.pulseRing, styles.pulse1]} />
                    <View style={[styles.pulseRing, styles.pulse2]} />
                    <View style={[styles.pulseRing, styles.pulse3]} />
                </View>
                
                <ActivityIndicator 
                    size={size} 
                    color={color} 
                    style={styles.spinner}
                />
                <Text style={[styles.loaderText, textStyle]}>
                    {message}
                </Text>
                
                {/* Loading dots animation */}
                <View style={styles.dotsContainer}>
                    <LoadingDot delay={0} />
                    <LoadingDot delay={200} />
                    <LoadingDot delay={400} />
                </View>
            </Animated.View>
        </Animated.View>
    );
};

const LoadingDot = ({ delay = 0 }) => {
    const opacity = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        const animate = () => {
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 600,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0.3,
                    duration: 600,
                    useNativeDriver: true,
                }),
            ]).start(() => animate());
        };

        const timer = setTimeout(animate, delay);
        return () => clearTimeout(timer);
    }, [opacity, delay]);

    return (
        <Animated.View 
            style={[
                styles.dot,
                { opacity }
            ]} 
        />
    );
};

const styles = StyleSheet.create({
    loaderContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    loaderContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 32,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        borderWidth: 2,
        borderColor: '#FDC500',
        minWidth: 140,
        minHeight: 140,
        position: 'relative',
    },
    pulseContainer: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        marginTop: -40,
        marginLeft: -40,
        width: 80,
        height: 80,
    },
    pulseRing: {
        position: 'absolute',
        borderWidth: 2,
        borderColor: '#FDC500',
        borderRadius: 40,
        opacity: 0.6,
    },
    pulse1: {
        width: 80,
        height: 80,
        animation: 'pulse 2s infinite',
    },
    pulse2: {
        width: 60,
        height: 60,
        top: 10,
        left: 10,
        animation: 'pulse 2s infinite 0.5s',
    },
    pulse3: {
        width: 40,
        height: 40,
        top: 20,
        left: 20,
        animation: 'pulse 2s infinite 1s',
    },
    spinner: {
        marginBottom: 16,
        zIndex: 10,
    },
    loaderText: {
        fontSize: 16,
        color: '#02096A',
        fontFamily: 'Poppins',
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 12,
        zIndex: 10,
    },
    dotsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 10,
    },
    dot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#02096A',
        marginHorizontal: 3,
    },
});

export default CustomLoader;