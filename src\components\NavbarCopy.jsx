import React, { useState, useEffect } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Modal,
    TouchableWithoutFeedback,
} from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBusinessDay } from '../apiHandling/businessDayAPI';

const Navbar = () => {
    const [currentTime, setCurrentTime] = useState('');
    const [selectedMenu, setSelectedMenu] = useState('');
    const [selectedSubOption, setSelectedSubOption] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [showExitModal, setShowExitModal] = useState(false);
    const [expandedStates, setExpandedStates] = useState({});
    const [dropdownPosition] = useState({ top: 100, left: 20 });
    const [businessDate, setBusinessDate] = useState('Loading...');
    const [branchId, setBranchId] = useState(''); 
    const [branchName, setBranchName] = useState(''); 
    const [currentDropdownType, setCurrentDropdownType] = useState('');

    const navigation = useNavigation();
    const route = useRoute();

    const loadBranchInfo = async () => {
        try {
            const storedBranch = await AsyncStorage.getItem('selectedBranch');
            if (storedBranch) {
                const parsedBranch = JSON.parse(storedBranch);
                setBranchId(parsedBranch.BranchId || 'N/A');
                setBranchName(parsedBranch.BranchName || 'N/A');
            } else {
                setBranchId('N/A');
                setBranchName('N/A');
            }
        } catch (error) {
            console.error('Error loading branch info:', error);
            setBranchId('N/A');
            setBranchName('N/A');
        }
    };


    const getBusinessDay = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            if (bearerToken && selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                const branchId = branch.BranchId;

                const formattedDate = await fetchBusinessDay(bearerToken, branchId);
                setBusinessDate(formattedDate);
            } else {
                console.error('Missing token or branch data');
                setBusinessDate('Date not available');
            }
        } catch (error) {
            console.error('Error in getBusinessDay:', error);
            setBusinessDate(new Date().toLocaleDateString());
        }
    };

    const menuOptions = [
        'Billing',
        'Stock',
        'Logistics',
        'Finance',
        'HR',
        'Utils',
        'Reports',
        'Exit',
    ];

    const stockDropdownOptions = [
        { title: 'Receiving', subOptions: ['Receiving', 'Wheat', 'Sugar'] },
        { title: 'Finished Goods', subOptions: ['Basmati Rice', 'Bread', 'Cookies'] },
        { title: 'Packaging', subOptions: ['Bags', 'Labels'] },
    ];

    const logisticsDropdownOptions = [
        { title: 'Trip', subOptions: ['Trip'] },
        { title: 'Trip Plan', subOptions: ['Trip Plan'] },
        { title: 'Delivery', subOptions: ['Delivery'] },
        { title: 'Expenses', subOptions: ['Expenses'] },
        { title: 'Dayend', subOptions: ['Dayend'] },

    ];

    const allStockSubOptions = stockDropdownOptions.flatMap(opt => opt.subOptions);
    const allLogisticsSubOptions = logisticsDropdownOptions.flatMap(opt => opt.subOptions);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date().toLocaleTimeString());
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        loadBranchInfo().then(getBusinessDay);
    }, []);

    useEffect(() => {
        if (route.name) {
            if (allStockSubOptions.includes(route.name)) {
                setSelectedMenu('Stock');
                setSelectedSubOption(route.name);
            } else if (allLogisticsSubOptions.includes(route.name)) {
                setSelectedMenu('Logistics');
                setSelectedSubOption(route.name);
            } else {
                setSelectedMenu(route.name);
                setSelectedSubOption('');
            }
        }
    }, [route.name]);

    const handleMenuPress = (option) => {
        if (option === 'Exit') {
            setShowExitModal(true);
        } else if (option === 'Stock') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setCurrentDropdownType('Stock');
            setShowDropdown(true);
        } else if (option === 'Logistics') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setCurrentDropdownType('Logistics');
            setShowDropdown(true);
        } else {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setShowDropdown(false);
            navigation.navigate(option);
        }
    };

    const handleOptionPress = (option) => {
        setExpandedStates(prev => ({
            ...prev,
            [option.title]: !prev[option.title],
        }));
    };

    const handleSubOptionPress = (sub) => {
        if (currentDropdownType === 'Stock') {
            setSelectedMenu('Stock');
        } else if (currentDropdownType === 'Logistics') {
            setSelectedMenu('Logistics');
        }
        setSelectedSubOption(sub);
        setShowDropdown(false);
        navigation.navigate(sub);
    };

    const getCurrentDropdownOptions = () => {
        if (currentDropdownType === 'Stock') {
            return stockDropdownOptions;
        } else if (currentDropdownType === 'Logistics') {
            return logisticsDropdownOptions;
        }
        return [];
    };

    return (
        <View style={styles.container}>
            <View style={styles.appBar}>
                <View style={styles.topRow}>
                    <Text style={styles.topText}>{businessDate}</Text>
                    <Text style={styles.topText}>{currentTime}</Text>
                    <Text style={styles.topText}>{`${branchId}, ${branchName}`}</Text>
                </View>

                <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.menuRow}>
                    {menuOptions.map(option => {
                        const isSelected = selectedMenu === option;
                        const isExit = option === 'Exit';
                        return (
                            <TouchableOpacity
                                key={option}
                                onPress={() => handleMenuPress(option)}
                                style={[styles.menuButton, {
                                    backgroundColor: isSelected
                                        ? isExit ? '#FF3333' : '#FDC500'
                                        : isExit ? '#FF3333' : '#02096A'
                                }]}
                            >
                                <Text style={[styles.menuText, { color: isSelected ? 'black' : 'white' }]}>{option}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </ScrollView>
            </View>

            {/* Stock/Logistics Dropdown */}
            <Modal transparent visible={showDropdown} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={[styles.dropdownContainer, dropdownPosition]}>
                                <ScrollView>
                                    {getCurrentDropdownOptions().map(option => (
                                        <View key={option.title}>
                                            <TouchableOpacity onPress={() => handleOptionPress(option)} style={styles.dropdownItem}>
                                                <Text style={styles.dropdownText}>{option.title}</Text>
                                                <Text style={styles.expandArrow}>{expandedStates[option.title] ? '▲' : '▼'}</Text>
                                            </TouchableOpacity>
                                            {expandedStates[option.title] && option.subOptions.map(sub => (
                                                <TouchableOpacity key={sub} onPress={() => handleSubOptionPress(sub)} style={styles.subOption}>
                                                    <Text style={styles.subOptionText}>{sub}</Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    ))}
                                </ScrollView>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>

            {/* Exit Modal */}
            <Modal transparent visible={showExitModal} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowExitModal(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={styles.exitModalContainer}>
                                <Text style={styles.exitModalText}>Are you sure you want to exit?</Text>
                                <View style={styles.modalButtonRow}>
                                    <TouchableOpacity style={styles.modalButton} onPress={() => {
                                        setShowExitModal(false);
                                        navigation.navigate('Login');
                                    }}>
                                        <Text style={styles.modalButtonText}>Yes</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[styles.modalButton, { backgroundColor: '#aaa' }]} onPress={() => setShowExitModal(false)}>
                                        <Text style={styles.modalButtonText}>No</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 0,
    },
    appBar: {
        backgroundColor: '#02096A',
        height: 120,
        paddingTop: 20,
        paddingBottom: 0,
    },
    topRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        marginBottom: 10,
    },
    topText: { color: 'white', fontWeight: 'bold', fontSize: 15 },
    menuRow: { paddingHorizontal: 5, alignItems: 'center' },
    menuButton: { paddingHorizontal: 10, paddingVertical: 5, marginHorizontal: 5, borderRadius: 5 },
    menuText: { fontSize: 25, fontWeight: 'bold' },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.73)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dropdownContainer: {
        position: 'absolute',
        backgroundColor: '#FDC500',
        marginTop: 10,
        padding: 10,
        minWidth: 250,
        borderRadius: 8,
        elevation: 6,
        maxHeight: 400,
    },
    dropdownItem: {
        paddingVertical: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    dropdownText: { fontSize: 16, fontWeight: '600' },
    expandArrow: { fontSize: 16 },
    subOption: { paddingLeft: 20, paddingVertical: 6 },
    subOptionText: { fontSize: 14 },
    exitModalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 8,
        elevation: 10,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -150 }, { translateY: -100 }],
        width: 300,
        height: 200,
    },
    exitModalText: { fontSize: 18, marginBottom: 20, textAlign: 'center' },
    modalButtonRow: { flexDirection: 'row', justifyContent: 'space-around', width: '100%' },
    modalButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        backgroundColor: '#FDC500',
        borderRadius: 5,
        marginHorizontal: 10,
    },
    modalButtonText: { fontSize: 16, fontWeight: 'bold' },
});

export default Navbar;