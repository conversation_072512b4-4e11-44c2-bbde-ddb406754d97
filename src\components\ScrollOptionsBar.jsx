import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

/**
 * ScrollOptionsBar - A reusable component for action buttons (New, Save, View, Cancel)
 * 
 * @param {Object} props
 * @param {string} props.title - Title displayed on the left side (e.g., "Trips")
 * @param {string} props.selectedOption - Currently selected option
 * @param {Function} props.onOptionPress - Function to handle option press, receives option name as parameter
 * @param {Array} props.options - Optional array of options to display (defaults to ["New", "Save", "View", "Cancel"])
 * @param {boolean} props.showBackArrow - Whether to show back arrow (not implemented yet, for future expansion)
 */
const ScrollOptionsBar = ({
  title,
  selectedOption,
  onOptionPress,
  options = ["New", "Save", "View", "Cancel"],
  showBackArrow = false
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        {/* Left side - Title */}
        <TouchableOpacity style={styles.titleContainer}>
          <Text style={styles.screenTitle}>{title}</Text>
        </TouchableOpacity>

        {/* Right side - Action Buttons */}
        <View style={styles.buttonsContainer}>
          {options.map((option, index) => {
            const isSelected = selectedOption === option;
            let buttonStyle = [styles.button];
            
            if (option === 'Cancel') {
              buttonStyle.push({
                backgroundColor: isSelected ? '#FE0000' : '#FF3333',
              });
            } else if (option === 'Save') {
              buttonStyle.push({
                backgroundColor: isSelected ? '#02720F' : '#02A515',
              });
            } else {
              buttonStyle.push({
                backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
              });
            }

            return (
              <View style={styles.buttonWrapper} key={index}>
                <TouchableOpacity
                  style={buttonStyle}
                  onPress={() => onOptionPress(option)}
                >
                  <Text style={[
                    styles.buttonText,
                    { color: isSelected || option === 'Cancel' || option === 'Save' ? 'white' : 'black' },
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#02096A',
  },
  buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default ScrollOptionsBar;