import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { local_signage, local_url } from '../../const';
import { PERMISSIONS, request, RESULTS, check } from 'react-native-permissions';

const { width, height } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [employeeId, setEmployeeId] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [branchModalVisible, setBranchModalVisible] = useState(false);
  const [branches, setBranches] = useState([]);
  const [authToken, setAuthToken] = useState('');
  const [bluetoothPermissionModalVisible, setBluetoothPermissionModalVisible] = useState(false);
  const [bluetoothPermissionGranted, setBluetoothPermissionGranted] = useState(false);

  const validate = () => {
    const newErrors = {};
    if (!employeeId.trim()) {
      newErrors.employeeId = 'Employee ID is required';
    } else if (!/^\d{8}$/.test(employeeId.trim())) {
      newErrors.employeeId = 'Employee ID must be exactly 8 digits';
    }
    if (!password.trim()) {
      newErrors.password = 'Password is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const fetchBranches = async (empId, token) => {
    try {
      const res = await axios.get(
        `${local_signage}/api/v1/branchaccess?EmpId=00${empId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      if (res.data && res.data.length > 0) {
        setBranches(res.data);
        setBranchModalVisible(true);
      } else {
        Alert.alert('No Branch Found', 'No branch access available for this user.');
      }
    } catch (err) {
      console.error(err);
      Alert.alert('Error', 'Unable to fetch branches');
    }
  };

  const handleLogin = async () => {
    if (!validate()) return;
    setLoading(true);

    try {
      const response = await axios.post(
        `${local_url}/api/Login/Post`,
        {
          email: 'email',
          userId: employeeId,
          password: password,
        }
      );

      const { token, user } = response.data;

      if (!token) {
        throw new Error('Invalid credentials');
      }

      setAuthToken(token);

      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('userData', JSON.stringify(user));


      await fetchBranches(employeeId, token);
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      Alert.alert(
        'Login Failed',
        error.response?.data?.message || 'Invalid credentials or network issue'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBranchSelect = async (branch) => {
    setBranchModalVisible(false);
    await AsyncStorage.setItem('selectedBranch', JSON.stringify(branch));
    navigation.replace('Main');
  };

  // Bluetooth permission functions
  const checkBluetoothPermissions = async () => {
    try {
      const bluetoothPermissions = Platform.OS === 'android'
        ? [
            PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
            PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
            PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          ]
        : [PERMISSIONS.IOS.BLUETOOTH_PERIPHERAL];

      const permissionResults = await Promise.all(
        bluetoothPermissions.map(permission => check(permission))
      );

      const allGranted = permissionResults.every(result => result === RESULTS.GRANTED);
      setBluetoothPermissionGranted(allGranted);

      if (!allGranted) {
        setBluetoothPermissionModalVisible(true);
      }
    } catch (error) {
      console.error('Error checking Bluetooth permissions:', error);
    }
  };

  const requestBluetoothPermissions = async () => {
    try {
      const bluetoothPermissions = Platform.OS === 'android'
        ? [
            PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
            PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
            PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          ]
        : [PERMISSIONS.IOS.BLUETOOTH_PERIPHERAL];

      const permissionResults = await Promise.all(
        bluetoothPermissions.map(permission => request(permission))
      );

      const allGranted = permissionResults.every(result => result === RESULTS.GRANTED);
      setBluetoothPermissionGranted(allGranted);
      setBluetoothPermissionModalVisible(false);

      if (!allGranted) {
        Alert.alert(
          'Bluetooth Permissions Required',
          'Bluetooth permissions are required for printer functionality. You can enable them later in app settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting Bluetooth permissions:', error);
      setBluetoothPermissionModalVisible(false);
    }
  };

  const skipBluetoothPermissions = () => {
    setBluetoothPermissionModalVisible(false);
    Alert.alert(
      'Bluetooth Permissions Skipped',
      'You can enable Bluetooth permissions later in app settings for printer functionality.',
      [{ text: 'OK' }]
    );
  };

  // Check Bluetooth permissions on component mount
  useEffect(() => {
    checkBluetoothPermissions();
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: '#ffffff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
          <Image source={require('../assets/logo/top-art.png')} style={styles.topBackground} />
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/logo/abis-logo.png')}
              style={[styles.logo, { width: width * 0.6, height: height * 0.2 }]}
              resizeMode="contain"
            />
          </View>

          <View style={styles.form}>
            <Text style={styles.label}>Employee ID</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your employee ID"
              placeholderTextColor="#000000"
              value={employeeId}
              onChangeText={text => {
                setEmployeeId(text);
                setErrors({ ...errors, employeeId: '' });
              }}
              autoCapitalize="none"
              keyboardType="numeric"
              returnKeyType="next"
              maxLength={10}
            />
            {errors.employeeId && <Text style={styles.error}>{errors.employeeId}</Text>}

            <Text style={styles.label}>Password</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="••••••••"
                placeholderTextColor="#000000"
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={text => {
                  setPassword(text);
                  setErrors({ ...errors, password: '' });
                }}
                returnKeyType="done"
                onSubmitEditing={handleLogin}
              />
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                <Icon
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={24}
                  color="#555"
                  style={{ marginLeft: 10 }}
                />
              </TouchableOpacity>
            </View>
            {errors.password && <Text style={styles.error}>{errors.password}</Text>}



            <TouchableOpacity
              style={styles.loginBtn}
              onPress={handleLogin}
              activeOpacity={0.8}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.loginText}>LOGIN</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Branch Modal */}
          <Modal visible={branchModalVisible} animationType="slide" transparent>
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Branch</Text>
                <FlatList
                  data={branches}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.branchItem}
                      onPress={() => handleBranchSelect(item)}
                    >
                      <Text style={styles.branchText}>{item?.BranchName ?? 'Unnamed Branch'}</Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </Modal>

          {/* Bluetooth Permission Modal */}
          <Modal visible={bluetoothPermissionModalVisible} animationType="fade" transparent>
            <View style={styles.modalContainer}>
              <View style={styles.permissionModalContent}>
                <View style={styles.permissionIconContainer}>
                  <Icon name="bluetooth" size={60} color="#007bff" />
                </View>
                <Text style={styles.permissionModalTitle}>Bluetooth Permission Required</Text>
                <Text style={styles.permissionModalText}>
                  This app needs Bluetooth access to connect to thermal printers for printing receipts.
                  This will enable you to print bills and receipts directly from the app.
                </Text>
                <View style={styles.permissionButtonContainer}>
                  <TouchableOpacity
                    style={styles.permissionSkipButton}
                    onPress={skipBluetoothPermissions}
                  >
                    <Text style={styles.permissionSkipButtonText}>Skip</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.permissionAllowButton}
                    onPress={requestBluetoothPermissions}
                  >
                    <Text style={styles.permissionAllowButtonText}>Allow</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    paddingTop: height * 0.15,
    paddingBottom: 40,
  },
  topBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: height * 0.6,
    resizeMode: 'cover',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: height * 0.04,
    marginTop: 300,
  },
  logo: {
    resizeMode: 'contain',
  },
  form: {
    width: '85%',
    paddingHorizontal: 10,
    paddingVertical: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 6,
    marginTop: 6,
  },
  input: {
    borderWidth: 1,
    borderColor: '#dddddd',
    padding: 14,
    marginBottom: 15,
    backgroundColor: '#f9f9f9',
    fontSize: 15,
    paddingHorizontal: 20,
    color: '#000000',
    borderRadius: 8,
  },
  passwordInput: {
    flex: 1,
    fontSize: 15,
    paddingVertical: 14,
    backgroundColor: '#f9f9f9',
    color: '#000000',
    borderRadius: 8,
  },
  error: {
    color: 'red',
    fontSize: 13,
    marginTop: -10,
    marginBottom: 10,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#dddddd',
    backgroundColor: '#f9f9f9',
    paddingHorizontal: 20,
    marginBottom: 15,
    borderRadius: 8,
  },

  loginBtn: {
    backgroundColor: '#002d6b',
    paddingVertical: 14,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
  },
  loginText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#00000099',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '50%',
    maxHeight: height * 0.5,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  branchItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  branchText: {
    fontSize: 16,
  },
  permissionModalContent: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  permissionIconContainer: {
    marginBottom: 16,
  },
  permissionModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionModalText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  permissionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  permissionSkipButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  permissionSkipButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  permissionAllowButton: {
    flex: 1,
    backgroundColor: '#007bff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  permissionAllowButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LoginScreen;
