import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import Icon from 'react-native-vector-icons/Ionicons';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { local_signage, local_url } from '../../const';

const { width, height } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [employeeId, setEmployeeId] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [branchModalVisible, setBranchModalVisible] = useState(false);
  const [branches, setBranches] = useState([]);
  const [authToken, setAuthToken] = useState('');

  const validate = () => {
    const newErrors = {};
    if (!employeeId.trim()) {
      newErrors.employeeId = 'Employee ID is required';
    } else if (!/^\d{10}$/.test(employeeId.trim())) {
      newErrors.employeeId = 'Employee ID must be exactly 10 digits';
    }
    if (!password.trim()) {
      newErrors.password = 'Password is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const fetchBranches = async (empId, token) => {
    try {
      const res = await axios.get(
        `${local_signage}/api/v1/branchaccess?EmpId=${empId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (res.data && res.data.length > 0) {
        setBranches(res.data);
        setBranchModalVisible(true);
      } else {
        Alert.alert('No Branch Found', 'No branch access available for this user.');
      }
    } catch (err) {
      console.error(err);
      Alert.alert('Error', 'Unable to fetch branches');
    }
  };

  const handleLogin = async () => {
    if (!validate()) return;
    setLoading(true);

    try {
      const response = await axios.post(
        `${local_url}/api/Login/Post`,
        {
          email: 'email',
          userId: employeeId,
          password: password,
        }
      );

      const { token, user } = response.data;

      if (!token) {
        throw new Error('Token not received');
      }

      setAuthToken(token);

      if (rememberMe) {
        await AsyncStorage.setItem('authToken', token);
        await AsyncStorage.setItem('userData', JSON.stringify(user));
      }

      await fetchBranches(employeeId, token);
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      Alert.alert(
        'Login Failed',
        error.response?.data?.message || 'Invalid credentials or network issue'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBranchSelect = async (branch) => {
    setBranchModalVisible(false);
    await AsyncStorage.setItem('selectedBranch', JSON.stringify(branch));
    navigation.replace('Main');
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#ffffff' }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
          <Image source={require('../assets/logo/top-art.png')} style={styles.topBackground} />
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/logo/abis-logo.png')}
              style={[styles.logo, { width: width * 0.6, height: height * 0.2 }]}
              resizeMode="contain"
            />
          </View>

          <View style={styles.form}>
            <Text style={styles.label}>Employee ID</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your employee ID"
              placeholderTextColor="#000000"
              value={employeeId}
              onChangeText={text => {
                setEmployeeId(text);
                setErrors({ ...errors, employeeId: '' });
              }}
              autoCapitalize="none"
              keyboardType="numeric"
              returnKeyType="next"
              maxLength={10}
            />
            {errors.employeeId && <Text style={styles.error}>{errors.employeeId}</Text>}

            <Text style={styles.label}>Password</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="••••••••"
                placeholderTextColor="#000000"
                secureTextEntry={!showPassword}
                value={password}
                onChangeText={text => {
                  setPassword(text);
                  setErrors({ ...errors, password: '' });
                }}
                returnKeyType="done"
                onSubmitEditing={handleLogin}
              />
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                <Icon
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={24}
                  color="#555"
                  style={{ marginLeft: 10 }}
                />
              </TouchableOpacity>
            </View>
            {errors.password && <Text style={styles.error}>{errors.password}</Text>}

            <View style={styles.rememberRow}>
              <CheckBox
                value={rememberMe}
                onValueChange={setRememberMe}
                tintColors={{ true: '#002d6b', false: '#999' }}
              />
              <Text style={styles.rememberText}>Remember Me</Text>
            </View>

            <TouchableOpacity
              style={styles.loginBtn}
              onPress={handleLogin}
              activeOpacity={0.8}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.loginText}>LOGIN</Text>
              )}
            </TouchableOpacity>
          </View>

          <Modal visible={branchModalVisible} animationType="slide" transparent>
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>Select Branch</Text>
                <FlatList
                  data={branches}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.branchItem}
                      onPress={() => handleBranchSelect(item)}
                    >
                      <Text>{item?.BranchName ?? 'Unnamed Branch'}</Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </Modal>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    paddingTop: height * 0.15,
    paddingBottom: 40,
  },
  topBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: height * 0.6,
    resizeMode: 'cover',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: height * 0.04,
    marginTop: 300,
  },
  logo: {
    resizeMode: 'contain',
  },
  form: {
    width: '85%',
    paddingHorizontal: 10,
    paddingVertical: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 6,
    marginTop: 6,
  },
  input: {
    borderWidth: 1,
    borderColor: '#dddddd',
    padding: 14,
    marginBottom: 15,
    backgroundColor: '#f9f9f9',
    fontSize: 15,
    paddingHorizontal: 20,
    color: '#000000',
    borderRadius: 8,
  },
  passwordInput: {
    flex: 1,
    fontSize: 15,
    paddingVertical: 14,
    backgroundColor: '#f9f9f9',
    color: '#000000',
    borderRadius: 8,
  },
  error: {
    color: 'red',
    fontSize: 13,
    marginTop: -10,
    marginBottom: 10,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#dddddd',
    backgroundColor: '#f9f9f9',
    paddingHorizontal: 20,
    marginBottom: 15,
    borderRadius: 8,
  },
  rememberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 5,
  },
  rememberText: {
    marginLeft: 8,
    color: '#333',
  },
  loginBtn: {
    backgroundColor: '#002d6b',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffffcc',
    justifyContent: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  branchItem: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
});

export default LoginScreen;
