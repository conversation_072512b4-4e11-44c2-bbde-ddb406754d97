import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    FlatList,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Calendar } from 'react-native-calendars';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Navbar from '../../components/Navbar';
import ScrollOptionsBar from '../../components/ScrollOptionsBar';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
console.log('-------',fetchBusinessDay, '-------');

/**
 * MiscCollection Component
 * 
 * A comprehensive interface for managing miscellaneous collection receipts.
 * Features include receipt details management, item addition, and tabular display.
 */
export const MiscCollection = () => {
    // Navigation and form states
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [businessDateYYYYMMDD, setBusinessDateYYYYMMDD] = useState('');
    // Receipt header information
    const [docNumber, setDocNumber] = useState('');
    const [businessDate, setBusinessDate] = useState('');
    const [mobile, setMobile] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [remarks, setRemarks] = useState('');
    
    // Payment details
    const [receiptMode, setReceiptMode] = useState('');
    const [chequeNumber, setChequeNumber] = useState('');
    const [bankName, setBankName] = useState('');
    const [chequeDate, setChequeDate] = useState('');
    const [paymentAmount, setPaymentAmount] = useState('');
    
    // Data table states
    const [items, setItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [isSelectAllItems, setIsSelectAllItems] = useState(false);

    // Modal states for selection interfaces
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoadingItems, setIsLoadingItems] = useState(false);
    
    // Calendar modal state
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [selectedDateField, setSelectedDateField] = useState('');
    
    // Customer modal state
    const [customerModalVisible, setCustomerModalVisible] = useState(false);
    const [customerSearchTerm, setCustomerSearchTerm] = useState('');
    const [filteredCustomers, setFilteredCustomers] = useState([]);

    // Branch and authentication
    const [selectedBranch, setSelectedBranch] = useState('');
    const [authToken, setAuthToken] = useState('');

    // Payment mode data
    const [paymentModeData, setPaymentModeData] = useState([]);
    
    // Business date states
    const [businessDateData, setBusinessDateData] = useState(null);
    const [isLoadingBusinessDate, setIsLoadingBusinessDate] = useState(false);
    
    // Customer data
    const customerData = [
        'John Doe',
        'Jane Smith', 
        'Mike Johnson',
        'Sarah Williams',
        'David Brown',
        'Lisa Davis',
        'Robert Wilson',
        'Emma Thompson',
        'James Taylor',
        'Mary Anderson'
    ];

    // Load authentication data and branch info on component mount
    useEffect(() => {
        loadAuthData();
    }, []);

    // Fetch business day when auth data is loaded
    useEffect(() => {
        if (authToken && selectedBranch) {
            fetchBusinessDayData();
        }
    }, [authToken, selectedBranch]);

    // Add state for CreatedUserID
    const [createdUserID, setCreatedUserID] = useState('');

    /**
     * Load authentication token and branch data from AsyncStorage
     */
    const loadAuthData = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const branch = await AsyncStorage.getItem('selectedBranch');
            const userData = await AsyncStorage.getItem('userData');

            if (token) {
                setAuthToken(token);
            }

            if (branch) {
                const branchData = JSON.parse(branch);
                setSelectedBranch(branchData.BranchId);
                console.log('----Selected Branch --', branchData.BranchId);
            }

            if (userData) {
                const userDataParsed = JSON.parse(userData);
                console.log('----UserData from AsyncStorage --', userDataParsed);
                setCreatedUserID(userDataParsed.CreatedUserID || userDataParsed.userId || '');
            }
        } catch (error) {
            console.error('Error loading auth data:', error);
            Alert.alert('Error', 'Failed to load authentication data');
        }
    };

    /**
     * Format ISO date string to yyyymmdd for API
     */
    const formatDateToYYYYMMDD = (dateString) => {
    if (!dateString) return '';
    // If DD-MM-YYYY
    if (/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
        const [dd, mm, yyyy] = dateString.split('-');
        return `${yyyy}${mm}${dd}`;
    }
    // If YYYY-MM-DD or ISO
    const d = new Date(dateString);
    if (!isNaN(d)) {
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
    }
    return '';
};

    /**
     * Fetch business day data from API
     */
const fetchBusinessDayData = async () => {
    if (!authToken || !selectedBranch) {
        console.log('Missing auth token or branch for business day fetch');
        return;
    }

    setIsLoadingBusinessDate(true);
    try {
        console.log('Fetching business day data...');
        let businessDayResponse = await fetchBusinessDay(authToken, selectedBranch);
        console.log('------', businessDayResponse, '------');

        // If response is a string (like "28-06-2025"), wrap it
        if (typeof businessDayResponse === 'string') {
            businessDayResponse = { BusinessDateCode: businessDayResponse };
        }

        // If response is an array, take the first element
        if (Array.isArray(businessDayResponse) && businessDayResponse.length > 0) {
            businessDayResponse = businessDayResponse[0];
        }

        // If response is an object with numeric keys, try to extract the first value
        if (
            businessDayResponse &&
            typeof businessDayResponse === 'object' &&
            Object.keys(businessDayResponse).every(key => !isNaN(Number(key)))
        ) {
            businessDayResponse = businessDayResponse[Object.keys(businessDayResponse)[0]];

            // If it's a string, wrap it
            if (typeof businessDayResponse === 'string') {
                businessDayResponse = { BusinessDateCode: businessDayResponse };
            }
        }

        console.log('Business day response structure:', businessDayResponse);
        console.log('BusinessDateCode:', businessDayResponse?.BusinessDateCode);

        if (businessDayResponse && businessDayResponse.BusinessDateCode) {
            setBusinessDateData(businessDayResponse);

            // Format the business date for display (DD-MM-YYYY)
            const businessDateISO = businessDayResponse.BusinessDateCode;
            const formattedBusinessDate = formatDateForDisplay(businessDateISO);
            setBusinessDate(formattedBusinessDate);

            console.log('Business day data fetched:', businessDayResponse);
            console.log('Formatted business date:', formattedBusinessDate);
        } else {
            console.log('No business date data received or BusinessDateCode is missing');
            console.log('Response object keys:', businessDayResponse ? Object.keys(businessDayResponse) : 'null response');
            Alert.alert('Warning', 'No business date data available');
        }
    } catch (error) {
        console.error('Error fetching business day:', error);
        Alert.alert('Error', 'Failed to fetch business day data');
    } finally {
        setIsLoadingBusinessDate(false);
    }
};

    /**
     * Format date from ISO string to DD-MM-YYYY for display
     */
    const formatDateForDisplay = (isoDateString) => {
        if (!isoDateString) return '';
        try {
            // If already DD-MM-YYYY, just return it
            if (/^\d{2}-\d{2}-\d{4}$/.test(isoDateString)) {
                return isoDateString;
            }
            // If ISO or YYYY-MM-DD, convert to DD-MM-YYYY
            let dateObj;
            if (isoDateString.includes('T')) {
                dateObj = new Date(isoDateString);
            } else if (/^\d{4}-\d{2}-\d{2}$/.test(isoDateString)) {
                dateObj = new Date(isoDateString);
            }
            if (dateObj && !isNaN(dateObj)) {
                const day = String(dateObj.getDate()).padStart(2, '0');
                const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                const year = dateObj.getFullYear();
                return `${day}-${month}-${year}`;
            }
            // fallback
            return isoDateString;
        } catch (error) {
            console.error('Error formatting date:', error);
            return '';
        }
    };

    /**
     * Format date from DD-MM-YYYY to YYYY-MM-DD for API
     */
const formatDateForAPI = (displayDate) => {
    if (!displayDate) return '';
    // If DD-MM-YYYY
    if (/^\d{2}-\d{2}-\d{4}$/.test(displayDate)) {
        const [dd, mm, yyyy] = displayDate.split('-');
        return `${yyyy}-${mm}-${dd}`;
    }
    // If already YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}$/.test(displayDate)) {
        return displayDate;
    }
    // If ISO
    if (displayDate.includes('T')) {
        return displayDate.split('T')[0];
    }
    return '';
};

    /**
     * Get current date in YYYY-MM-DD format for API
     */
    const getCurrentDateForAPI = () => {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    /**
     * Get business date range for API calls
     */
    const getBusinessDateRange = () => {
        if (businessDateData && businessDateData.BusinessDateCode) {
            const businessDateForAPI = businessDateData.BusinessDateCode.split('T')[0]; // Extract YYYY-MM-DD
            return {
                fromDate: businessDateForAPI,
                toDate: businessDateForAPI,
                businessDate: businessDateForAPI,
                createdDate: getCurrentDateForAPI()
            };
        }
        
        // Fallback to current business date if no data
        const fallbackDate = formatDateForAPI(businessDate);
        return {
            fromDate: fallbackDate,
            toDate: fallbackDate,
            businessDate: fallbackDate,
            createdDate: getCurrentDateForAPI()
        };
    };

    /**
     * Fetch payment modes from API
     */
    const fetchPaymentModesFromAPI = async () => {
        if (!authToken) {
            Alert.alert('Error', 'Missing authentication token');
            return [];
        }

        setIsLoadingItems(true);
        try {
           const dateRange = getBusinessDateRange();
        // Use YYYYMMDD for GET API
        const fromDate = formatDateToYYYYMMDD(businessDateData?.BusinessDateCode || businessDate);
        const toDate = fromDate;
        const url = `https://retailuat.abisibg.com/api/v1/paymentmode`;
            console.log('Fetching payment modes from:', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Payment Modes API Response:', data);
            
            return data || [];
        } catch (error) {
            console.error('Error fetching payment modes:', error);
            Alert.alert('Error', 'Failed to fetch payment modes from server');
            return [];
        } finally {
            setIsLoadingItems(false);
        }
    };

    /**
     * Save receipt to API
     */
const saveReceiptToAPI = async (receiptData) => {
    if (!authToken) {
        Alert.alert('Error', 'Missing authentication token');
        return false;
    }

    try {
        const dateRange = getBusinessDateRange();
        // CHANGE: Update URL to the new API endpoint
        const url = 'https://retailUAT.abisaio.com:9001/api/Collection2';
        
        // CHANGE: Transform data to match the new API payload structure
        const requestData = {
            collectionHdrid: docNumber || "", // Use docNumber or generate unique ID
            branchID: selectedBranch,
            transTypeID: "RECEIPT", // Based on your SQL table
            transSubTypeID: "MISC_FUND", // Based on your SQL table
            channelId: "POS", // Add channelId if available
            businessDate: new Date(formatDateForAPI(businessDate)).toISOString(),
            isO_Number: "", // Add if needed
            remarks: remarks || "",
            totalAmount: parseFloat(paymentAmount) || 0,
            posted: false, // Set to false for save, true for post
            deleted: "N",
            createdDate: new Date().toISOString(),
            createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
            modifiedUserID: createdUserID,
            modifiedDate: new Date().toISOString(),
            deletedUserID: "",
            deletedDate: '1755-07-11T15:10:35.810Z',
            postedUserID: "",

            // CHANGE: Map items to collectionDtls array
            collectionDtls: items.map((item, index) => {
                console.log(`Processing item ${index + 1}:`, item);
                console.log('Item chequeNumber:', item.chequeNumber);
                console.log('Item bankName:', item.bankName);
                console.log('Item chequeDate:', item.chequeDate);
                
                const mappedItem = {
                    lineNumber: String(index + 1).padStart(3, '0'), // "001", "002", etc.
                    customerName: customerName || "",
                    paymentGatewayName: item.name || "",
                    amount: parseFloat(item.amount) || 0,
                    remarks: item.remarks || remarks || "",
                    chequeNo: item.chequeNumber || "",
                    chequeDate: item.chequeDate && item.chequeDate.trim() !== "" ? new Date(formatDateForAPI(item.chequeDate)).toISOString() : null,
                    bankName: item.bankName || "",
                    hoCustomerID: "", // Add if available
                    custID: selectedBranch, // Add if available
                    openingBalance: 0,
                    creditLimit: 0,
                    paymentGatewayID: "CAASH", // Add payment gateway ID if available
                    createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
                    deleted: "N",
                    dml: "I" // Insert operation
                };
                
                console.log('Mapped item for API:', mappedItem);
                return mappedItem;
            }),
            
            // CHANGE: Add empty arrays as specified
            collectionBills: [],
            collectionCNusages: []
        };

        console.log('Saving receipt with data:', requestData);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Save receipt API response:', result);
        return true;
    } catch (error) {
        console.error('Error saving receipt:', error);
        Alert.alert('Error', 'Failed to save receipt to server');
        return false;
    }
};






    /**
     * Post receipt to API
     */
const postReceiptToAPI = async (receiptData) => {
    if (!authToken) {
        Alert.alert('Error', 'Missing authentication token');
        return false;
    }

    try {
        const dateRange = getBusinessDateRange();
        // CHANGE: Update URL to the new API endpoint
        const url = 'https://retailUAT.abisaio.com:9001/api/Collection2';
        
        // CHANGE: Same structure as save but with posted: true
        const requestData = {
            collectionHdrid: docNumber || "",
            branchID: selectedBranch,
            transTypeID: "RECEIPT",
            transSubTypeID: "SBSCR",
            channelId: "POS",
            businessDate: new Date(formatDateForAPI(businessDate)).toISOString(),
            isO_Number: "",
            remarks: remarks || "",
            totalAmount: parseFloat(paymentAmount) || 0,
            posted: true, // CHANGE: Set to true for posting
            deleted: "N",
            createdDate: new Date().toISOString(),
            createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
            modifiedUserID: createdUserID,
            modifiedDate: new Date().toISOString(),
            deletedUserID: "",
            deletedDate: null,
            postedUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
            postedDate: new Date().toISOString(), // CHANGE: Set posted date
            
            collectionDtls: items.map((item, index) => {
                console.log(`Processing item ${index + 1} for POST:`, item);
                console.log('Item chequeNumber:', item.chequeNumber);
                console.log('Item bankName:', item.bankName);
                console.log('Item chequeDate:', item.chequeDate);
                
                const mappedItem = {
                    lineNumber: String(index + 1).padStart(3, '0'),
                    customerName: customerName || "",
                    paymentGatewayName: item.name || "",
                    amount: parseFloat(item.amount) || 0,
                    remarks: item.remarks || remarks || "",
                    chequeNo: item.chequeNumber || "",
                    chequeDate: item.chequeDate && item.chequeDate.trim() !== "" ? new Date(formatDateForAPI(item.chequeDate)).toISOString() : null,
                    bankName: item.bankName || "",
                    hoCustomerID: "",
                    custID: "",
                    openingBalance: 0,
                    creditLimit: 0,
                    paymentGatewayID: "CAASH",
                    createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
                    deleted: "N",
                    dml: "I"
                };
                
                console.log('Mapped item for POST API:', mappedItem);
                return mappedItem;
            }),
            
            collectionBills: [],
            collectionCNusages: []
        };

        console.log('Posting receipt with data:', requestData);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Post receipt API response:', result);
        return true;
    } catch (error) {
        console.error('Error posting receipt:', error);
        Alert.alert('Error', 'Failed to post receipt to server');
        return false;
    }
};

    // Filter modal data when search term changes
    useEffect(() => {
        if (searchTerm) {
            let filtered;
            if (modalType === 'receiptMode') {
                // For payment modes, search in PaymentGatewayName property
                filtered = modalData.filter(item =>
                    item.PaymentGatewayName.toLowerCase().includes(searchTerm.toLowerCase())
                );
            } else {
                // For other dropdowns, search directly in string array
                filtered = modalData.filter(item =>
                    (typeof item === 'string' ? item : item.PaymentGatewayName).toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData, modalType]);
    
    // Filter customer data when search term changes
    useEffect(() => {
        if (customerSearchTerm) {
            const filtered = customerData.filter(customer => 
                customer.toLowerCase().includes(customerSearchTerm.toLowerCase())
            );
            setFilteredCustomers(filtered);
        } else {
            setFilteredCustomers(customerData);
        }
    }, [customerSearchTerm]);

    // Handle select all checkbox for items
    useEffect(() => {
        if (isSelectAllItems) {
            const allIndices = items.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllItems]);

    /**
     * Handles actions from the ScrollOptionsBar
     */
    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        
        switch(option) {
            case 'New':
                resetForm();
                break;
            case 'Save':
                saveReceipt();
                break;
            case 'View':
                // View logic would be implemented here
                break;
            case 'Cancel':
                resetForm();
                break;
            case 'Post':
                postReceipt();
                break;
            default:
                break;
        }
    };

    /**
     * Resets all form fields to their initial state
     */
    const resetForm = () => {
        setDocNumber('');
        // Reset to business date from API
        if (businessDateData && businessDateData.BusinessDateCode) {
            const formattedBusinessDate = formatDateForDisplay(businessDateData.BusinessDateCode);
            setBusinessDate(formattedBusinessDate);
        } else {
            setBusinessDate('');
        }
        setMobile('');
        setCustomerName('');
        setRemarks('');
        setReceiptMode('');
        setChequeNumber('');
        setBankName('');
        setChequeDate('');
        setPaymentAmount('');
        setItems([]);
        setSelectedRows([]);
        setIsSelectAllItems(false);
    };

    /**
     * Validate numeric input (for amount, etc.)
     */
    const validateNumericInput = (value) => {
        // Allow only numbers and decimal point
        const numericValue = value.replace(/[^0-9.]/g, '');
        
        // Ensure only one decimal point
        const parts = numericValue.split('.');
        if (parts.length > 2) {
            return parts[0] + '.' + parts.slice(1).join('');
        }
        
        return numericValue;
    };

    /**
     * Enhanced validation function for all form fields
     */
    const validateForm = () => {
        const errors = [];
        
        // Business date validation
        if (!businessDate) {
            errors.push('Business date is required');
        }
        
        // Items validation
        if (items.length === 0) {
            errors.push('Please add at least one payment item');
        }
        
        return errors;
    };

    /**
     * Enhanced item validation function
     */
    const validateItemFields = () => {
        const errors = [];
        
        if (!receiptMode || receiptMode.trim().length === 0) {
            errors.push('Payment mode is required');
        }
        
        if (!paymentAmount || paymentAmount.trim().length === 0) {
            errors.push('Payment amount is required');
        } else if (isNaN(paymentAmount) || parseFloat(paymentAmount) <= 0) {
            errors.push('Payment amount must be a positive number');
        }
        
        // Payment details validation based on payment mode
        if (receiptMode && !['Cash', 'CAASH'].includes(receiptMode)) {
            if (receiptMode.toLowerCase().includes('upi')) {
                // UPI requires reference number
                if (!chequeNumber || chequeNumber.trim().length === 0) {
                    errors.push('Reference number is required for UPI payments');
                }
            } else if (receiptMode.toLowerCase().includes('cheque') || receiptMode.toLowerCase().includes('draft')) {
                // Cheque/Draft requires cheque number, bank name and date
                if (!chequeNumber || chequeNumber.trim().length === 0) {
                    errors.push('Cheque/Draft number is required');
                }
                if (!chequeDate) {
                    errors.push('Cheque/Draft date is required');
                }
                if (!bankName || bankName.trim().length === 0) {
                    errors.push('Bank name is required for Cheque/Draft payments');
                }
            } else if (receiptMode.toLowerCase().includes('card') || receiptMode.toLowerCase().includes('online')) {
                // Card/Online payments require reference number
                if (!chequeNumber || chequeNumber.trim().length === 0) {
                    errors.push('Reference number is required for card/online payments');
                }
                if (!bankName || bankName.trim().length === 0) {
                    errors.push('Bank name is required for card/online payments');
                }
            }
        }
        
        return errors;
    };

    /**
     * Validates and saves the receipt
     */
const saveReceipt = async () => {
    console.log('=== SAVE RECEIPT FUNCTION CALLED ===');
    console.log('Current items:', items);
    console.log('Items count:', items.length);
    
    const validationErrors = validateForm();
    
    if (validationErrors.length > 0) {
        Alert.alert(
            'Validation Error', 
            validationErrors.join('\n'),
            [{ text: 'OK', style: 'default' }]
        );
        return;
    }

    const receiptData = {
        docNumber,
        mobile,
        customerName,
        remarks,
        receiptMode,
        chequeNumber,
        bankName,
        chequeDate: formatDateForAPI(chequeDate),
        paymentAmount,
        items
    };

    console.log('Receipt data being sent to API:', receiptData);

    setIsLoading(true);
    const success = await saveReceiptToAPI(receiptData);
    setIsLoading(false);

    if (success) {
        Alert.alert('Success', 'Receipt saved successfully');
        // Reset the entire screen after successful save
        resetForm();
    }
};

    /**
     * Posts the receipt
     */
    const postReceipt = async () => {
        const validationErrors = validateForm();
        
        if (validationErrors.length > 0) {
            Alert.alert(
                'Validation Error', 
                validationErrors.join('\n'),
                [{ text: 'OK', style: 'default' }]
            );
            return;
        }

        const receiptData = {
            docNumber,
            mobile,
            customerName,
            remarks,
            receiptMode,
            chequeNumber,
            bankName,
            chequeDate: formatDateForAPI(chequeDate),
            paymentAmount,
            items
        };

        setIsLoading(true);
        const success = await postReceiptToAPI(receiptData);
        setIsLoading(false);

        if (success) {
            Alert.alert('Success', 'Receipt posted successfully');
            // Reset the entire screen after successful post
            resetForm();
        }
    };

    /**
     * Toggles selection of a table row
     */
    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllItems(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            if (selectedRows.length + 1 === items.length) {
                setIsSelectAllItems(true);
            }
        }
    };

    /**
     * Removes selected rows from the items table
     */
    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }
        const newItems = items.filter((_, index) => !selectedRows.includes(index));
        setItems(newItems);
        setSelectedRows([]);
        setIsSelectAllItems(false);
    };

    /**
     * Opens selection modal with appropriate data
     */
    const openModal = async (type) => {
        setIsLoading(true);
        setSearchTerm('');

        try {
            let dataToShow = [];
            switch (type) {
                case 'receiptMode':
                    dataToShow = await fetchPaymentModesFromAPI();
                    break;
                default:
                    dataToShow = [];
            }
            
            setModalData(dataToShow);
            setFilteredModalData(dataToShow);
            setModalType(type);
            setModalVisible(true);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handles item selection from modal
     */
    const handleModalItemSelect = (item) => {
        console.log('Modal item selected:', item);
        switch (modalType) {
            case 'receiptMode':
                console.log('Setting receipt mode to:', item.PaymentGatewayName);
                setReceiptMode(item.PaymentGatewayName);
                break;
            default:
                break;
        }
        setModalVisible(false);
    };
    
    /**
     * Opens calendar modal for date selection
     */
    const openCalendar = (field) => {
        setSelectedDateField(field);
        setCalendarVisible(true);
    };
    
    /**
     * Handles date selection from calendar
     */
    const handleDateSelect = (day) => {
        console.log('Date selected:', day);
        // day.dateString is YYYY-MM-DD
        const formattedDate = day.dateString.split('-').reverse().join('-'); // DD-MM-YYYY
        console.log('Formatted date:', formattedDate);
        console.log('Selected date field:', selectedDateField);
        if (selectedDateField === 'businessDate') {
            console.log('Setting business date to:', formattedDate);
            setBusinessDate(formattedDate);
        } else if (selectedDateField === 'chequeDate') {
            console.log('Setting cheque date to:', formattedDate);
            setChequeDate(formattedDate);
        }
        setCalendarVisible(false);
    };
    
    /**
     * Opens customer selection modal
     */
    const openCustomerModal = () => {
        setCustomerSearchTerm('');
        setFilteredCustomers(customerData);
        setCustomerModalVisible(true);
    };
    
    /**
     * Handles customer selection
     */
    const handleCustomerSelect = (customer) => {
        setCustomerName(customer);
        setCustomerModalVisible(false);
    };
    
    /**
     * Handles customer search
     */
    const handleCustomerSearch = () => {
        const filtered = customerData.filter(customer => 
            customer.toLowerCase().includes(customerSearchTerm.toLowerCase())
        );
        setFilteredCustomers(filtered);
    };

    /**
     * Get current date in YYYY-MM-DD format for calendar
     */
    const getCurrentDate = () => {
        const today = new Date();
        return today.toISOString().split('T')[0];
    };

    /**
     * Convert ISO or DD-MM-YYYY to YYYY-MM-DD for calendar
     */
    const convertDateForCalendar = (dateString) => {
        if (!dateString) return getCurrentDate();
        // If ISO format (2025-06-28T00:00:00.000Z)
        if (dateString.includes('T')) {
            return dateString.split('T')[0];
        }
        // If DD-MM-YYYY
        const parts = dateString.split('-');
        if (parts.length === 3) {
            return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        return getCurrentDate();
    };

    /**
     * Handle payment amount change with validation
     */
    const handlePaymentAmountChange = (value) => {
        const validatedAmount = validateNumericInput(value);
        setPaymentAmount(validatedAmount);
    };

    /**
     * Validates and adds a new item to the table
     */
const handleAddItem = async () => {
    const validationErrors = validateItemFields();
    
    if (validationErrors.length > 0) {
        Alert.alert(
            'Validation Error', 
            validationErrors.join('\n'),
            [{ text: 'OK', style: 'default' }]
        );
        return;
    }

    setIsLoading(true);

    try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        const newItem = {
            name: receiptMode,
            amount: paymentAmount,
            date: businessDate,
            chequeNumber,
            bankName,
            chequeDate,
            remarks: remarks
        };

        console.log('New item being added:', newItem);
        setItems([...items, newItem]);
        
        // Clear item details section only
        setReceiptMode('');
        setPaymentAmount('');
        setChequeNumber('');
        setBankName('');
        setChequeDate('');
    } finally {
        setIsLoading(false);
    }
};

    return (
        <View style={styles.container}>
            <Navbar />
            
            <ScrollOptionsBar
                title="Misc Collection"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    {/* Business Date Loading Indicator */}
                    {isLoadingBusinessDate && (
                        <View style={styles.loadingIndicator}>
                            <Text style={styles.loadingText}>Loading business date...</Text>
                        </View>
                    )}

                    {/* First Row: Doc# and Business Date */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Doc#</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Document Number"
                                placeholderTextColor="#888"
                                value={docNumber}
                                editable={false} // Disable input for Doc# as it will be auto-generated
                                onChangeText={(text) => {
                                    console.log('Doc Number changed:', text);
                                    setDocNumber(text);
                                }}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Business Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder="DD-MM-YYYY"
                                    placeholderTextColor="#888"
                                    value={businessDate}
                                    onChangeText={(text) => {
                                        console.log('Business Date changed:', text);
                                        setBusinessDate(text);
                                    }}
                                    editable={false}
                                    disabled={true} // Disable input for Business Date
                                />
                                <TouchableOpacity 
                                    style={styles.calendarButton}
                                    disabled={true} // Disable calendar button for now
                                    onPress={() => openCalendar('businessDate')}
                                >
                                    <Icon name="date-range" size={16} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Second Row: Line# and Remarks */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Line#</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter Line Number"
                                placeholderTextColor="#888"
                                value={mobile}
                                onChangeText={(text) => {
                                    console.log('Line# changed:', text);
                                    setMobile(text);
                                }}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Remarks</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter Remarks"
                                placeholderTextColor="#888"
                                value={remarks}
                                onChangeText={(text) => {
                                    console.log('Remarks changed:', text);
                                    setRemarks(text);
                                }}
                            />
                        </View>
                    </View>

                    {/* Third Row: Payment Details */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft No/Ref No</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter Cheque/Draft No/Ref No"
                                placeholderTextColor="#888"
                                value={chequeNumber}
                                onChangeText={(text) => {
                                    console.log('Cheque Number changed:', text);
                                    setChequeNumber(text);
                                }}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Bank Name</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter Bank Name"
                                placeholderTextColor="#888"
                                value={bankName}
                                onChangeText={(text) => {
                                    console.log('Bank Name changed:', text);
                                    setBankName(text);
                                }}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder="DD-MM-YYYY"
                                    placeholderTextColor="#888"
                                    value={chequeDate}
                                    onChangeText={(text) => {
                                    console.log('Cheque Date changed:', text);
                                    setChequeDate(text);
                                }}
                                    editable={false}
                                />
                                <TouchableOpacity 
                                    style={styles.calendarButton}
                                    onPress={() => openCalendar('chequeDate')}
                                >
                                    <Icon name="date-range" size={16} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Fourth Row: Payment Mode and Amount with Add Button */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Payment Mode</Text>
                            <TouchableOpacity 
                                style={styles.dropdown}
                                onPress={() => openModal('receiptMode')}
                                disabled={isLoading}
                            >
                                <Text style={[styles.inputText, !receiptMode && styles.placeholderText]}>
                                    {receiptMode || 'Select Payment Mode'}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Amount</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Enter Amount"
                                placeholderTextColor="#888"
                                value={paymentAmount}
                                onChangeText={handlePaymentAmountChange}
                                keyboardType="decimal-pad"
                            />
                        </View>
                        
                        <View style={styles.addButtonContainer}>
                            <TouchableOpacity 
                                style={[styles.addButton, isLoading && styles.disabledButton]} 
                                onPress={handleAddItem}
                                disabled={isLoading}
                            >
                                <Text style={styles.addButtonText}>Add</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Items Table */}
                    <View style={styles.tableContainer}>
                        {items.length > 0 ? (
                            <>
                                <View style={styles.tableHeader}>
                                    <View style={styles.checkboxCell}>
                                        <CheckBox
                                            value={isSelectAllItems}
                                            onValueChange={setIsSelectAllItems}
                                            tintColors={{ true: '#FDC500', false: '#FDC500' }}
                                        />
                                    </View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Payment Mode</Text></View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Amount</Text></View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Date</Text></View>
                                </View>

                                {items.map((item, index) => (
                                    <View key={index} style={styles.tableRow}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.name}</Text></View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.amount}</Text></View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.date}</Text></View>
                                    </View>
                                ))}
                            </>
                        ) : (
                            <View style={styles.emptyTableContainer}>
                                <Text style={styles.emptyTableText}>No Data</Text>
                            </View>
                        )}
                    </View>

                    {/* Delete Selected Button */}
                    <View style={styles.deleteButtonContainer}>
                        <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                            <Text style={styles.deleteButtonText}>Delete selected</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>

            {/* Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'receiptMode' ? 'Select Payment Mode' : 'Select Option'}
                        </Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {isLoadingItems ? (
                                    <View style={styles.loadingContainer}>
                                        <Text style={styles.loadingText}>Loading payment modes...</Text>
                                    </View>
                                ) : (
                                    filteredModalData.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.modalItem}
                                            onPress={() => handleModalItemSelect(item)}
                                        >
                                            <Text style={styles.modalItemText}>
                                                {modalType === 'receiptMode' ? item.PaymentGatewayName : item}
                                            </Text>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Customer Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={customerModalVisible}
                onRequestClose={() => setCustomerModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setCustomerModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Select Customer</Text>
                        
                        <View style={styles.customerSearchContainer}>
                            <TextInput
                                style={styles.customerSearchInput}
                                placeholder="Search Customer"
                                placeholderTextColor="#888"
                                value={customerSearchTerm}
                                onChangeText={setCustomerSearchTerm}
                            />
                            <TouchableOpacity 
                                style={styles.searchButton}
                                onPress={handleCustomerSearch}
                            >
                                <Text style={styles.searchButtonText}>Search</Text>
                            </TouchableOpacity>
                        </View>
                        
                        <ScrollView>
                            <View style={styles.customerItemsContainer}>
                                {filteredCustomers.map((customer, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.customerItem}
                                        onPress={() => handleCustomerSelect(customer)}
                                    >
                                        <Text style={styles.customerItemText}>{customer}</Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCustomerModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Calendar Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={calendarVisible}
                onRequestClose={() => setCalendarVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setCalendarVisible(false)}
                >
                    <View style={styles.calendarModalContent}>
                        <Text style={styles.modalTitle}>Select Date</Text>
                        
                        <Calendar
                            onDayPress={handleDateSelect}
                            current={
                                selectedDateField === 'businessDate'
                                    ? convertDateForCalendar(
                                        businessDateData && businessDateData.BusinessDateCode
                                            ? businessDateData.BusinessDateCode
                                            : businessDate
                                    )
                                    : convertDateForCalendar(chequeDate)
                            }
                            markedDates={{
                                [selectedDateField === 'businessDate'
                                    ? convertDateForCalendar(
                                        businessDateData && businessDateData.BusinessDateCode
                                            ? businessDateData.BusinessDateCode
                                            : businessDate
                                    )
                                    : convertDateForCalendar(chequeDate)
                                ]: {
                                    selected: true,
                                    selectedColor: '#FDC500'
                                }
                            }}
                            theme={{
                                backgroundColor: '#ffffff',
                                calendarBackground: '#ffffff',
                                textSectionTitleColor: '#02096A',
                                selectedDayBackgroundColor: '#FDC500',
                                selectedDayTextColor: '#000000',
                                todayTextColor: '#02096A',
                                dayTextColor: '#2d4150',
                                textDisabledColor: '#d9e1e8',
                                dotColor: '#FDC500',
                                selectedDotColor: '#ffffff',
                                arrowColor: '#02096A',
                                disabledArrowColor: '#d9e1e8',
                                monthTextColor: '#02096A',
                                indicatorColor: '#02096A',
                                textDayFontFamily: 'Poppins',
                                textMonthFontFamily: 'Poppins',
                                textDayHeaderFontFamily: 'Poppins',
                                textDayFontWeight: 'bold',
                                textMonthFontWeight: 'bold',
                                textDayHeaderFontWeight: 'bold',
                                textDayFontSize: 14,
                                textMonthFontSize: 16,
                                textDayHeaderFontSize: 12
                            }}
                        />
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCalendarVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    loadingIndicator: {
        backgroundColor: '#fff3cd',
        padding: 10,
        borderRadius: 6,
        marginBottom: 15,
        borderWidth: 1,
        borderColor: '#ffeaa7',
    },
    disabledButton: {
        opacity: 0.6,
    },
    contentContainer: {
        flex: 1,
        padding: 8,
    },
    sectionContainer: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 12,
        marginHorizontal: 6,
        marginVertical: 4,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.12,
        shadowRadius: 4,
        elevation: 5,
        borderWidth: 1,
        borderColor: '#e3f2fd',
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginBottom: 10,
        gap: 8,
    },
    inputContainer: {
        flex: 1,
        minWidth: 150,
    },
    addButtonContainer: {
        justifyContent: 'flex-end',
        alignItems: 'center',
        minWidth: 120,
    },
    labelText: {
        fontSize: 13,
        color: '#2c3e50',
        marginBottom: 6,
        fontFamily: 'Poppins',
        fontWeight: '600',
        letterSpacing: 0.3,
    },
    input: {
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 8,
        height: 56,
        fontFamily: 'Poppins',
        fontSize: 16,
        color: '#2c3e50',
        fontWeight: '600',
        textAlignVertical: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    dateInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    dateInput: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 8,
        height: 56,
        fontFamily: 'Poppins',
        fontSize: 16,
        color: '#2c3e50',
        fontWeight: '600',
        textAlignVertical: 'center',
        marginRight: 10,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    calendarButton: {
        backgroundColor: '#3f51b5',
        borderRadius: 12,
        padding: 16,
        height: 56,
        width: 56,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    dropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 12,
        paddingHorizontal: 18,
        paddingVertical: 8,
        height: 56,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    inputText: {
        color: '#2c3e50',
        fontFamily: 'Poppins',
        fontSize: 16,
        fontWeight: '600',
    },
    placeholderText: {
        color: '#999',
        fontWeight: '400',
    },
    dropdownIcon: {
        color: '#3f51b5',
        fontSize: 16,
        fontWeight: 'bold',
    },
    addButton: {
        backgroundColor: '#4caf50',
        paddingVertical: 16,
        paddingHorizontal: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        height: 56,
        minWidth: 120,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
    },
    addButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 16,
        fontFamily: 'Poppins',
        letterSpacing: 0.3,
    },
    tableContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        borderWidth: 1,
        borderColor: '#ddd',
        minHeight: 200,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#041C44',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableHeaderText: {
        fontWeight: 'bold',
        fontSize: 12,
        color: 'white',
        fontFamily: 'Poppins',
        textAlign: 'center',
    },
    tableHeaderCell: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableCell: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tableCellText: {
        fontSize: 12,
        color: '#333',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableContainer: {
        padding: 40,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f8f8',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
        fontWeight: 'bold',
        fontStyle: 'italic',
    },
    deleteButtonContainer: {
        alignItems: 'flex-start',
        marginTop: 10,
    },
    deleteButton: {
        backgroundColor: '#f44336',
        paddingVertical: 16,
        paddingHorizontal: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        height: 56,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
    },
    deleteButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 16,
        fontFamily: 'Poppins',
        letterSpacing: 0.3,
    },
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    // Loading styles
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        fontSize: 16,
        color: '#888',
        fontFamily: 'Poppins',
        fontStyle: 'italic',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    // Customer Modal Styles
    customerSearchContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerSearchInput: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    searchButton: {
        backgroundColor: '#02096A',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    searchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        fontSize: 14,
    },
    customerItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    customerItem: {
        backgroundColor: '#FDC500',
        width: 140,
        height: 60,
        padding: 10,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    customerItemText: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    // Calendar Modal Styles
    calendarModalContent: {
        width: '90%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
});

export default MiscCollection;