    import React, { useState, useEffect, useMemo, useCallback } from 'react';
    import {
        View,
        Text,
        StyleSheet,
        TouchableOpacity,
        ScrollView,
        TextInput,
        Alert,
        ActivityIndicator
    } from 'react-native';
    import CheckBox from '@react-native-community/checkbox';
    import AsyncStorage from '@react-native-async-storage/async-storage';
    import Icon from 'react-native-vector-icons/MaterialIcons';
    import Navbar from '../../components/Navbar';
    import ScrollOptionsBar from '../../components/ScrollOptionsBar';
    import ReceiptModals from './ReusableModal';
    import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';

    /**
     * ReceiptForSubscription Component
     * 
     * A comprehensive interface for managing subscription receipts.
     * Features include receipt details management, item addition, and tabular display.
     */
    export const ReceiptForSubscription = () => {
        // Navigation and form states
        const [selectedScrollOption, setSelectedScrollOption] = useState('');
        const [isLoading, setIsLoading] = useState(false);

        // Receipt header information
        const [docNumber, setDocNumber] = useState('');
        const [businessDate, setBusinessDate] = useState('');
        const [mobile, setMobile] = useState('');
        const [customerName, setCustomerName] = useState('');
        const [remarks, setRemarks] = useState('');
        const [isMobileSearch, setIsMobileSearch] = useState(false);
        
        // Item details
        const [itemName, setItemName] = useState('');
        const [itemID, setItemID] = useState(''); // Store selected item ID
        const [qty, setQty] = useState('');
        const [price, setPrice] = useState('');
        const [amount, setAmount] = useState('');
        const [finalAmount, setFinalAmount] = useState('');
        const [pointsPerQty, setPointsPerQty] = useState('');
        const [totalPoints, setTotalPoints] = useState('');
        
        // NEW: Wallet points state
        const [walletPointsData, setWalletPointsData] = useState([]);
        const [isLoadingPoints, setIsLoadingPoints] = useState(false);

        // ...other state...
        const [businessDateData, setBusinessDateData] = useState(null);
        const [isLoadingBusinessDate, setIsLoadingBusinessDate] = useState(false);
        
        // Payment details
        const [receiptMode, setReceiptMode] = useState('');
        const [chequeNumber, setChequeNumber] = useState('');
        const [bankName, setBankName] = useState('');
        const [chequeDate, setChequeDate] = useState('');

        // Data table states
        const [items, setItems] = useState([]);
        const [selectedRows, setSelectedRows] = useState([]);
        const [isSelectAllItems, setIsSelectAllItems] = useState(false);

        // Summary fields
        const [totalNos, setTotalNos] = useState('');
        const [totalAmount, setTotalAmount] = useState('');
        const [tax, setTax] = useState('');
        const [grandTotal, setGrandTotal] = useState('');

        // Modal states for selection interfaces
        const [modalVisible, setModalVisible] = useState(false);
        const [modalType, setModalType] = useState('');
        const [modalData, setModalData] = useState([]);
        const [filteredModalData, setFilteredModalData] = useState([]);
        const [searchTerm, setSearchTerm] = useState('');
        const [isLoadingItems, setIsLoadingItems] = useState(false);

        // Branch and authentication
        const [selectedBranch, setSelectedBranch] = useState('');
        const [authToken, setAuthToken] = useState('');
        const [createdUserID, setCreatedUserID] = useState('');

        // Calendar modal state
        const [calendarVisible, setCalendarVisible] = useState(false);
        const [selectedDateField, setSelectedDateField] = useState('');

        // Customer modal state
        const [customerModalVisible, setCustomerModalVisible] = useState(false);
        const [customerSearchTerm, setCustomerSearchTerm] = useState('');
        const [filteredCustomers, setFilteredCustomers] = useState([]);
        const [isCustomerSearchLoading, setIsCustomerSearchLoading] = useState(false);

        // DocName modal state
        const [docNameModalVisible, setDocNameModalVisible] = useState(false);
        const [docNameSearchTerm, setDocNameSearchTerm] = useState('');
        const [filteredDocNames, setFilteredDocNames] = useState([]);
        const [docNameData, setDocNameData] = useState([]);
        const [isLoadingDocNames, setIsLoadingDocNames] = useState(false);

        const [customerData, setCustomerData] = useState([]);
        const [selectedCustomer, setSelectedCustomer] = useState(null);
        const [custID, setCustID] = useState('');

        // Payment mode data
        const [paymentModeData, setPaymentModeData] = useState([]);

        // NEW: Memoized SBSC points calculation
        const sbscPoints = useMemo(() => {
            const sbscPoint = walletPointsData.find(point => point.PointType === 'SBSC');
            return sbscPoint ? sbscPoint.points : 0;
        }, [walletPointsData]);

        // NEW: Memoized total points calculation
        const calculatedTotalPoints = useMemo(() => {
            if (qty && sbscPoints) {
                return (parseFloat(qty) * sbscPoints).toString();
            }
            return '';
        }, [qty, sbscPoints]);

        // Load authentication data and branch info on component mount
        useEffect(() => {
            loadAuthData();
        }, []);

        useEffect(() => {
            if (authToken && selectedBranch) {
                fetchBusinessDayData();
            }
        }, [authToken, selectedBranch]);

        // NEW: Fetch wallet points when itemID changes
        useEffect(() => {
            if (itemID && authToken) {
                fetchWalletPoints(itemID);
            } else {
                // Reset points when no item selected
                setWalletPointsData([]);
                setPointsPerQty('');
                setTotalPoints('');
                setPrice(''); // Add this line to reset price as well
            }
        }, [itemID, authToken]);

        // NEW: Update points per qty when SBSC points change
        useEffect(() => {
            if (sbscPoints > 0) {
                setPointsPerQty(sbscPoints.toString());
            }
        }, [sbscPoints]);


        // Add this with other useMemo hooks
    const cshPoints = useMemo(() => {
        const cshPoint = walletPointsData.find(point => point.PointType === 'CSH');
        return cshPoint ? cshPoint.points : 0;
    }, [walletPointsData]);


    // Add this new useEffect after the existing SBSC points useEffect
    useEffect(() => {
        if (cshPoints > 0) {
            setPrice(cshPoints.toString());
        }
    }, [cshPoints]);



        // NEW: Update total points when qty or points per qty changes
        useEffect(() => {
            if (calculatedTotalPoints) {
                setTotalPoints(calculatedTotalPoints);
            }
        }, [calculatedTotalPoints]);

        // NEW: Fetch wallet points API function
        const fetchWalletPoints = useCallback(async (selectedItemID) => {
            if (!selectedItemID || !authToken) {
                console.log('Missing itemID or auth token for wallet points');
                return;
            }

            setIsLoadingPoints(true);
            try {
                const url = `https://retailuat.abisibg.com/api/v1/walletpoints?ItemId=${selectedItemID}`;
                console.log('🎯 Fetching wallet points from:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Wallet Points API Response:', data);

                setWalletPointsData(data || []);
            } catch (error) {
                console.error('Error fetching wallet points:', error);
                // Don't show alert for points fetch failure, just log it
                console.warn('Failed to fetch wallet points for item:', selectedItemID);
                setWalletPointsData([]);
            } finally {
                setIsLoadingPoints(false);
            }
        }, [authToken]);

        const formatDateForDisplay = (isoDateString) => {
            if (!isoDateString) return '';
            if (/^\d{2}-\d{2}-\d{4}$/.test(isoDateString)) {
                return isoDateString;
            }
            let dateObj;
            if (isoDateString.includes('T')) {
                dateObj = new Date(isoDateString);
            } else if (/^\d{4}-\d{2}-\d{2}$/.test(isoDateString)) {
                dateObj = new Date(isoDateString);
            }
            if (dateObj && !isNaN(dateObj)) {
                const day = String(dateObj.getDate()).padStart(2, '0');
                const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                const year = dateObj.getFullYear();
                return `${day}-${month}-${year}`;
            }
            return isoDateString;
        };

        const fetchBusinessDayData = async () => {
            setIsLoadingBusinessDate(true);
            try {
                let businessDayResponse = await fetchBusinessDay(authToken, selectedBranch);

                if (typeof businessDayResponse === 'string') {
                    businessDayResponse = { BusinessDateCode: businessDayResponse };
                }
                if (Array.isArray(businessDayResponse) && businessDayResponse.length > 0) {
                    businessDayResponse = businessDayResponse[0];
                }
                if (
                    businessDayResponse &&
                    typeof businessDayResponse === 'object' &&
                    Object.keys(businessDayResponse).every(key => !isNaN(Number(key)))
                ) {
                    businessDayResponse = businessDayResponse[Object.keys(businessDayResponse)[0]];
                    if (typeof businessDayResponse === 'string') {
                        businessDayResponse = { BusinessDateCode: businessDayResponse };
                    }
                }

                if (businessDayResponse && businessDayResponse.BusinessDateCode) {
                    setBusinessDateData(businessDayResponse);
                    setBusinessDate(formatDateForDisplay(businessDayResponse.BusinessDateCode));
                }
            } catch (error) {
                Alert.alert('Error', 'Failed to fetch business day data');
            } finally {
                setIsLoadingBusinessDate(false);
            }
        };

        /**
         * Fetch customers from API based on customer name
         */
        const fetchCustomersByName = async (customerName) => {
            if (!selectedBranch || !authToken || !customerName || customerName.length < 2) {
                return [];
            }

            try {
                const url = `https://retailuat.abisaio.com:9001/api/POSCustomerSerch/Get/${selectedBranch}/RT/CUSTNAME/${customerName}`;
                console.log('🔍 Using CUSTOMER NAME API endpoint:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Customer API Response:', data);

                return data.customers || [];
            } catch (error) {
                console.error('Error fetching customers:', error);
                Alert.alert('Error', 'Failed to fetch customers from server');
                return [];
            }
        };

        /**
         * Fetch customer from API based on mobile number
         */
        const fetchCustomerByMobile = async (mobileNumber) => {
            if (!authToken || !mobileNumber || mobileNumber.length !== 10) {
                console.log('Invalid mobile number or missing auth token:', mobileNumber);
                return null;
            }

            setIsLoading(true);
            try {
                const url = `https://retailuat.abisaio.com:9001/api/POSCustomer/MOBILE/${mobileNumber}`;
                console.log('🔍 Using MOBILE API endpoint:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Customer by Mobile API Response:', data);

                if (data.customers && data.customers.length > 0) {
                    if (data.customers.length > 1) {
                        return data.customers;
                    } else {
                        const customer = data.customers[0];
                        console.log('🔍 Auto-selected customer from mobile:', customer);
                        setCustomerName(customer.customerName);
                        setCustID(customer.custID);
                        setSelectedCustomer(customer);
                        console.log('🔍 Auto-setting customerName:', customer.customerName, 'custID:', customer.custID);
                        return customer;
                    }
                } else {
                    Alert.alert('Info', 'No customer found with this mobile number');
                    return null;
                }
            } catch (error) {
                console.error('Error fetching customer by mobile:', error);
                Alert.alert('Error', 'Failed to fetch customer details');
                return null;
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Load authentication token and branch data from AsyncStorage
         */
        const loadAuthData = async () => {
            try {
                const token = await AsyncStorage.getItem('authToken');
                const branch = await AsyncStorage.getItem('selectedBranch');
                const userData = await AsyncStorage.getItem('userData');

                if (token) {
                    setAuthToken(token);
                }

                if (branch) {
                    const branchData = JSON.parse(branch);
                    setSelectedBranch(branchData.BranchId);
                    console.log('----Selected Branch --', branchData.BranchId);
                }

                if (userData) {
                    const userDataParsed = JSON.parse(userData);
                    console.log('----UserData from AsyncStorage --', userDataParsed);
                    setCreatedUserID(userDataParsed.CreatedUserID || userDataParsed.userId || '');
                }
            } catch (error) {
                console.error('Error loading auth data:', error);
                Alert.alert('Error', 'Failed to load authentication data');
            }
        };

        /**
         * Fetch items from API based on branch ID
         */
        const fetchItemsFromAPI = async () => {
            if (!selectedBranch || !authToken) {
                Alert.alert('Error', 'Missing authentication data or branch information');
                return [];
            }

            setIsLoadingItems(true);
            try {
                const url = `https://retailUAT.abisaio.com:9001/api/ItemByParm/GetbyCategory/${selectedBranch}/SR001`;
                console.log('-----', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data || [];
            } catch (error) {
                console.error('Error fetching items:', error);
                Alert.alert('Error', 'Failed to fetch items from server');
                return [];
            } finally {
                setIsLoadingItems(false);
            }
        };

        /**
         * Fetch payment modes from API
         */
        const fetchPaymentModesFromAPI = async () => {
            if (!authToken) {
                Alert.alert('Error', 'Missing authentication token');
                return [];
            }

            setIsLoadingItems(true);
            try {
                const url = 'https://retailuat.abisibg.com/api/v1/paymentmode';
                console.log('Fetching payment modes from:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Payment Modes API Response:', data);

                return data || [];
            } catch (error) {
                console.error('Error fetching payment modes:', error);
                Alert.alert('Error', 'Failed to fetch payment modes from server');
                return [];
            } finally {
                setIsLoadingItems(false);
            }
        };

        /**
         * Fetch DocNames from API
         */
        const fetchDocNamesFromAPI = async () => {
            if (!selectedBranch || !businessDateData?.BusinessDateCode) {
                Alert.alert('Error', 'Missing branch or business date');
                return [];
            }

            setIsLoadingDocNames(true);
            try {
                const formatDateToYYYYMMDD = (dateString) => {
                    if (!dateString) return '';
                    if (/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
                        const [dd, mm, yyyy] = dateString.split('-');
                        return `${yyyy}${mm}${dd}`;
                    }
                    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                        const [yyyy, mm, dd] = dateString.split('-');
                        return `${yyyy}${mm}${dd}`;
                    }
                    if (dateString.includes('T')) {
                        const d = new Date(dateString);
                        const year = d.getFullYear();
                        const month = String(d.getMonth() + 1).padStart(2, '0');
                        const day = String(d.getDate()).padStart(2, '0');
                        return `${year}${month}${day}`;
                    }
                    return '';
                };

                const businessDate = formatDateToYYYYMMDD(businessDateData.BusinessDateCode);
                console.log('Business date for DocNames API:', businessDate);

                const url = `https://retailuat.abisibg.com/api/v1/fetchsale?BranchId=${selectedBranch}&stage=PL&FromDate=${businessDate}&ToDate=${businessDate}`;
                console.log('Fetching DocNames from:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                console.log('DocNames API Response:', data);
                console.log('Response length:', data?.length);

                const docNames = data.map(item => ({
                    DocID: item.DocID,
                    DocName: item.DocName
                }));

                console.log('Processed DocNames:', docNames);

                setDocNameData(docNames);
                setFilteredDocNames(docNames);
                return docNames;
            } catch (error) {
                console.error('Error fetching DocNames:', error);
                Alert.alert('Error', 'Failed to fetch DocNames from server');
                return [];
            } finally {
                setIsLoadingDocNames(false);
            }
        };

        // Filter modal data when search term changes
        useEffect(() => {
            if (searchTerm) {
                let filtered;
                if (modalType === 'itemName') {
                    filtered = modalData.filter(item =>
                        item.itemName.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                } else if (modalType === 'receiptMode') {
                    filtered = modalData.filter(item =>
                        item.PaymentGatewayName.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                } else {
                    filtered = modalData.filter(item =>
                        (typeof item === 'string' ? item : item.itemName).toLowerCase().includes(searchTerm.toLowerCase())
                    );
                }
                setFilteredModalData(filtered);
            } else {
                setFilteredModalData(modalData);
            }
        }, [searchTerm, modalData, modalType]);

        // Filter customer data when search term changes
        useEffect(() => {
            if (customerSearchTerm) {
                const filtered = customerData.filter(customer =>
                    customer.customerName.toLowerCase().includes(customerSearchTerm.toLowerCase())
                );
                setFilteredCustomers(filtered);
            } else {
                setFilteredCustomers(customerData);
            }
        }, [customerSearchTerm]);

        // Filter DocName data when search term changes
        useEffect(() => {
            if (docNameSearchTerm) {
                const filtered = docNameData.filter(item =>
                    item.DocName.toLowerCase().includes(docNameSearchTerm.toLowerCase())
                );
                setFilteredDocNames(filtered);
            } else {
                setFilteredDocNames(docNameData);
            }
        }, [docNameSearchTerm, docNameData]);

        // Handle select all checkbox for items
        useEffect(() => {
            if (isSelectAllItems) {
                const allIndices = items.map((_, index) => index);
                setSelectedRows(allIndices);
            } else {
                setSelectedRows([]);
            }
        }, [isSelectAllItems]);

        /**
         * Validate numeric input (for qty, price, etc.)
         */
        const validateNumericInput = (value) => {
            // Allow only numbers and decimal point
            const numericValue = value.replace(/[^0-9.]/g, '');
            
            // Ensure only one decimal point
            const parts = numericValue.split('.');
            if (parts.length > 2) {
                return parts[0] + '.' + parts.slice(1).join('');
            }
            
            return numericValue;
        };

        /**
         * Handle quantity change with validation
         */
        const handleQtyChange = (value) => {
            const validatedQty = validateNumericInput(value);
            setQty(validatedQty);
        };

        /**
         * Handle price change with validation
         */
        const handlePriceChange = (value) => {
            const validatedPrice = validateNumericInput(value);
            setPrice(validatedPrice);
        };

        // Calculate amount when qty and price change
        useEffect(() => {
            if (qty && price) {
                const calculatedAmount = (parseFloat(qty) * parseFloat(price)).toFixed(2);
                setAmount(calculatedAmount);
                setFinalAmount(calculatedAmount);
            } else {
                setAmount('');
                setFinalAmount('');
            }
        }, [qty, price]);

        /**
         * Handles actions from the ScrollOptionsBar
         */
        const handleOptionPress = async (option) => {
            setSelectedScrollOption(option);
            setIsLoading(true);

            try {
                switch (option) {
                    case 'New':
                        resetForm();
                        break;
                    case 'Save':
                        await saveReceipt();
                        break;
                    case 'View':
                        break;
                    case 'Cancel':
                        resetForm();
                        break;
                    case 'Post':
                        await postReceipt();
                        break;
                    default:
                        break;
                }
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Resets all form fields to their initial state
         */
        const resetForm = () => {
            setDocNumber('');
            setBusinessDate('');
            setMobile('');
            setCustomerName('');
            setCustID('');
            setSelectedCustomer(null);
            setRemarks('');
            setItemName('');
            setItemID('');
            setQty('');
            setPrice('');
            setWalletPointsData([]);
            setAmount('');
            setFinalAmount('');
            setPointsPerQty('');
            setTotalPoints('');
            // NEW: Reset wallet points
            setWalletPointsData([]);
            setReceiptMode('');
            setChequeNumber('');
            setBankName('');
            setChequeDate('');
            setItems([]);
            setSelectedRows([]);
            setIsSelectAllItems(false);
            setTotalNos('');
            setTotalAmount('');
            setTax('');
            setGrandTotal('');
            setCustomerData([]);
            setFilteredCustomers([]);
        };

        /**
         * Enhanced validation function for all form fields
         */
        const validateForm = () => {
            const errors = [];
            
            // Customer validation
            if (!customerName || customerName.trim().length === 0) {
                errors.push('Customer name is required');
            }
            
            // Mobile validation
            if (mobile && mobile.length > 0 && mobile.length !== 10) {
                errors.push('Mobile number must be exactly 10 digits');
            }
            
            // Business date validation
            if (!businessDate) {
                errors.push('Business date is required');
            }
            
            // Items validation
            if (items.length === 0) {
                errors.push('Please add at least one item');
            }
            
            // Receipt mode validation
            if (!receiptMode) {
                errors.push('Receipt mode is required');
            }
            
            return errors;
        };

        /**
         * Enhanced item validation function
         */
        const validateItemFields = () => {
            const errors = [];
            
            if (!itemName || itemName.trim().length === 0) {
                errors.push('Item name is required');
            }
            
            if (!qty || qty.trim().length === 0) {
                errors.push('Quantity is required');
            } else if (isNaN(qty) || parseFloat(qty) <= 0) {
                errors.push('Quantity must be a positive number');
            }
            
            if (!price || price.trim().length === 0) {
                errors.push('Price is required');
            } else if (isNaN(price) || parseFloat(price) <= 0) {
                errors.push('Price must be a positive number');
            }
            
            // Payment details validation based on receipt mode
            if (receiptMode && !['Cash', 'CAASH'].includes(receiptMode)) {
                if (receiptMode === 'UPI') {
                    // UPI requires reference number
                    if (!chequeNumber || chequeNumber.trim().length === 0) {
                        errors.push('Reference number is required for UPI payments');
                    }
                } else if (receiptMode === 'Cheque/Draft') {
                    // Cheque/Draft requires cheque number and date
                    if (!chequeNumber || chequeNumber.trim().length === 0) {
                        errors.push('Cheque/Draft number is required');
                    }
                    if (!chequeDate) {
                        errors.push('Cheque/Draft date is required');
                    }
                    if (!bankName || bankName.trim().length === 0) {
                        errors.push('Bank name is required for Cheque/Draft payments');
                    }
                } else {
                    // Other payment modes require all fields
                    if (!bankName || bankName.trim().length === 0) {
                        errors.push('Bank name is required');
                    }
                    if (!chequeNumber || chequeNumber.trim().length === 0) {
                        errors.push('Reference number is required');
                    }
                    if (!chequeDate) {
                        errors.push('Transaction date is required');
                    }
                }
            }
            
            return errors;
        };

        /**
         * Validates and saves the receipt
         */
        const saveReceipt = async () => {
            const validationErrors = validateForm();
            
            if (validationErrors.length > 0) {
                Alert.alert(
                    'Validation Error', 
                    validationErrors.join('\n'),
                    [{ text: 'OK', style: 'default' }]
                );
                return;
            }
            
            setIsLoading(true);
            const success = await saveReceiptToAPI({});
            setIsLoading(false);

            if (success) {
                // Reset the entire screen after successful save
                resetForm();
            }
        };

        /**
         * Posts the receipt
         */
        const postReceipt = async () => {
            const validationErrors = validateForm();
            
            if (validationErrors.length > 0) {
                Alert.alert(
                    'Validation Error', 
                    validationErrors.join('\n'),
                    [{ text: 'OK', style: 'default' }]
                );
                return;
            }
            
            setIsLoading(true);
            try {
                await new Promise(resolve => setTimeout(resolve, 2000));
                Alert.alert('Success', 'Receipt posted successfully');
            } catch (error) {
                Alert.alert('Error', 'Failed to post receipt');
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Toggles selection of a table row
         */
        const handleRowSelection = (index) => {
            if (selectedRows.includes(index)) {
                setSelectedRows(selectedRows.filter(i => i !== index));
                setIsSelectAllItems(false);
            } else {
                setSelectedRows([...selectedRows, index]);
                if (selectedRows.length + 1 === items.length) {
                    setIsSelectAllItems(true);
                }
            }
        };

        /**
         * Removes selected rows from the items table
         */
        const handleDeleteSelectedRows = () => {
            if (selectedRows.length === 0) {
                Alert.alert('Info', 'No rows selected for deletion');
                return;
            }
            const newItems = items.filter((_, index) => !selectedRows.includes(index));
            setItems(newItems);
            setSelectedRows([]);
            setIsSelectAllItems(false);
            calculateTotals(newItems);
        };

        /**
         * Validates and adds a new item to the table
         */
        const handleAddItem = async () => {
            const validationErrors = validateItemFields();
            
            if (validationErrors.length > 0) {
                Alert.alert(
                    'Validation Error', 
                    validationErrors.join('\n'),
                    [{ text: 'OK', style: 'default' }]
                );
                return;
            }

            setIsLoading(true);

            try {
                await new Promise(resolve => setTimeout(resolve, 1000));

                const newItem = {
                    set: items.length + 1,
                    itemID: itemID,
                    itemName: itemName,
                    return: '',
                    kas: qty,
                    batchNumber: '',
                    rate: price,
                    discount: '0',
                    disc: finalAmount
                };

                const updatedItems = [...items, newItem];
                setItems(updatedItems);
                calculateTotals(updatedItems);

                // Reset item details section only (keep payment details for API call)
                setItemName('');
                setItemID('');
                setQty('');
                setPrice('');
                setAmount('');
                setFinalAmount('');
                setPointsPerQty('');
                setTotalPoints('');
                setWalletPointsData([]);
                // Don't reset payment details - they should persist for the API call
                console.log('🔍 Payment details preserved after adding item:', {
                    receiptMode: receiptMode,
                    bankName: bankName,
                    chequeNumber: chequeNumber,
                    chequeDate: chequeDate
                });
                // setReceiptMode('');
                // setChequeNumber('');
                // setBankName('');
                // setChequeDate('');
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Calculate totals based on items
         */
        const calculateTotals = (itemsList) => {
            const totalQty = itemsList.reduce((sum, item) => sum + parseFloat(item.kas || 0), 0);
            const totalAmt = itemsList.reduce((sum, item) => sum + parseFloat(item.disc || 0), 0);
            const taxAmount = totalAmt * 0.18;
            const grandTotalAmt = totalAmt + taxAmount;

            setTotalNos(totalQty.toString());
            setTotalAmount(totalAmt.toFixed(2));
            setTax(taxAmount.toFixed(2));
            setGrandTotal(grandTotalAmt.toFixed(2));
        };

        /**
         * Opens selection modal with appropriate data
         */
        const openModal = async (type) => {
            setIsLoading(true);
            setSearchTerm('');

            try {
                let dataToShow = [];
                switch (type) {
                    case 'receiptMode':
                        dataToShow = await receiptModeData();
                        break;
                    case 'itemName':
                        dataToShow = await itemNameData();
                        break;
                    default:
                        dataToShow = [];
                }

                setModalData(dataToShow);
                setFilteredModalData(dataToShow);
                setModalType(type);
                setModalVisible(true);
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Handles item selection from modal
         */
        const handleModalItemSelect = (item) => {
            switch (modalType) {
                case 'receiptMode':
                    setReceiptMode(item.PaymentGatewayName);
                    break;
                case 'itemName':
                    // NEW: Set both itemName and itemID, which will trigger wallet points fetch
                    setItemName(item.itemName);
                    setItemID(item.itemID);
                    console.log('🎯 Selected item:', item.itemName, 'with ID:', item.itemID);
                    break;
                default:
                    break;
            }
            setModalVisible(false);
        };

        /**
         * Opens calendar modal for date selection
         */
        const openCalendar = async (field) => {
            setIsLoading(true);

            try {
                await new Promise(resolve => setTimeout(resolve, 500));
                setSelectedDateField(field);
                setCalendarVisible(true);
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Handles date selection from calendar
         */
        const handleDateSelect = (day) => {
            const formattedDate = day.dateString.split('-').reverse().join('-');
            if (selectedDateField === 'businessDate') {
                setBusinessDate(formattedDate);
            } else if (selectedDateField === 'chequeDate') {
                setChequeDate(formattedDate);
            }
            setCalendarVisible(false);
        };

        /**
         * Handles customer selection
         */
        const handleCustomerSelect = (customer) => {
            console.log('🔍 Selected customer:', customer);
            setCustomerName(customer.customerName);
            setMobile(customer.mobile);
            setCustID(customer.custID);
            setSelectedCustomer(customer);
            console.log('🔍 Setting customerName:', customer.customerName, 'custID:', customer.custID);
            setIsMobileSearch(false);
            setCustomerModalVisible(false);
        };

        /**
         * Handles customer search
         */
        const handleCustomerSearch = async () => {
            if (customerSearchTerm && customerSearchTerm.length >= 2) {
                setIsCustomerSearchLoading(true);
                try {
                    console.log('🔍 Searching by CUSTOMER NAME:', customerSearchTerm);
                    const customers = await fetchCustomersByName(customerSearchTerm);
                    setFilteredCustomers(customers);
                    setCustomerData(customers);
                } finally {
                    setIsCustomerSearchLoading(false);
                }
            } else {
                setFilteredCustomers([]);
                setCustomerData([]);
            }
        };

        /**
         * Validate mobile number format
         */
        const validateMobileNumber = (value) => {
            // Allow only numeric characters
            const numericValue = value.replace(/[^0-9]/g, '');
            
            // Limit to 10 digits
            return numericValue.substring(0, 10);
        };

        /**
         * Handle mobile number change and auto-fetch customer if 10 digits
         */
        const handleMobileChange = async (value) => {
            const validatedMobile = validateMobileNumber(value);
            setMobile(validatedMobile);

            if (validatedMobile.length === 10) {
                const result = await fetchCustomerByMobile(validatedMobile);
                if (Array.isArray(result)) {
                    setCustomerData(result);
                    setFilteredCustomers(result);
                    setIsMobileSearch(true);
                    setCustomerModalVisible(true);
                }
            } else if (validatedMobile.length < 10) {
                setCustomerName('');
                setCustID('');
                setSelectedCustomer(null);
                setCustomerData([]);
                setFilteredCustomers([]);
                setIsMobileSearch(false);
            }
        };

        /**
         * Handle mobile search from modal
         */
        const handleMobileSearch = async () => {
            if (customerSearchTerm && customerSearchTerm.length === 10) {
                setIsCustomerSearchLoading(true);
                try {
                    console.log('Searching by mobile number:', customerSearchTerm);
                    const customer = await fetchCustomerByMobile(customerSearchTerm);
                    if (customer) {
                        if (!Array.isArray(customer)) {
                            console.log('🔍 Modal search - selected customer:', customer);
                            setCustomerName(customer.customerName);
                            setMobile(customer.mobile);
                            setCustID(customer.custID);
                            setSelectedCustomer(customer);
                            console.log('🔍 Modal search - setting customerName:', customer.customerName, 'custID:', customer.custID);
                            setCustomerModalVisible(false);
                        } else {
                            setFilteredCustomers(customer);
                            setCustomerData(customer);
                            setIsMobileSearch(true);
                        }
                    }
                } finally {
                    setIsCustomerSearchLoading(false);
                }
            } else {
                Alert.alert('Validation Error', 'Please enter a valid 10-digit mobile number');
            }
        };

        /**
         * Opens customer selection modal
         */
        const openCustomerModal = async () => {
            setIsLoading(true);

            try {
                await new Promise(resolve => setTimeout(resolve, 300));
                if (customerName && customerName.trim()) {
                    setCustomerSearchTerm(customerName);
                    console.log('🔍 Opening customer modal with pre-filled name:', customerName);
                    // Automatically search when there's a customer name pre-filled
                    const customers = await fetchCustomersByName(customerName);
                    setFilteredCustomers(customers);
                    setCustomerData(customers);
                } else {
                    setCustomerSearchTerm('');
                    setFilteredCustomers([]);
                    setCustomerData([]);
                }
                setIsMobileSearch(false);
                setCustomerModalVisible(true);
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Opens DocName selection modal
         */
        const openDocNameModal = async () => {
            setIsLoading(true);

            try {
                setDocNameSearchTerm('');
                setDocNameData([]);
                setFilteredDocNames([]);
                setDocNameModalVisible(true);
                await fetchDocNamesFromAPI();
            } finally {
                setIsLoading(false);
            }
        };

        /**
         * Handles DocName selection
         */
        const handleDocNameSelect = (docItem) => {
            console.log('Selected DocName:', docItem);
            setDocNumber(docItem.DocName);
            setDocNameModalVisible(false);
        };

        /**
         * Get current date in YYYY-MM-DD format for calendar
         */
        const getCurrentDate = () => {
            const today = new Date();
            return today.toISOString().split('T')[0];
        };

        /**
         * Convert DD-MM-YYYY to YYYY-MM-DD for calendar
         */
        const convertDateForCalendar = (dateString) => {
            if (!dateString) return getCurrentDate();
            const parts = dateString.split('-');
            if (parts.length === 3) {
                return `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
            return getCurrentDate();
        };

        // Utility to convert DD-MM-YYYY to YYYY-MM-DD
        const convertDDMMYYYYtoYYYYMMDD = (dateString) => {
            if (!dateString) return '';
            const parts = dateString.split('-');
            if (parts.length === 3) {
                return `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
            return dateString;
        };

        const saveReceiptToAPI = async (receiptData) => {
            if (!authToken) {
                Alert.alert('Error', 'Missing authentication token');
                return false;
            }
            try {
                const url = 'https://retailUAT.abisaio.com:9001/api/Collection2';
                const getCurrentDateISO = () => new Date().toISOString();
                // Fix: Convert DD-MM-YYYY to YYYY-MM-DD before creating ISO string
                let businessDateISO = new Date().toISOString();
                if (businessDate) {
                    let formatted = businessDate;
                    if (/^\d{2}-\d{2}-\d{4}$/.test(businessDate)) {
                        formatted = convertDDMMYYYYtoYYYYMMDD(businessDate);
                    }
                    businessDateISO = new Date(formatted).toISOString();
                }
                
                const requestData = {
                    collectionHdrid: docNumber || "",
                    branchID: selectedBranch,
                    transTypeID: "RECEIPT",
                    transSubTypeID: "SBSCR",
                    channelId: "POS",
                    businessDate: businessDateISO,
                    isO_Number: "",
                    remarks: remarks || "",
                    totalAmount: parseFloat(grandTotal) || 0,
                    posted: false,
                    deleted: "N",
                    createdDate: getCurrentDateISO(),
                    createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
                    modifiedUserID: createdUserID,
                    modifiedDate: getCurrentDateISO(),
                    deletedUserID: "",
                    deletedDate: '1755-07-11T15:10:35.810Z',
                    postedUserID: "",
                    collectionDtls: items.map((item, index) => {
                        console.log('🔍 Processing item for API:', {
                            itemIndex: index,
                            itemName: item.itemName,
                            amount: item.disc,
                            chequeNo: chequeNumber,
                            chequeDate: chequeDate,
                            bankName: bankName,
                            receiptMode: receiptMode,
                            customerName: customerName,
                            custID: custID
                        });
                        
                        return {
                            lineNumber: String(index + 1).padStart(3, '0'),
                            customerName: customerName || "",
                            amount: parseFloat(item.disc) || 0,
                            remarks: remarks || "",
                            chequeNo: chequeNumber || "",
                            chequeDate: chequeDate ? new Date(convertDDMMYYYYtoYYYYMMDD(chequeDate)).toISOString() : null,
                            bankName: bankName || "",
                            hoCustomerID: "",
                            custID: custID || "",
                            openingBalance: 0,
                            creditLimit: 0,
                            paymentGatewayName: receiptMode || "CAASH",
                            paymentGatewayID: "",
                            createdUserID: createdUserID, // Pass the CreatedUserID from AsyncStorage
                            deleted: "N",
                            dml: "I"
                        };
                    }),
                    collectionBills: [],
                    collectionCNusages: []
                };

                console.log('🚀 Saving subscription receipt with data:', requestData);
                console.log('🔍 Payment details summary:', {
                    receiptMode: receiptMode,
                    bankName: bankName,
                    chequeNumber: chequeNumber,
                    chequeDate: chequeDate,
                    totalAmount: grandTotal
                });
                console.log('🔍 Customer details being sent to API:', {
                    customerName: customerName,
                    custID: custID,
                    selectedCustomer: selectedCustomer
                });
                // Print the payload in correct format for debugging
                console.log('📦 PAYLOAD TO API:', JSON.stringify(requestData, null, 2));

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('Save subscription receipt API response:', result);
                
                // Show success message with API response and ask for print
                if (result.result === 1) {
                    Alert.alert(
                        'Success',
                        `Receipt saved successfully!\nReceipt Number: ${result.description}`,
                        [
                            {
                                text: 'Print',
                                onPress: () => {
                                    // TODO: Implement print functionality
                                    console.log('Print receipt:', result.description);
                                    Alert.alert('Print', 'Print functionality will be implemented here');
                                }
                            },
                            {
                                text: 'No',
                                style: 'cancel'
                            }
                        ]
                    );
                    return true;
                } else {
                    Alert.alert('Error', 'Failed to save receipt');
                    return false;
                }
            } catch (error) {
                console.error('Error saving subscription receipt:', error);
                Alert.alert('Error', 'Failed to save subscription receipt to server');
                return false;
            }
        };

        // Memoized fetch for item names (runs only once)
        const itemNameData = React.useMemo(() => {
            let didFetch = false;
            let cache = [];
            return async () => {
                if (!didFetch) {
                    cache = await fetchItemsFromAPI();
                    didFetch = true;
                }
                return cache;
            };
        }, [authToken, selectedBranch]);

        // Memoized fetch for receipt modes (runs only once)
        const receiptModeData = React.useMemo(() => {
            let didFetch = false;
            let cache = [];
            return async () => {
                if (!didFetch) {
                    cache = await fetchPaymentModesFromAPI();
                    didFetch = true;
                }
                return cache;
            };
        }, [authToken]);

        return (
            <View style={styles.container}>
                <Navbar />

                <ScrollOptionsBar
                    title="Receipt for Subscription"
                    selectedOption={selectedScrollOption}
                    onOptionPress={handleOptionPress}
                />

                <View style={styles.contentContainer}>
                    <View style={styles.sectionContainer}>
                        {/* Header Section */}
                        <View style={styles.formRow}>
                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Doc#</Text>
                                <View style={styles.customerInputContainer}>
                                    <TextInput
                                        style={styles.customerInput}
                                        placeholder="Docs Number"
                                        placeholderTextColor="#888"
                                        value={docNumber}
                                        onChangeText={setDocNumber}
                                        editable={false}
                                    />
                                    <TouchableOpacity
                                        style={styles.searchCustomerButton}
                                        disabled
                                        onPress={openDocNameModal}
                                    >
                                        <Icon name="search" size={20} color="white" />
                                    </TouchableOpacity>
                                </View>
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Business Date</Text>
                                <View style={styles.dateInputContainer}>
                                    <TextInput
                                        style={styles.dateInput}
                                        placeholder="Business Date"
                                        placeholderTextColor="#888"
                                        value={businessDate}
                                        onChangeText={setBusinessDate}
                                        editable={false}
                                    />
                                    <TouchableOpacity
                                        style={styles.calendarButton}
                                        onPress={() => openCalendar('businessDate')}
                                    >
                                        <Icon name="date-range" size={20} color="white" />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>

                        <View style={styles.formRow}>
                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Mobile</Text>
                                <TextInput
                                    style={styles.input}
                                    placeholder="Mobile Number"
                                    placeholderTextColor="#888"
                                    value={mobile}
                                    onChangeText={handleMobileChange}
                                    keyboardType="numeric"
                                    maxLength={10}
                                />
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Customer Name</Text>
                                <View style={styles.customerInputContainer}>
                                    <Icon name="person" size={20} color="#888" style={styles.customerIcon} />
                                    <TextInput
                                        style={styles.customerInput}
                                        placeholder="Customer Name"
                                        placeholderTextColor="#888"
                                        value={customerName}
                                        onChangeText={setCustomerName}
                                    />
                                    <TouchableOpacity
                                        style={styles.searchCustomerButton}
                                        onPress={openCustomerModal}
                                    >
                                        <Icon name="search" size={20} color="white" />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>

                        <View style={styles.fullWidthRow}>
                            <Text style={styles.labelText}>Remarks</Text>
                            <TextInput
                                style={styles.remarksInput}
                                placeholder="Add Remarks"
                                placeholderTextColor="#888"
                                value={remarks}
                                onChangeText={setRemarks}
                                multiline={false}
                            />
                        </View>

                        {/* Item Details Section */}
                        <View style={styles.sectionHeaderContainer}>
                            <Text style={styles.sectionTitle}>Item Details</Text>
                        </View>

                        {/* First Row of Item Details */}
                        <View style={styles.itemRow}>
                            <View style={styles.dropdownContainer}>
                                <Text style={styles.labelText}>Item Name</Text>
                                <TouchableOpacity
                                    style={styles.dropdown}
                                    onPress={() => openModal('itemName')}
                                >
                                    <Text style={[styles.inputText, !itemName && styles.placeholderText]}>
                                        {itemName || 'Select Item'}
                                    </Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>Qty</Text>
                                <TextInput
                                    style={styles.smallInput}
                                    placeholder="Qty"
                                    placeholderTextColor="#888"
                                    value={qty}
                                    onChangeText={handleQtyChange}
                                    keyboardType="decimal-pad"
                                />
                            </View>

                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>Price</Text>
                                <TextInput
                                    style={styles.smallInput}
                                    placeholder="Price"
                                    placeholderTextColor="#888"
                                    value={price}
                                    onChangeText={handlePriceChange}
                                    keyboardType="decimal-pad"
                                    editable={false} // Disabled
                                />
                            </View>

                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>Amount</Text>
                                <TextInput
                                    style={styles.smallInput}
                                    placeholder="Amount"
                                    placeholderTextColor="#888"
                                    value={amount}
                                    editable={false} // Disabled
                                />
                            </View>

                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>Final Amount</Text>
                                <TextInput
                                    style={styles.smallInput}
                                    placeholder="Final Amount"
                                    placeholderTextColor="#888"
                                    value={finalAmount}
                                    onChangeText={setFinalAmount}
                                    keyboardType="numeric"
                                    editable={false} // Disabled
                                />
                            </View>
                        </View>

                        {/* Second Row: Points, Receipt Mode and Add Button */}
                        <View style={styles.receiptModeAddRow}>
                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>Points / Qty</Text>
                                <View style={styles.pointsInputContainer}>
                                    <TextInput
                                        style={[
                                            styles.smallInput,
                                            isLoadingPoints && styles.loadingInput
                                        ]}
                                        placeholder="Points/Qty"
                                        placeholderTextColor="#888"
                                        value={pointsPerQty}
                                        onChangeText={setPointsPerQty}
                                        keyboardType="numeric"
                                        editable={false} // Disabled
                                    />
                                    {isLoadingPoints && (
                                        <ActivityIndicator 
                                            size="small" 
                                            color="#FDC500" 
                                            style={styles.pointsLoader}
                                        />
                                    )}
                                </View>
                            </View>

                            <View style={styles.smallInputContainer}>
                                <Text style={styles.labelText}>TOTAL points</Text>
                                <TextInput
                                    style={styles.smallInput}
                                    placeholder="Total Points"
                                    placeholderTextColor="#888"
                                    value={totalPoints}
                                    onChangeText={setTotalPoints}
                                    keyboardType="numeric"
                                    editable={false} // Disabled
                                />
                            </View>

                            <View style={styles.receiptModeContainer}>
                                <Text style={styles.labelText}>Receipt Mode</Text>
                                <TouchableOpacity
                                    style={styles.receiptModeDropdown}
                                    onPress={() => openModal('receiptMode')}
                                >
                                    <Text style={[styles.inputText, !receiptMode && styles.placeholderText]}>
                                        {receiptMode || 'Select Receipt Mode'}
                                    </Text>
                                    <Text style={styles.dropdownIcon}>▼</Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Cheque/Draft No/Ref No</Text>
                                <TextInput
                                    style={styles.input}
                                    placeholder="Cheque/Draft No/Ref No"
                                    placeholderTextColor="#888"
                                    value={chequeNumber}
                                    onChangeText={setChequeNumber}
                                />
                            </View>
                        </View>

                        {/* Payment Details Section */}
                        <View style={styles.formRow}>
                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Bank Name</Text>
                                <TextInput
                                    style={styles.input}
                                    placeholder="Bank Name"
                                    placeholderTextColor="#888"
                                    value={bankName}
                                    onChangeText={setBankName}
                                />
                            </View>

                            <View style={styles.inputContainer}>
                                <Text style={styles.labelText}>Cheque/Draft Date</Text>
                                <View style={styles.dateInputContainer}>
                                    <TextInput
                                        style={styles.dateInput}
                                        placeholder="Cheque/Draft Date"
                                        placeholderTextColor="#888"
                                        value={chequeDate}
                                        onChangeText={setChequeDate}
                                        editable={false}
                                    />
                                    <TouchableOpacity
                                        style={styles.calendarButton}
                                        onPress={() => openCalendar('chequeDate')}
                                    >
                                        <Icon name="date-range" size={20} color="white" />
                                    </TouchableOpacity>
                                </View>
                            </View>

                            <TouchableOpacity style={styles.addButton} onPress={handleAddItem}>
                                <Text style={styles.addButtonText}>Add +</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Items Table */}
                        <View style={styles.tableContainer}>
                            <View style={styles.tableHeader}>
                                <View style={styles.checkboxCell}>
                                    <CheckBox
                                        value={isSelectAllItems}
                                        onValueChange={setIsSelectAllItems}
                                        tintColors={{ true: '#FDC500', false: '#FDC500' }}
                                    />
                                </View>
                                <View style={styles.setCell}><Text style={styles.tableHeaderText}>#</Text></View>
                                <View style={styles.setCell}><Text style={styles.tableHeaderText}>Set</Text></View>
                                <View style={styles.itemCell}><Text style={styles.tableHeaderText}>Item</Text></View>
                                <View style={styles.returnCell}><Text style={styles.tableHeaderText}>Return</Text></View>
                                <View style={styles.kasCell}><Text style={styles.tableHeaderText}>Kas</Text></View>
                                <View style={styles.batchCell}><Text style={styles.tableHeaderText}>Rate</Text></View>
                                <View style={styles.discountCell}><Text style={styles.tableHeaderText}>Discount</Text></View>
                                <View style={styles.discCell}><Text style={styles.tableHeaderText}>Disc</Text></View>
                            </View>

                            <ScrollView style={styles.tableScrollView} showsVerticalScrollIndicator={true}>
                                {items.length > 0 ? (
                                    items.map((item, index) => (
                                        <View key={index} style={styles.tableRow}>
                                            <View style={styles.checkboxCell}>
                                                <CheckBox
                                                    value={selectedRows.includes(index)}
                                                    onValueChange={() => handleRowSelection(index)}
                                                    tintColors={{ true: '#02096A', false: '#999' }}
                                                />
                                            </View>
                                            <View style={styles.setCell}><Text style={styles.tableCell}>{index + 1}</Text></View>
                                            <View style={styles.setCell}><Text style={styles.tableCell}>{item.set}</Text></View>
                                            <View style={styles.itemCell}><Text style={styles.tableCell}>{item.itemName}</Text></View>
                                            <View style={styles.returnCell}><Text style={styles.tableCell}>{item.return}</Text></View>
                                            <View style={styles.kasCell}><Text style={styles.tableCell}>{item.kas}</Text></View>
                                            <View style={styles.batchCell}><Text style={styles.tableCell}>{item.batchNumber}</Text></View>
                                            <View style={styles.rateCell}><Text style={styles.tableCell}>{item.rate}</Text></View>
                                            <View style={styles.discountCell}><Text style={styles.tableCell}>{item.discount}</Text></View>
                                            <View style={styles.discCell}><Text style={styles.tableCell}>{item.disc}</Text></View>
                                        </View>
                                    ))
                                ) : (
                                    <View style={styles.emptyTableRow}>
                                        <Text style={styles.emptyTableText}>No Data</Text>
                                    </View>
                                )}
                            </ScrollView>
                        </View>

                        {/* Summary Section */}
                        <View style={styles.summaryRow}>
                            <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                                <Text style={styles.deleteButtonText}>Delete selected</Text>
                            </TouchableOpacity>

                            <View style={styles.summaryInputContainer}>
                                <Text style={styles.labelText}>Nos</Text>
                                <TextInput
                                    style={styles.summaryInput}
                                    placeholder="Nos"
                                    placeholderTextColor="#888"
                                    value={totalNos}
                                    editable={false}
                                />
                            </View>

                            <View style={styles.summaryInputContainer}>
                                <Text style={styles.labelText}>Amount</Text>
                                <TextInput
                                    style={styles.summaryInput}
                                    placeholder="Amount"
                                    placeholderTextColor="#888"
                                    value={totalAmount}
                                    editable={false}
                                />
                            </View>

                            <View style={styles.summaryInputContainer}>
                                <Text style={styles.labelText}>Tax</Text>
                                <TextInput
                                    style={styles.summaryInput}
                                    placeholder="Tax"
                                    placeholderTextColor="#888"
                                    value={tax}
                                    editable={false}
                                />
                            </View>

                            <View style={styles.summaryInputContainer}>
                                <Text style={styles.labelText}>Total</Text>
                                <TextInput
                                    style={styles.summaryInput}
                                    placeholder="Total"
                                    placeholderTextColor="#888"
                                    value={grandTotal}
                                    editable={false}
                                />
                            </View>
                        </View>
                    </View>
                </View>

                {/* Modal Component */}
                <ReceiptModals
                    // Selection Modal Props
                    modalVisible={modalVisible}
                    setModalVisible={setModalVisible}
                    modalType={modalType}
                    modalData={modalData}
                    filteredModalData={filteredModalData}
                    searchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    isLoadingItems={isLoadingItems}
                    handleModalItemSelect={handleModalItemSelect}

                    // Customer Modal Props
                    customerModalVisible={customerModalVisible}
                    setCustomerModalVisible={setCustomerModalVisible}
                    customerSearchTerm={customerSearchTerm}
                    setCustomerSearchTerm={setCustomerSearchTerm}
                    filteredCustomers={filteredCustomers}
                    handleCustomerSelect={handleCustomerSelect}
                    handleCustomerSearch={handleCustomerSearch}
                    handleMobileSearch={handleMobileSearch}
                    isMobileSearch={isMobileSearch}
                    mobile={mobile}
                    isCustomerSearchLoading={isCustomerSearchLoading}

                    // Calendar Modal Props
                    calendarVisible={calendarVisible}
                    setCalendarVisible={setCalendarVisible}
                    handleDateSelect={handleDateSelect}
                    convertDateForCalendar={convertDateForCalendar}
                    selectedDateField={selectedDateField}
                    businessDate={businessDate}
                    chequeDate={chequeDate}

                    // DocName Modal Props
                    docNameModalVisible={docNameModalVisible}
                    setDocNameModalVisible={setDocNameModalVisible}
                    docNameSearchTerm={docNameSearchTerm}
                    setDocNameSearchTerm={setDocNameSearchTerm}
                    filteredDocNames={filteredDocNames}
                    isLoadingDocNames={isLoadingDocNames}
                    handleDocNameSelect={handleDocNameSelect}
                />

                {isLoading && (
                    <View style={styles.loaderOverlay}>
                        <View style={styles.loaderContainer}>
                            <ActivityIndicator size="large" color="#FDC500" />
                            <Text style={styles.loaderText}>Loading...</Text>
                        </View>
                    </View>
                )}
            </View>
        );
    };

    const styles = StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: '#f8f9fa',
        },
        contentContainer: {
            flex: 1,
            padding: 8,
        },
        sectionContainer: {
            backgroundColor: '#ffffff',
            borderRadius: 12,
            padding: 12,
            marginHorizontal: 6,
            marginVertical: 4,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 3,
            },
            shadowOpacity: 0.12,
            shadowRadius: 4,
            elevation: 5,
            borderWidth: 1,
            borderColor: '#e3f2fd',
        },
        sectionHeaderContainer: {
            backgroundColor: '#f5f5f5',
            padding: 8,
            marginVertical: 6,
            borderRadius: 8,
            borderLeftWidth: 4,
            borderLeftColor: '#3f51b5',
        },
        sectionTitle: {
            fontSize: 18,
            fontWeight: '700',
            color: '#2c3e50',
            textAlign: 'left',
            fontFamily: 'Poppins',
            letterSpacing: 0.5,
            marginBottom: 0,
        },
        formRow: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
            marginBottom: 10,
            gap: 8,
        },
        fullWidthRow: {
            marginBottom: 10,
        },
        itemRow: {
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 8,
            marginBottom: 10,
        },
        receiptModeAddRow: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
            marginBottom: 10,
            gap: 10,
        },
        receiptModeContainer: {
            flex: 1,
            minWidth: 220, // Increased width for Receipt Mode
            maxWidth: 320,
        },
        receiptModeDropdown: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 18,
            paddingVertical: 8,
            height: 56,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        inputContainer: {
            flex: 1,
            minWidth: 150,
        },
        labelText: {
            fontSize: 13,
            color: '#2c3e50',
            marginBottom: 6,
            fontFamily: 'Poppins',
            fontWeight: '600',
            letterSpacing: 0.3,
        },
        // NEW: Points input container styles
        pointsInputContainer: {
            position: 'relative',
        },
        loadingInput: {
            opacity: 0.7,
        },
        pointsLoader: {
            position: 'absolute',
            right: 8,
            top: '50%',
            marginTop: -10,
        },
        loaderOverlay: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999,
        },
        loaderContainer: {
            backgroundColor: 'white',
            padding: 30,
            borderRadius: 15,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
        },
        loaderText: {
            marginTop: 15,
            fontSize: 16,
            color: '#333',
            fontFamily: 'Poppins',
            fontWeight: 'bold',
        },
        input: {
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 8,
            height: 56,
            fontFamily: 'Poppins',
            fontSize: 16,
            color: '#2c3e50',
            fontWeight: '600',
            textAlignVertical: 'center',
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        remarksInput: {
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 12,
            minHeight: 56,
            fontFamily: 'Poppins',
            fontSize: 16,
            color: '#2c3e50',
            textAlignVertical: 'top',
            fontWeight: '600',
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        dateInputContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        dateInput: {
            flex: 1,
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 16,
            paddingVertical: 8,
            height: 56,
            fontFamily: 'Poppins',
            fontSize: 16,
            color: '#2c3e50',
            fontWeight: '600',
            textAlignVertical: 'center',
            marginRight: 10,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        calendarButton: {
            backgroundColor: '#3f51b5',
            borderRadius: 12,
            padding: 16,
            height: 56,
            width: 56,
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 3,
        },
        customerInputContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingLeft: 16,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        customerIcon: {
            marginRight: 8,
        },
        customerInput: {
            flex: 1,
            paddingHorizontal: 16,
            paddingVertical: 8,
            height: 56,
            fontFamily: 'Poppins',
            fontSize: 16,
            color: '#2c3e50',
            fontWeight: '600',
            textAlignVertical: 'center',
        },
        searchCustomerButton: {
            backgroundColor: '#3f51b5',
            borderRadius: 12,
            padding: 16,
            height: 56,
            width: 56,
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: 10,
            marginRight: 10,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 3,
        },
        dropdownContainer: {
            flex: 0.9, // Increased width for Item Name
            minWidth: 180,
            maxWidth: 320,
        },
        dropdown: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 18,
            paddingVertical: 8,
            height: 56,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        inputText: {
            color: '#2c3e50',
            fontFamily: 'Poppins',
            fontSize: 16,
            fontWeight: '600',
        },
        placeholderText: {
            color: '#999',
            fontWeight: '400',
        },
        dropdownIcon: {
            color: '#3f51b5',
            fontSize: 16,
            fontWeight: 'bold',
        },
        smallInputContainer: {
            minWidth: 110, // Increased width for Qty, Price, Amount, Final Amount, Points/Qty, Total Points
            maxWidth: 160,
        },
        smallInput: {
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 12,
            paddingHorizontal: 8,
            paddingVertical: 8,
            height: 56,
            fontFamily: 'Poppins',
            fontSize: 16,
            color: '#2c3e50',
            textAlign: 'center',
            textAlignVertical: 'center',
            fontWeight: '600',
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.05,
            shadowRadius: 2,
            elevation: 2,
        },
        addButton: {
            backgroundColor: '#4caf50',
            paddingVertical: 16,
            paddingHorizontal: 24,
            borderRadius: 12,
            alignItems: 'center',
            justifyContent: 'center',
            height: 56,
            minWidth: 120,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.15,
            shadowRadius: 3,
            elevation: 4,
        },
        addButtonText: {
            color: '#ffffff',
            fontWeight: '700',
            fontSize: 16,
            fontFamily: 'Poppins',
            letterSpacing: 0.3,
        },
        deleteButton: {
            backgroundColor: '#f44336',
            paddingVertical: 16,
            paddingHorizontal: 24,
            borderRadius: 12,
            alignItems: 'center',
            justifyContent: 'center',
            height: 56,
            minWidth: 140,
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.15,
            shadowRadius: 3,
            elevation: 4,
        },
        deleteButtonText: {
            color: '#ffffff',
            fontWeight: '700',
            fontSize: 16,
            fontFamily: 'Poppins',
            letterSpacing: 0.3,
        },
        tableContainer: {
            backgroundColor: '#ffffff',
            borderRadius: 12,
            overflow: 'hidden',
            marginBottom: 8,
            borderWidth: 1,
            borderColor: '#e3f2fd',
            shadowColor: '#000',
            shadowOffset: {
                width: 0,
                height: 3,
            },
            shadowOpacity: 0.12,
            shadowRadius: 4,
            elevation: 5,
            maxHeight: 260,
        },
        tableScrollView: {
            maxHeight: 190,
        },
        tableHeader: {
            flexDirection: 'row',
            backgroundColor: '#3f51b5',
            borderBottomWidth: 2,
            borderBottomColor: '#1a237e',
            paddingVertical: 12,
            paddingHorizontal: 8,
            minHeight: 50,
            alignItems: 'center',
        },
        tableHeaderText: {
            fontWeight: '700',
            fontSize: 13,
            color: '#ffffff',
            fontFamily: 'Poppins',
            textAlign: 'center',
            letterSpacing: 0.5,
            textTransform: 'uppercase',
            lineHeight: 16,
            textShadowColor: 'rgba(0, 0, 0, 0.3)',
            textShadowOffset: { width: 0, height: 1 },
            textShadowRadius: 2,
        },
        tableRow: {
            flexDirection: 'row',
            borderBottomWidth: 1,
            borderBottomColor: '#e8eaf6',
            paddingVertical: 12,
            paddingHorizontal: 8,
            minHeight: 48,
            backgroundColor: '#ffffff',
            alignItems: 'center',
        },
        tableCell: {
            fontSize: 13,
            color: '#2c3e50',
            fontFamily: 'Poppins',
            textAlign: 'center',
            fontWeight: '600',
            lineHeight: 18,
        },
        checkboxCell: {
            width: 30,
            justifyContent: 'center',
            alignItems: 'center',
        },
        setCell: {
            width: 40,
            justifyContent: 'center',
            alignItems: 'center',
        },
        itemCell: {
            flex: 2,
            justifyContent: 'center',
            alignItems: 'center',
        },
        returnCell: {
            width: 60,
            justifyContent: 'center',
            alignItems: 'center',
        },
        kasCell: {
            width: 50,
            justifyContent: 'center',
            alignItems: 'center',
        },
        batchCell: {
            width: 80,
            justifyContent: 'center',
            alignItems: 'center',
        },
        rateCell: {
            width: 60,
            justifyContent: 'center',
            alignItems: 'center',
        },
        discountCell: {
            width: 70,
            justifyContent: 'center',
            alignItems: 'center',
        },
        discCell: {
            width: 60,
            justifyContent: 'center',
            alignItems: 'center',
        },
        emptyTableRow: {
            padding: 20,
            alignItems: 'center',
            justifyContent: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#ddd',
        },
        emptyTableText: {
            color: '#999',
            fontFamily: 'Poppins',
            fontSize: 14,
            fontStyle: 'italic',
        },
        summaryRow: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
            marginTop: 8,
            gap: 8,
        },
        summaryInputContainer: {
            minWidth: 70,
            maxWidth: 100,
        },
        summaryInput: {
            backgroundColor: '#ffffff',
            borderWidth: 2,
            borderColor: '#e3f2fd',
            borderRadius: 8,
            paddingHorizontal: 6,
            paddingVertical: 4,
            height: 36,
            fontFamily: 'Poppins',
            fontSize: 13,
            color: '#2c3e50',
            textAlign: 'center',
            textAlignVertical: 'center',
            fontWeight: '600',
        },
    });