import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Modal,
    ActivityIndicator,
} from 'react-native';
import { Calendar } from 'react-native-calendars';

/**
 * ReceiptModals Component
 * 
 * Contains all modal components used in the Receipt for Subscription screen.
 * Includes Selection Modal, Customer Modal, Calendar Modal, and DocName Modal.
 */
const ReceiptModals = ({
    // Selection Modal Props
    modalVisible,
    setModalVisible,
    modalType,
    modalData,
    filteredModalData,
    searchTerm,
    setSearchTerm,
    isLoadingItems,
    handleModalItemSelect,
    
    // Customer Modal Props
    customerModalVisible,
    setCustomerModalVisible,
    customerSearchTerm,
    setCustomerSearchTerm,
    filteredCustomers,
    handleCustomerSelect,
    handleCustomerSearch,
    handleMobileSearch, // Add this prop
    isMobileSearch, // Add this prop
    mobile, // Add this prop
    isCustomerSearchLoading, // Add loading state for customer search
    
    // Calendar Modal Props
    calendarVisible,
    setCalendarVisible,
    handleDateSelect,
    convertDateForCalendar,
    selectedDateField,
    businessDate,
    chequeDate,
    
    // DocName Modal Props
    docNameModalVisible,
    setDocNameModalVisible,
    docNameSearchTerm,
    setDocNameSearchTerm,
    filteredDocNames,
    isLoadingDocNames,
    handleDocNameSelect,
}) => {
    
    // Local state for search mode toggle
    const [searchMode, setSearchMode] = useState('name'); // 'name' or 'mobile'
    
    // Function to determine if search term is mobile number (10 digits)
    const isMobileNumber = (searchTerm) => {
        return /^\d{10}$/.test(searchTerm);
    };
    

    // Enhanced search handler
    const handleSearchPress = () => {
        if (searchMode === 'mobile') {
            handleMobileSearch();
        } else {
            handleCustomerSearch();
        }
    };

    // Handle search mode toggle
    const handleSearchModeToggle = (newMode) => {
        setSearchMode(newMode);
        setCustomerSearchTerm(''); // Clear search term when switching modes
    };

    return (
        <>
            {/* Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'receiptMode' ? 'Select Receipt Mode' : 
                             modalType === 'itemName' ? 'Select Item' : 'Select Option'}
                        </Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {isLoadingItems ? (
                                    <View style={styles.loadingContainer}>
                                        <Text style={styles.loadingText}>Loading items...</Text>
                                    </View>
                                ) : (
                                    filteredModalData.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.modalItem}
                                            onPress={() => handleModalItemSelect(item)}
                                        >
                                            <Text style={styles.modalItemText}>
                                                {modalType === 'itemName' ? item.itemName : 
                                                 modalType === 'receiptMode' ? item.PaymentGatewayName : item}
                                            </Text>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* Customer Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={customerModalVisible}
                onRequestClose={() => setCustomerModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {isMobileSearch ? `Select Customer (Mobile: ${mobile})` : 'Select Customer'}
                        </Text>
                        
                        {/* Search Mode Toggle */}
                        <View style={styles.searchModeToggleContainer}>
                            <TouchableOpacity 
                                style={[
                                    styles.toggleButton,
                                    searchMode === 'name' && styles.toggleButtonActive
                                ]}
                                onPress={() => handleSearchModeToggle('name')}
                            >
                                <Text style={[
                                    styles.toggleButtonText,
                                    searchMode === 'name' && styles.toggleButtonTextActive
                                ]}>
                                    Customer Name
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity 
                                style={[
                                    styles.toggleButton,
                                    searchMode === 'mobile' && styles.toggleButtonActive
                                ]}
                                onPress={() => handleSearchModeToggle('mobile')}
                            >
                                <Text style={[
                                    styles.toggleButtonText,
                                    searchMode === 'mobile' && styles.toggleButtonTextActive
                                ]}>
                                    Mobile Number
                                </Text>
                            </TouchableOpacity>
                        </View>
                        
                        {/* Search Information */}
                        <View style={styles.searchInfoContainer}>
                            <Text style={styles.searchInfoText}>
                                {searchMode === 'mobile' ? 
                                    'Enter 10-digit mobile number to search' : 
                                    'Enter customer name to search'}
                            </Text>
                        </View>
                        
                        <View style={styles.customerSearchContainer}>
                            <TextInput
                                style={styles.customerSearchInput}
                                placeholder={searchMode === 'mobile' ? 
                                    "Enter mobile number" : "Enter customer name"}
                                placeholderTextColor="#888"
                                value={customerSearchTerm}
                                onChangeText={setCustomerSearchTerm}
                                keyboardType={searchMode === 'mobile' ? "numeric" : "default"}
                                maxLength={searchMode === 'mobile' ? 10 : undefined}
                            />
                            <TouchableOpacity 
                                style={[
                                    styles.searchButton,
                                    (!customerSearchTerm || 
                                     (searchMode === 'mobile' ? customerSearchTerm.length !== 10 : customerSearchTerm.length < 2) ||
                                     isCustomerSearchLoading) && styles.disabledButton
                                ]}
                                onPress={handleSearchPress}
                                disabled={!customerSearchTerm || 
                                         (searchMode === 'mobile' ? customerSearchTerm.length !== 10 : customerSearchTerm.length < 2) ||
                                         isCustomerSearchLoading}
                            >
                                {isCustomerSearchLoading ? (
                                    <ActivityIndicator size="small" color="white" />
                                ) : (
                                    <Text style={styles.searchButtonText}>
                                        {searchMode === 'mobile' ? 'Search Mobile' : 'Search Name'}
                                    </Text>
                                )}
                            </TouchableOpacity>
                        </View>
                        
                        {/* Results count for mobile search */}
                        {isMobileSearch && filteredCustomers.length > 1 && (
                            <View style={styles.resultsCountContainer}>
                                <Text style={styles.resultsCountText}>
                                    {filteredCustomers.length} customers found with this mobile number
                                </Text>
                            </View>
                        )}
                        
                        <ScrollView style={styles.customerScrollView}>
                            <View style={styles.customerItemsContainer}>
                                {isCustomerSearchLoading ? (
                                    <View style={styles.customerLoadingContainer}>
                                        <ActivityIndicator size="large" color="#02096A" />
                                        <Text style={styles.customerLoadingText}>
                                            Searching customers...
                                        </Text>
                                    </View>
                                ) : filteredCustomers.length > 0 ? (
                                    filteredCustomers.map((customer, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.customerItem}
                                            onPress={() => handleCustomerSelect(customer)}
                                        >
                                            <Text style={styles.customerItemText}>
                                                {customer.customerName}
                                            </Text>
                                            <Text style={styles.customerMobileText}>
                                                {customer.mobile}
                                            </Text>
                                            {customer.custID && (
                                                <Text style={styles.customerIdText}>
                                                    ID: {customer.custID}
                                                </Text>
                                            )}
                                        </TouchableOpacity>
                                    ))
                                ) : (
                                    <View style={styles.noDataContainer}>
                                        <Text style={styles.noDataText}>
                                            {customerSearchTerm ? 
                                                (searchMode === 'mobile' ? 
                                                    'No customers found with this mobile number' : 
                                                    'No customers found with this name') : 
                                                `Enter ${searchMode === 'mobile' ? 'mobile number' : 'customer name'} to search`}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCustomerModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* Calendar Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={calendarVisible}
                onRequestClose={() => setCalendarVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.calendarModalContent}>
                        <Text style={styles.modalTitle}>Select Date</Text>
                        
                        <Calendar
                            onDayPress={handleDateSelect}
                            markedDates={{
                                [convertDateForCalendar(selectedDateField === 'businessDate' ? businessDate : chequeDate)]: {
                                    selected: true,
                                    selectedColor: '#FDC500'
                                }
                            }}
                            theme={{
                                backgroundColor: '#ffffff',
                                calendarBackground: '#ffffff',
                                textSectionTitleColor: '#02096A',
                                selectedDayBackgroundColor: '#FDC500',
                                selectedDayTextColor: '#000000',
                                todayTextColor: '#02096A',
                                dayTextColor: '#2d4150',
                                textDisabledColor: '#d9e1e8',
                                dotColor: '#FDC500',
                                selectedDotColor: '#ffffff',
                                arrowColor: '#02096A',
                                disabledArrowColor: '#d9e1e8',
                                monthTextColor: '#02096A',
                                indicatorColor: '#02096A',
                                textDayFontFamily: 'Poppins',
                                textMonthFontFamily: 'Poppins',
                                textDayHeaderFontFamily: 'Poppins',
                                textDayFontWeight: 'bold',
                                textMonthFontWeight: 'bold',
                                textDayHeaderFontWeight: 'bold',
                                textDayFontSize: 14,
                                textMonthFontSize: 16,
                                textDayHeaderFontSize: 12
                            }}
                        />
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCalendarVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* DocName Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={docNameModalVisible}
                onRequestClose={() => setDocNameModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Select Document</Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search Document"
                                placeholderTextColor="#888"
                                value={docNameSearchTerm}
                                onChangeText={setDocNameSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {isLoadingDocNames ? (
                                    <View style={styles.loadingContainer}>
                                        <Text style={styles.loadingText}>Loading documents...</Text>
                                    </View>
                                ) : (
                                    filteredDocNames.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.modalItem}
                                            onPress={() => handleDocNameSelect(item)}
                                        >
                                            <Text style={styles.modalItemText}>
                                                {item.DocName}
                                            </Text>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setDocNameModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    // Search Info Styles
    searchInfoContainer: {
        backgroundColor: '#f0f8ff',
        padding: 10,
        borderRadius: 8,
        marginBottom: 10,
        borderLeftWidth: 4,
        borderLeftColor: '#02096A',
    },
    searchInfoText: {
        fontSize: 12,
        color: '#02096A',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    // Customer Modal Styles
    customerSearchContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerSearchInput: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    searchButton: {
        backgroundColor: '#02096A',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        minWidth: 100,
    },
    disabledButton: {
        backgroundColor: '#ccc',
        borderColor: '#999',
    },
    searchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        fontSize: 12,
        textAlign: 'center',
    },
    // Results Count Styles
    resultsCountContainer: {
        backgroundColor: '#e8f5e8',
        padding: 8,
        borderRadius: 6,
        marginBottom: 10,
        alignItems: 'center',
    },
    resultsCountText: {
        fontSize: 12,
        color: '#2d5a2d',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    // Customer Items Styles
    customerScrollView: {
        maxHeight: 300,
    },
    customerItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    customerItem: {
        backgroundColor: '#FDC500',
        width: 140,
        minHeight: 100,
        padding: 10,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    customerItemText: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
        marginBottom: 4,
    },
    customerMobileText: {
        fontSize: 12,
        color: '#666',
        fontFamily: 'Poppins',
        textAlign: 'center',
        marginBottom: 2,
    },
    customerIdText: {
        fontSize: 10,
        color: '#888',
        fontFamily: 'Poppins',
        textAlign: 'center',
        fontStyle: 'italic',
    },
    noDataContainer: {
        width: '100%',
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    noDataText: {
        fontSize: 14,
        color: '#888',
        fontFamily: 'Poppins',
        fontStyle: 'italic',
        textAlign: 'center',
        lineHeight: 20,
    },
    // Regular Modal Items
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    // Loading styles
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        fontSize: 16,
        color: '#888',
        fontFamily: 'Poppins',
        fontStyle: 'italic',
    },
    // Customer Loading styles
    customerLoadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 40,
        minHeight: 200,
    },
    customerLoadingText: {
        fontSize: 16,
        color: '#02096A',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
        marginTop: 15,
        textAlign: 'center',
    },
    // Calendar Modal Styles
    calendarModalContent: {
        width: '90%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    // Search Mode Toggle Styles
    searchModeToggleContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginBottom: 15,
        paddingVertical: 10,
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        marginHorizontal: 5,
    },
    toggleButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 25,
        borderWidth: 2,
        borderColor: '#ddd',
        backgroundColor: 'white',
        minWidth: 120,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    toggleButtonActive: {
        backgroundColor: '#02096A',
        borderColor: '#FDC500',
        shadowColor: '#02096A',
        shadowOpacity: 0.3,
    },
    toggleButtonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#666',
        fontFamily: 'Poppins',
    },
    toggleButtonTextActive: {
        color: 'white',
        fontWeight: 'bold',
    },
});

export default ReceiptModals;