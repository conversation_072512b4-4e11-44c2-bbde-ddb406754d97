import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { getPaymentMethodsAPI } from '../../apiHandling/BillingAPI/PaymentMethodAPI';
import { saveBillingData } from '../../apiHandling/BillingAPI/SaveBillingAPI'; // <--- Import your API function
import { fetchBillsByDateNew } from '../../apiHandling/BillingAPI/fetchBillsAPI';
import { fetchBillDetails } from '../../apiHandling/BillingAPI/fetchBillDetailsAPI';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BillingPaymentScreen = () => {
    const navigation = useNavigation();

    // Payment methods state
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
    const [utrNo, setUtrNo] = useState('');
    const [amountReceived, setAmountReceived] = useState('');

    const route = useRoute();
    const {
        customerId,
        customerName,
        customerMobile,
        selectedSaleType,
        selectedBusinessChannel,
        selectedDeliveryMode,
        selectedTripId,
        itemTableDetails,
        summary,
        total,
    } = route.params;

    useEffect(() => {
        const fetchPaymentMethods = async () => {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;
            const result = await getPaymentMethodsAPI(loginBranchID, bearerToken);
            if (Array.isArray(result)) {
                setPaymentMethods(result);
            } else {
                setPaymentMethods([]);
            }
        };
        fetchPaymentMethods();
    }, []);

    // Function to fetch latest bill and navigate to view screen
    const fetchLatestBillAndNavigate = async () => {
  try {
    const bearerToken = await AsyncStorage.getItem('authToken');
    const selectedBranch = await AsyncStorage.getItem('selectedBranch');
    const parsedBranch = JSON.parse(selectedBranch);
    const loginBranchID = parsedBranch.BranchId;

    // Get business date
    const businessDate = await fetchBusinessDay(bearerToken, loginBranchID);
    const [day, month, year] = businessDate.split('-');
    const from = new Date(`${year}-${month}-${day}`);
    const to = new Date(from);
    to.setDate(to.getDate() + 1);

    const fromApiDate = `${year}${month}${day}`;
    const toApiDate = `${to.getFullYear()}${String(to.getMonth() + 1).padStart(2, '0')}${String(to.getDate()).padStart(2, '0')}`;

    // Fetch bills and sort by SaleId
    const bills = await fetchBillsByDateNew(loginBranchID, 'PL', fromApiDate, toApiDate, bearerToken);
    const sortedBills = bills.sort((a, b) => {
      const saleIdA = parseInt(a.SaleId) || 0;
      const saleIdB = parseInt(b.SaleId) || 0;
      return saleIdB - saleIdA;
    });

    if (sortedBills.length > 0) {
      const latestBill = sortedBills[0];

      // Fetch bill details
      const res = await fetchBillDetails(loginBranchID, latestBill.SaleId, bearerToken);

      if (res?.saleHeader?.length && res?.customerInfo?.length) {
        const saleHeader = res.saleHeader[0];
        const saleDetails = res.saleDetails || [];
        const customerInfo = res.customerInfo[0];

        const deliveryAddress =
          Array.isArray(res.deliveryAddress) && res.deliveryAddress.length > 0
            ? res.deliveryAddress[0]
            : {};

        const billingDetails =
          Array.isArray(res.billingDetails) && res.billingDetails.length > 0
            ? res.billingDetails[0]
            : {};

        // 🔹 Fetch Branch Details
        const branchIdFromSale = saleHeader.BranchId;
        const branchDetailsResponse = await axios.get(
          `https://retailuat.abisibg.com/api/v1/branchdetails`,
          {
            params: { Branchid: branchIdFromSale },
            headers: {
              Authorization: `Bearer ${bearerToken}`,
            },
          }
        );

        const branchData = branchDetailsResponse?.data?.[0] || {};

        const billData = {
          saleID: saleHeader.SaleId ?? '',
          currentTimeStamp: saleHeader.BusinessDate ?? '',
          itemTableDetails: saleDetails,

          customerID: customerInfo.CustID ?? '',
          customerName: customerInfo.CustomerName ?? '',
          customerPhoneNumber: customerInfo.Mobile ?? '',

          customerAddress: deliveryAddress.CustomerAddress ?? '',
          customerPlace: deliveryAddress.PlaceName ?? '',
          customerCity: deliveryAddress.CityName ?? '',
          customerState: deliveryAddress.StateName ?? '',
          customerPincode: deliveryAddress.PINCODE ?? '',

          deliveryCharges: saleHeader.DeliveryCharge ?? 0,
          selectedGatewayName: billingDetails.PaymentGatewayName ?? '',
          selectedCustomerType: saleHeader.CustomerType ?? '',

          taxAmount: saleHeader.TaxAmount?.toString() ?? '0',
          discountAmount: saleHeader.DiscountAmount?.toString() ?? '0',
          totalAmount: saleHeader.TotalAmount?.toString() ?? '0',
          roundOffAmount: saleHeader.RoundOffAmount?.toString() ?? '0',

          printDateTime: new Date().toISOString(),

          // 🏷️ Branch Details
          branchId: branchData.BranchId ?? '',
          branchName: branchData.BranchName ?? '',
          areaName: branchData.AreaName ?? '',
          gstNumber: branchData.GSTNumber ?? '',
          foodLicenseNumber: branchData.FoodLicenseNumber ?? '',
          branchPincode: branchData.PINCODE ?? '',
          isFromDirectPrint: true, // This is from direct print after saving
        };

        navigation.navigate('ViewBillScreen', billData);
      } else {
        Alert.alert("Error", "Failed to fetch latest bill details");
      }
    } else {
      Alert.alert("Error", "No bills found");
    }
  } catch (error) {
    console.error('Error fetching latest bill:', error);
    Alert.alert("Error", "Failed to fetch latest bill");
  }
};

    const handleSave = async () => {
        if (!selectedPaymentMethod) {
            Alert.alert('Validation Error', 'Please select a payment method.');
            return;
        }

        // Check if UTR number is required for UPI and Debit Card
        const requiresUTR = selectedPaymentMethod.PaymentGatewayId === 'HDFCB' ||
                           selectedPaymentMethod.PaymentGatewayId === 'VISAD';

        if (requiresUTR) {
            if (!utrNo.trim()) {
                Alert.alert('Validation Error', 'UTR number is required for UPI and Debit Card payments.');
                return;
            }

            // Validate UTR number: exactly 12 digits, only numbers
            const utrRegex = /^\d{12}$/;
            if (!utrRegex.test(utrNo.trim())) {
                Alert.alert('Validation Error', 'UTR number must be exactly 12 digits and contain only numbers.');
                return;
            }
        }
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const loginBranchName = parsedBranch.BranchName;
        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
       const loginUserID = user.userId;

        // Map itemTableDetails to API expected structure
        const mappedItems = (itemTableDetails || []).map(item => ({
            ItemID: item.itemId,
            ItemName: item.itemName,
            ItemCategoryID: item.itemCategoryId,
            BatchNo: item.batchNumber,
            ItemPrice: item.price,
            ItemDayRate: item.itemDayRate,
            ItemWeight: item.weight,
            ItemQuantity: item.quantity,
            StockQty: item.stockQty,
            ItemTotalPrice: item.totalPrice,
            TaxPercent: item.taxPercent,
            TaxAmount: item.taxAmount,
            Discount: item.discount,
            ItemStockGroupID: item.stockGroupId,
            ItemTaxCategoryID: item.taxCategoryId,
            ItemSellByWeight: item.sellByWeight,
            ItemAltQtyEnabled: item.altQtyEnabled,
        }));

        const payload = {
            token: bearerToken,
            loginBranchID,
            loginUserID,
            CustomerID: customerId,
            CustomerName: customerName,
            CustomerMobile: customerMobile,
            saleType: selectedSaleType,
            businessChannel: selectedBusinessChannel,
            deliveryMode: selectedDeliveryMode,
            tripID: selectedTripId,
            items: mappedItems, // <-- use mapped items
            billSummary: summary,
            totalAmount: total,
            branchId: loginBranchID,
            binId: 'OKBIN',
            branchName: loginBranchName,
            utrNo: utrNo || '',
            amountReceived: parseFloat(amountReceived) || 0,
            paymentMethodId: selectedPaymentMethod.PaymentMethodId,
            paymentGatewayId: selectedPaymentMethod.PaymentGatewayId,
            paymentGatewayName: selectedPaymentMethod.PaymentGatewayName,

        };

        const result = await saveBillingData(payload);

        if (result?.result === 1) {
            // Show success dialog with print option
            Alert.alert(
                "Success",
                "Bill saved successfully. Do you want to print the bill?",
                [
                    {
                        text: "No",
                        onPress: () => {
                            // Reset and reload the bill page
                            navigation.navigate('Billing');
                        },
                        style: "cancel"
                    },
                    {
                        text: "Yes",
                        onPress: () => {
                            // Fetch latest bill and navigate to view screen
                            fetchLatestBillAndNavigate();
                        }
                    }
                ]
            );
        } else {
            Alert.alert("Error", result?.description || 'Failed to save billing');
        }
    };

    return (
        <View style={styles.container}>
            {/* App Bar */}
            <View style={styles.appBar}>
                <Text style={styles.appBarTitle}>Payment Screen</Text>
            </View>

            <ScrollView contentContainerStyle={styles.bodyContent} keyboardShouldPersistTaps="handled">
                {/* Row 1: UTR No. & Amount */}
                <View style={styles.row}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>UTR No.</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="UTR No."
                            keyboardType="numeric"
                            value={utrNo}
                            onChangeText={(text) => {
                                // Only allow numbers and limit to 12 characters
                                const numericText = text.replace(/[^0-9]/g, '');
                                if (numericText.length <= 12) {
                                    setUtrNo(numericText);
                                }
                            }}
                            maxLength={12}
                        />
                    </View>
                    <View style={{ width: 16 }} />
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Amount</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Amount"
                                keyboardType="numeric"
                                value={total.toString()}
                                editable={false}
                            />
                        </View>
                    </View>
                </View>

                {/* Row 2: Bill Amount, Total Amount Received, Change */}
                <View style={styles.row}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Bill Amount</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Bill Amount"
                                keyboardType="numeric"
                                value={total.toString()}
                                editable={false}
                            />
                        </View>
                    </View>
                    <View style={{ width: 16 }} />
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Total Amount Received</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Total Amount Received"
                                keyboardType="numeric"
                                value={amountReceived}
                                onChangeText={setAmountReceived}
                            />
                        </View>
                    </View>
                </View>
                <View style={styles.row}>
                    <View style={[styles.inputContainer, { flex: 1 }]}>
                        <Text style={styles.label}>Change:</Text>
                        <Text style={styles.changeText}>
                            ₹{Math.max((parseFloat(amountReceived) || 0) - total, 0).toFixed(2)}
                        </Text>
                    </View>
                </View>

                {/* Payment Methods Buttons */}
                <View style={styles.paymentMethodsRow}>
                    {paymentMethods.map((method) => (
                        <TouchableOpacity
                            key={method.PaymentMethodId}
                            style={[
                                styles.paymentButton,
                                selectedPaymentMethod?.PaymentMethodId === method.PaymentMethodId
                                    ? styles.paymentButtonSelected
                                    : null,
                            ]}
                            onPress={() => setSelectedPaymentMethod(method)}
                        >
                            <Text
                                style={[
                                    styles.paymentButtonText,
                                    selectedPaymentMethod?.PaymentMethodId === method.PaymentMethodId
                                        ? styles.paymentButtonTextSelected
                                        : null,
                                ]}
                            >
                                {method.PaymentGatewayName}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>

                {/* Save Button */}
                <View style={styles.saveRow}>
                    <TouchableOpacity
                        style={styles.saveButton}
                        onPress={handleSave}
                    >
                        <Text style={styles.saveButtonText}>Save</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>

            {/* Bottom Back Button */}
            <View style={styles.bottomButtonContainer}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.goBack()}
                >
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    appBar: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'rgb(2,9,106)',
        paddingHorizontal: 20,
        paddingVertical: 18,
        borderBottomLeftRadius: 16,
        borderBottomRightRadius: 16,
        elevation: 4,
    },
    appBarTitle: {
        color: '#fff',
        fontSize: 22,
        fontWeight: 'bold',
        letterSpacing: 1,
    },

    bodyContent: {
        padding: 24,
        paddingBottom: 100, // Add space for bottom button
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 18,
    },
    inputContainer: {
        flex: 1,
    },
    label: {
        fontSize: 15,
        fontWeight: '600',
        marginBottom: 6,
        color: '#222',
    },
    input: {
        borderWidth: 1,
        borderColor: '#bfc4d1',
        borderRadius: 10,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#f8f9fa',
        color: '#222',
    },
    inputWithIcon: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rupeeIcon: {
        fontSize: 18,
        color: '#888',
        marginRight: 6,
    },
    changeText: {
        fontSize: 20,
        fontWeight: 'bold',
        color: 'green',
        marginTop: 8,
        marginLeft: 8,
    },
    paymentMethodsRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginVertical: 24,
        gap: 16,
        flexWrap: 'wrap',
    },
    paymentButton: {
        backgroundColor: '#e0e3ee',
        paddingVertical: 14,
        paddingHorizontal: 32,
        borderRadius: 10,
        marginHorizontal: 6,
        marginBottom: 8,
        minWidth: 110,
        alignItems: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    paymentButtonSelected: {
        backgroundColor: '#02096A',
        borderColor: '#02096A',
    },
    paymentButtonText: {
        color: '#02096A',
        fontWeight: 'bold',
        fontSize: 16,
    },
    paymentButtonTextSelected: {
        color: '#fff',
    },
    saveRow: {
        flexDirection: 'row',
        marginTop: 24,
    },
    saveButton: {
        flex: 1,
        backgroundColor: 'rgb(2,9,106)',
        paddingVertical: 16,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 2,
    },
    saveButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 19,
        letterSpacing: 1,
    },
    bottomButtonContainer: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        alignItems: 'center',
    },
    backButton: {
        backgroundColor: '#dc3545',
        paddingVertical: 15,
        paddingHorizontal: 40,
        borderRadius: 10,
        minWidth: 120,
        minHeight: 60,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    backButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default BillingPaymentScreen;
