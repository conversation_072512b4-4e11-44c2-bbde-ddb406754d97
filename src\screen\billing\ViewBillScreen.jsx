import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';


const ViewBillScreen = ({ route }) => {

    const navigation = useNavigation();

    const {
        saleID,
        printDateTime,
        currentTimeStamp,
        itemTableDetails,
        deliveryCharges,
        roundOffAmount,
        totalAmount,
        discountAmount,
        selectedCustomerType,
        selectedGatewayName,
        customerID,
        customerName,
        customerPhoneNumber,
        customerAddress,
        customerPlace,
        customerCity,
        customerState,
        customerPincode,
        branchId,
        branchName,
        areaName,
        gstNumber,
        foodLicenseNumber,
        branchPincode,
        isFromDirectPrint = false, // New parameter to check if coming from direct print
    } = route.params;

    // Format dates
    const formatBusinessDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const formatPrintDateTime = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    const hideNosColumn = itemTableDetails.every(item => (item.AltQty ?? 0) === 0);
    const hideWeightColumn = itemTableDetails.every(item => (item.Qty ?? 0) === 0);
    const hideGSTColumn = itemTableDetails.every(item => (item.TaxAmount ?? 0) === 0);

    const calculateTaxDetails = (items) => {
        const taxBreakups = [];

        items.forEach(item => {
            const cgstPercent = item.CGSTPercent || 0;
            const sgstPercent = item.SGSTPercent || 0;
            const cgstAmount = item.CGSTAmount || 0;
            const sgstAmount = item.SGSTAmount || 0;

            if (cgstAmount !== 0 || sgstAmount !== 0) {
                taxBreakups.push({
                    cgstPercent,
                    sgstPercent,
                    cgstAmount,
                    sgstAmount,
                });
            }
        });

        return { taxBreakups };
    };

    const taxDetails = calculateTaxDetails(itemTableDetails);

    // Bluetooth printer functions
    const scanForPrinters = async (showErrorAlert = false) => {
        setIsScanning(true);
        try {
            await BLEPrinter.init();
            const devices = await BLEPrinter.getDeviceList();

            const printerDevices = devices.map(device => ({
                label: device.device_name || device.inner_mac_address,
                value: device.inner_mac_address,
                device: device
            }));

            setAvailablePrinters(printerDevices);

            if (printerDevices.length === 0 && showErrorAlert) {
                Alert.alert('No Printers Found', 'No Bluetooth printers found. Please make sure your printer is discoverable and try again.');
            }
        } catch (error) {
            console.error('Error scanning for printers:', error);
            // Only show alert if explicitly requested (e.g., when user clicks refresh)
            if (showErrorAlert) {
                Alert.alert('Error', 'Failed to scan for printers. Please check Bluetooth permissions and try again.');
            }
        }
        setIsScanning(false);
    };

    const printReceipt = async () => {
        if (!selectedPrinter) {
            Alert.alert('No Printer Selected', 'Please select a printer first.');
            return;
        }

        setIsPrinting(true);

        try {
            // Connect to printer
            await BLEPrinter.connectPrinter(selectedPrinter);

            // Build receipt content using the tag format
            let receiptContent = '';

            // Header
            receiptContent += '<C>ABIS Exports\n';
            receiptContent += 'India Pvt Ltd\n';
            receiptContent += `${branchName}\n`;
            receiptContent += `${areaName}\n`;
            receiptContent += `${branchPincode}\n`;
            receiptContent += `FSSAI: ${foodLicenseNumber}\n`;
            receiptContent += `GSTIN: ${gstNumber}\n`;
            // Only show "Duplicate Bill" if not from direct print
            if (!isFromDirectPrint) {
                receiptContent += 'Duplicate Bill\n';
            }
            receiptContent += '</C>\n';
            receiptContent += '--------------------------------\n';

            // Bill details
            receiptContent += `POS/Bill No: ${saleID}\n`;
            receiptContent += `Print Date: ${formatPrintDateTime(printDateTime)}\n`;
            receiptContent += `Bus. Date: ${formatBusinessDate(currentTimeStamp)}\n`;
            receiptContent += '--------------------------------\n';

            // Item details
            receiptContent += 'Item Details:\n';
            receiptContent += hideGSTColumn
                ? 'Qty  Rate  Total\n'
                : 'Qty  Rate  GST  Total\n';

            itemTableDetails.forEach(item => {
                receiptContent += `${item.ItemName}\n`;
                const qty = item.Qty !== 0 ? item.Qty : item.AltQty;
                const rate = item.Rate;
                const tax = item.TaxAmount;
                const total = item.TotalAmount;

                if (hideGSTColumn) {
                    receiptContent += `${qty.toString().padStart(3)} ${rate.toString().padStart(5)} ${total.toString().padStart(6)}\n`;
                } else {
                    receiptContent += `${qty.toString().padStart(3)} ${rate.toString().padStart(5)} ${tax.toString().padStart(4)} ${total.toString().padStart(6)}\n`;
                }
            });

            receiptContent += '--------------------------------\n';

            // Totals
            if (deliveryCharges > 0) {
                receiptContent += `Freight: ${deliveryCharges}\n`;
            }
            receiptContent += `Round-off: ${roundOffAmount}\n`;
            receiptContent += `Total (incl. GST): ${totalAmount}\n`;

            // Payment details
            if (selectedCustomerType !== 'CS') {
                receiptContent += '--------------------------------\n';
                receiptContent += `Payment mode: ${selectedGatewayName}\n`;
            }

            // Discount and tax details
            if (discountAmount !== '0' || taxDetails.taxBreakups.length > 0) {
                receiptContent += '--------------------------------\n';
            }

            if (discountAmount !== '0') {
                receiptContent += `<C>You saved Rs${discountAmount}</C>\n`;
            }

            if (taxDetails.taxBreakups.length > 0) {
                receiptContent += 'CGST  SGST  CGST Amt  SGST Amt\n';
                taxDetails.taxBreakups.forEach(tax => {
                    receiptContent += `${tax.cgstPercent.toFixed(1)}% ${tax.sgstPercent.toFixed(1)}% ${tax.cgstAmount.toFixed(2)} ${tax.sgstAmount.toFixed(2)}\n`;
                });
            }

            receiptContent += '--------------------------------\n';

            // Customer details
            receiptContent += 'Customer Details:\n';
            receiptContent += `Customer ID: ${customerID}\n`;
            receiptContent += `Name: ${customerName}\n`;
            receiptContent += `Mobile: ${customerPhoneNumber}\n`;
            if (customerAddress && customerAddress.length > 0) {
                receiptContent += `Address: ${customerAddress}\n`;
            }
            if (customerPlace || customerCity) {
                receiptContent += `${customerPlace} ${customerCity}\n`;
            }
            if (customerState || customerPincode) {
                receiptContent += `${customerState} ${customerPincode}\n`;
            }

            receiptContent += '--------------------------------\n';
            receiptContent += '<C>Thanks & Visit Again!</C>\n';
            receiptContent += '\n\n\n';

            // Print the complete receipt
            await BLEPrinter.printBill(receiptContent);

            Alert.alert('Print Successful', 'The receipt has been printed successfully.');

        } catch (error) {
            console.error('Print error:', error);
            Alert.alert('Print Failed', 'Failed to print receipt: ' + error.message);
        }

        setIsPrinting(false);
    };

    // Load printers on component mount (silently, without showing errors)
    useEffect(() => {
        scanForPrinters(false);
    }, []);

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollContainer}>
                <Text style={styles.title}>ABIS Exports India Pvt Ltd</Text>
                <Text style={styles.center}>{branchId}, {branchName}</Text>
                <Text style={styles.center}>{areaName}</Text>
                <Text style={styles.center}>{branchPincode}</Text>
                <Text style={styles.center}>FSSAI License Number: {foodLicenseNumber}, GSTIN : {gstNumber}</Text>
                {!isFromDirectPrint && (
                    <Text style={styles.center}>Duplicate Bill</Text>
                )}

                <View style={styles.rowSpaceBetween}>
                    <Text>POS/Bill No: {saleID}</Text>
                    <Text>Print Date: {formatPrintDateTime(printDateTime)}</Text>
                </View>
                <View style={styles.rowRight}>
                    <Text>Business Date: {formatBusinessDate(currentTimeStamp)}</Text>
                </View>

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={styles.cell}>Item name</Text>
                    <Text style={styles.cell}>Qty</Text>
                    <Text style={styles.cell}>Rate</Text>
                    {!hideGSTColumn && <Text style={styles.cell}>GST</Text>}
                    <Text style={styles.cell}>Total</Text>
                </View>
                {itemTableDetails.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={styles.cell}>{item.ItemName}</Text>
                        <Text style={styles.cell}>{item.Qty !== 0 ? item.Qty : item.AltQty}</Text>
                        <Text style={styles.cell}>{item.Rate}</Text>
                        {!hideGSTColumn && <Text style={styles.cell}>{item.TaxAmount}</Text>}
                        <Text style={styles.cell}>{item.TotalAmount}</Text>
                    </View>
                ))}
            </View>

            {deliveryCharges !== 0 && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Freight:</Text>
                    <Text>₹{deliveryCharges}</Text>
                </View>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>Roundoff:</Text>
                <Text>₹{roundOffAmount}</Text>
            </View>

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>Total (incl. GST):</Text>
                <Text style={styles.bold}>₹{totalAmount}</Text>
            </View>

            {selectedCustomerType !== 'CS' && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Payment mode:</Text>
                    <Text>{selectedGatewayName}</Text>
                </View>
            )}

            {(discountAmount !== '0' || taxDetails.taxBreakups.length > 0) && <View style={styles.divider} />}

            {discountAmount !== '0' && (
                <View style={styles.rowCenter}>
                    <Text>You saved ₹{discountAmount}</Text>
                </View>
            )}

            {taxDetails.taxBreakups.length > 0 && (
                <>
                    <View style={styles.rowCenter}>
                        <Text style={styles.cell}>CGST</Text>
                        <Text style={styles.cell}>SGST</Text>
                        <Text style={styles.cell}>CGST Amt</Text>
                        <Text style={styles.cell}>SGST Amt</Text>
                    </View>
                    {taxDetails.taxBreakups.map((tax, i) => (
                        <View key={i} style={styles.rowCenter}>
                            <Text style={styles.cell}>{tax.cgstPercent}%</Text>
                            <Text style={styles.cell}>{tax.sgstPercent}%</Text>
                            <Text style={styles.cell}>₹{tax.cgstAmount.toFixed(2)}</Text>
                            <Text style={styles.cell}>₹{tax.sgstAmount.toFixed(2)}</Text>
                        </View>
                    ))}
                </>
            )}

            <Text style={styles.sectionHeader}>Customer Details:</Text>
            <Text>Customer ID: {customerID}</Text>
            <Text>Customer Name: {customerName}</Text>
            <Text>Mobile: {customerPhoneNumber}</Text>
            {customerAddress?.length > 0 && <Text>Address: {customerAddress}</Text>}
            {(customerPlace || customerCity) && <Text>{customerPlace} {customerCity}</Text>}
            {(customerState || customerPincode) && <Text>{customerState} {customerPincode}</Text>}

            <View style={styles.divider} />
            <Text style={styles.center}>Thanks & Visit Again!</Text>

            {/* Printer Controls */}
            <View style={styles.printerControlsContainer}>
                <View style={styles.printerRow}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                            data={availablePrinters}
                            maxHeight={300}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={item => {
                                setSelectedPrinter(item.value);
                            }}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={() => scanForPrinters(true)}
                        disabled={isScanning}
                    >
                        {isScanning ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <Icon name="refresh" size={20} color="#fff" />
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printReceipt}
                        disabled={isPrinting || !selectedPrinter}
                    >
                        {isPrinting ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Icon name="print" size={20} color="#fff" />
                                <Text style={styles.buttonText}>Print</Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
            </ScrollView>

            {/* Bottom Back Button */}
            <View style={styles.bottomButtonContainer}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => navigation.navigate('Billing')}
                >
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default ViewBillScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        padding: 16,
        paddingBottom: 100, // Add space for bottom button
    },
    bottomButtonContainer: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        alignItems: 'center',
    },
    backButton: {
        backgroundColor: '#dc3545',
        paddingVertical: 15,
        paddingHorizontal: 40,
        borderRadius: 10,
        minWidth: 120,
        minHeight: 60,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    backButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
    title: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' },
    center: { textAlign: 'center' },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: { flexDirection: 'row', justifyContent: 'flex-end' },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
    },
    table: {
        borderWidth: 1,
        marginTop: 8,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
    },
    cell: {
        flex: 1,
        padding: 6,
        textAlign: 'center',
        borderRightWidth: 1,
    },
    bold: { fontWeight: 'bold' },
    rowCenter: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
    },
    printerControlsContainer: {
        marginTop: 20,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});
