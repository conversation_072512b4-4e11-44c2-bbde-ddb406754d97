import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import { fetchBankAccounts } from '../../apiHandling/FinanceAPI/bankTransferAPI/bankNamesAPI.js';
import { fetchCurrentCashBalance } from '../../apiHandling/FinanceAPI/bankTransferAPI/currentCashBalanceAPI.js';
import { saveBranchTransfer } from '../../apiHandling/FinanceAPI/bankTransferAPI/saveBankTransferAPI.js';


import AsyncStorage from '@react-native-async-storage/async-storage';

const BankTransferScreen = () => {
    const navigation = useNavigation();


    const [orderNumber, setOrderNumber] = useState('');
    const [selectedScrollOption, setSelectedScrollOption] = useState('');


    const [bankOptions, setBankOptions] = useState([]);
    const [selectedBankName, setSelectedBankName] = useState('');

    const [dropdownValue, setDropdownValue] = useState('');
    const [businessDate, setBusinessDate] = useState('');
    const [paymentMode, setPaymentMode] = useState('Cash');
    const [currentBalance, setCurrentBalance] = useState('');
    const [remarks, setRemarks] = useState('');
    const [amount, setAmount] = useState('');
    const [branchTransferData, setBranchTransferData] = useState([]);




    useEffect(() => {

        const getBusinessDate = async () => {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;
            const date = await fetchBusinessDay(bearerToken, loginBranchID);
            setBusinessDate(date);
        };

        getBusinessDate();
    }, []);

    useEffect(() => {
        const loadBanks = async () => {
            const bearerToken = await AsyncStorage.getItem('authToken');

            const data = await fetchBankAccounts(bearerToken);
            setBankOptions(data);
        };

        loadBanks();
    }, []);


    useEffect(() => {
        const getBusinessDateAndBalance = async () => {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;
            const businessDateFormatted = await fetchBusinessDay(bearerToken, loginBranchID);
            setBusinessDate(businessDateFormatted);
            const [day, month, year] = businessDateFormatted.split('-');
            const businessDateCode = `${year}${month}${day}`;
            const balance = await fetchCurrentCashBalance(bearerToken, loginBranchID, businessDateCode);
            setCurrentBalance(balance);
        };

        getBusinessDateAndBalance();
    }, []);

    const addDetailsToTable = () => {
        if (remarks.trim() && amount.trim() && selectedBankName) {
            const newItem = {
                id: Date.now(),
                lineNumber: (branchTransferData.length + 1).toString().padStart(3, '0'),

                bankName: `${selectedBankName}`,
                remarks: remarks.trim(),
                amount: amount.trim(),
                vehicleNumber: '', // Add this if using in table later
                driverName: '',    // Add this if using in table later
            };

            setBranchTransferData([...branchTransferData, newItem]);

            // Clear only user-input fields
            setRemarks('');
            setAmount('');

            setSelectedBankName('');
            setDropdownValue('');
        } else {
            alert('Please fill all required fields (Branch, Remarks, Amount)');
        }
    };

    const handleResetAndReload = async () => {
        // Reset all form states
        setOrderNumber('');
        setSelectedScrollOption('');
        setBankOptions([]);
        setSelectedBankName('');
        setDropdownValue('');
        setPaymentMode('Cash');
        setRemarks('');
        setAmount('');
        setBranchTransferData([]);

        // Reload business date and current balance
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        // Reload business date
        const date = await fetchBusinessDay(bearerToken, loginBranchID);
        setBusinessDate(date);

        // Reload current balance
        const [day, month, year] = date.split('-');
        const businessDateCode = `${year}${month}${day}`;
        const balance = await fetchCurrentCashBalance(bearerToken, loginBranchID, businessDateCode);
        setCurrentBalance(balance);

        // Reload bank accounts
        const bankData = await fetchBankAccounts(bearerToken);
        setBankOptions(bankData);
    };


    return (
        <View style={styles.container}>
            <Navbar />
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>
                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Bank Transfer Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>Bank Transfer</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={buttonStyle}
                                        onPress={async () => {
                                            if (option === 'New' || option === 'Cancel') {
                                                await handleResetAndReload();
                                            } else if (option === 'Save') {
                                                const bearerToken = await AsyncStorage.getItem('authToken');
                                                const selectedBranch = await AsyncStorage.getItem('selectedBranch');
                                                const parsedBranch = JSON.parse(selectedBranch);
                                                const loginBranchID = parsedBranch.BranchId;
                                                const userDetails = await AsyncStorage.getItem('userData');
                                                const user = JSON.parse(userDetails);
                                                const loginUserID = user.userId;

                                                try {
                                                    const result = await saveBranchTransfer(
                                                        bearerToken,
                                                        loginBranchID,
                                                        loginUserID,
                                                        businessDate,
                                                        branchTransferData
                                                    );

                                                    Alert.alert(
                                                        'Success',
                                                        'Transfer saved successfully',
                                                        [
                                                            {
                                                                text: 'OK',
                                                                onPress: () => navigation.replace('Bank Transfer')
                                                            }
                                                        ],
                                                        { cancelable: false }
                                                    );
                                                } catch (error) {
                                                    console.error('Error saving transfer:', error);
                                                    Alert.alert('Error', 'Failed to save transfer');
                                                }
                                            } else {
                                                setSelectedScrollOption(option);
                                            }
                                        }}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>

            {/* Main Content ScrollView */}
            <ScrollView style={{ flex: 1 }}>
                {/* Bank Transfer Container */}
                <View style={styles.bankTransfer_container}>
                    {/* Row 1: Document No., Business Date */}
                    <View style={styles.bankTransfer_inputRow}>
                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Document No."
                            value={orderNumber}
                            onChangeText={setOrderNumber}
                        />

                        <View style={styles.bankTransfer_dateContainer}>
                            <TextInput
                                style={styles.bankTransfer_dateField}
                                placeholder="Business Date"
                                value={businessDate}
                                onChangeText={setBusinessDate}
                            />
                            <TouchableOpacity
                                style={styles.bankTransfer_calendarButton}
                                onPress={() => {
                                    // Calendar functionality can be added here
                                    console.log('Calendar pressed');
                                }}
                            >
                                <Text style={styles.bankTransfer_calendarIcon}>📅</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Row 2: Branch Name, Payment Mode, Current Balance */}
                    <View style={styles.bankTransfer_inputRow}>
                        <View style={styles.bankTransfer_dropdownContainer}>
                            <Dropdown
                                data={bankOptions}
                                labelField="label"
                                valueField="value"
                                placeholder="Bank Name"
                                value={dropdownValue}
                                onChange={(item) => {
                                    setDropdownValue(item.value);
                                    setSelectedBankName(item.value);

                                }}
                                style={styles.bankTransfer_dropdown}
                            />
                        </View>

                        <TextInput
                            style={[styles.bankTransfer_textField, { backgroundColor: '#e0e0e0', color: '#555' }]}
                            value={paymentMode}
                            editable={false}
                            selectTextOnFocus={false}
                        />


                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Current Balance"
                            value={currentBalance}
                            onChangeText={setCurrentBalance}
                            keyboardType="numeric"
                            editable={false}
                        />
                    </View>

                    {/* Row 3: Remarks, Amount */}
                    <View style={styles.bankTransfer_inputRow}>
                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Remarks"
                            value={remarks}
                            onChangeText={setRemarks}
                        />

                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Amount"
                            value={amount}
                            onChangeText={setAmount}
                            keyboardType="numeric"
                        />
                    </View>

                    {/* Row 4: Add Button, Clear Button */}
                    <View style={styles.bankTransfer_buttonRow}>
                        <TouchableOpacity
                            style={styles.bankTransfer_addButton}
                            onPress={addDetailsToTable}
                        >
                            <Text style={styles.bankTransfer_buttonText}>Add</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.bankTransfer_clearButton}
                            onPress={() => {
                                setRemarks('');
                                setAmount('');

                                setSelectedBankName('');
                                setDropdownValue('');
                            }}
                        >
                            <Text style={styles.bankTransfer_buttonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Row 5: Table */}
                    <View style={styles.bankTransfer_tableContainer}>
                        <ScrollView horizontal showsHorizontalScrollIndicator={true}>
                            <View style={styles.bankTransfer_tableWrapper}>
                                <DataTable>
                                    <DataTable.Header style={styles.bankTransfer_tableHeader}>
                                        <DataTable.Title style={styles.bankTransfer_columnLineNo}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Line No</Text>
                                        </DataTable.Title>
                                        <DataTable.Title style={styles.bankTransfer_columnBankName}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Bank Name</Text>
                                        </DataTable.Title>
                                        <DataTable.Title style={styles.bankTransfer_columnRemarks}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Remarks</Text>
                                        </DataTable.Title>
                                        <DataTable.Title style={styles.bankTransfer_columnAmount}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Amount</Text>
                                        </DataTable.Title>
                                        <DataTable.Title style={styles.bankTransfer_columnVehicleNo}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Vehicle No.</Text>
                                        </DataTable.Title>
                                        <DataTable.Title style={styles.bankTransfer_columnDriverName}>
                                            <Text style={styles.bankTransfer_tableHeaderText}>Driver Name</Text>
                                        </DataTable.Title>
                                    </DataTable.Header>

                                    {branchTransferData.map((item) => (
                                        <DataTable.Row key={item.id}>
                                            <DataTable.Cell style={styles.bankTransfer_columnLineNo}>
                                                <Text style={styles.bankTransfer_cellText}>{item.lineNumber}</Text>
                                            </DataTable.Cell>
                                            <DataTable.Cell style={styles.bankTransfer_columnBankName}>
                                                <Text style={styles.bankTransfer_cellText}>{item.bankName}</Text>
                                            </DataTable.Cell>
                                            <DataTable.Cell style={styles.bankTransfer_columnRemarks}>
                                                <Text style={styles.bankTransfer_cellText}>{item.remarks}</Text>
                                            </DataTable.Cell>
                                            <DataTable.Cell style={styles.bankTransfer_columnAmount}>
                                                <Text style={styles.bankTransfer_cellText}>{item.amount}</Text>
                                            </DataTable.Cell>
                                            <DataTable.Cell style={styles.bankTransfer_columnVehicleNo}>
                                                <Text style={styles.bankTransfer_cellText}>{item.vehicleNumber || '-'}</Text>
                                            </DataTable.Cell>
                                            <DataTable.Cell style={styles.bankTransfer_columnDriverName}>
                                                <Text style={styles.bankTransfer_cellText}>{item.driverName || '-'}</Text>
                                            </DataTable.Cell>
                                        </DataTable.Row>
                                    ))}
                                </DataTable>
                            </View>
                        </ScrollView>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: 'black',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,

    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== BANK TRANSFER STYLES ====================
    bankTransfer_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    bankTransfer_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    bankTransfer_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    bankTransfer_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    bankTransfer_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
        gap: 10,
    },
    bankTransfer_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    bankTransfer_dateContainer: {
        flex: 1,
        position: 'relative',
        minWidth: 120,
    },
    bankTransfer_dateField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        paddingRight: 45, // Make space for the calendar icon
        borderWidth: 1,
        borderColor: '#ccc',
    },
    bankTransfer_calendarButton: {
        position: 'absolute',
        right: 10,
        top: 0,
        bottom: 0,
        width: 30,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent',
    },
    bankTransfer_calendarIcon: {
        fontSize: 18,
        color: '#666',
    },
    bankTransfer_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    bankTransfer_addButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
        flex: 1,
        marginRight: 5,
    },
    bankTransfer_clearButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
        flex: 1,
        marginLeft: 5,
    },
    bankTransfer_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    bankTransfer_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    bankTransfer_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    bankTransfer_tableWrapper: {
        minWidth: '100%',
    },
    bankTransfer_tableHeader: {
        backgroundColor: '#041C44',
    },
    bankTransfer_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    bankTransfer_cellText: {
        fontSize: 14,
        color: '#333',
    },
    // Dynamic column widths based on content
    bankTransfer_columnLineNo: {
        minWidth: 80,
        maxWidth: 100,
        flex: 0,
    },
    bankTransfer_columnBankName: {
        minWidth: 150,
        maxWidth: 250,
        flex: 1,
    },
    bankTransfer_columnRemarks: {
        minWidth: 200,
        maxWidth: 300,
        flex: 1,
    },
    bankTransfer_columnAmount: {
        minWidth: 120,
        maxWidth: 150,
        flex: 0,
    },
    bankTransfer_columnVehicleNo: {
        minWidth: 120,
        maxWidth: 180,
        flex: 0,
    },
    bankTransfer_columnDriverName: {
        minWidth: 150,
        maxWidth: 200,
        flex: 1,
    },


});

export default BankTransferScreen;
