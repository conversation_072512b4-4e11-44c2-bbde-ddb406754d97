import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, branchId, branchName } from '../../globals';

const HOTransferScreen = () => {
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [selectedTransferType, setSelectedTransferType] = useState('');
    const [orderNumber, setOrderNumber] = useState('');
    const [invoiceNumber, setInvoiceNumber] = useState('');
    const [lotNumber, setLotNumber] = useState('');
    const [remarks, setRemarks] = useState('');
    const [amount, setAmount] = useState('');
    const [bankTransferData, setBankTransferData] = useState([
        { id: 1, lineNumber: '001', bankAccountName: 'HDFC Bank', remarks: 'Salary payment', amount: '25000' },
        { id: 2, lineNumber: '002', bankAccountName: 'SBI Bank', remarks: 'Vendor payment', amount: '15000' }
    ]);
    return (
        <View style={styles.container}>
            <Navbar/>
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>
                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Bank Transfer Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>HO Transfer</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={buttonStyle}
                                        onPress={() => setSelectedScrollOption(option)}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>

            {/* Main Content ScrollView */}
            <ScrollView style={{ flex: 1 }}>
                {/* Bank Transfer Container */}
                <View style={styles.bankTransfer_container}>
                    {/* Row 1: Document No., Branch Key */}
                    <View style={styles.bankTransfer_inputRow}>
                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Document No."
                            value={orderNumber}
                            onChangeText={setOrderNumber}
                        />

                        <View style={styles.bankTransfer_dropdownContainer}>
                            <Dropdown
                                data={[
                                    { label: 'Branch 1', value: 'Branch 1' },
                                    { label: 'Branch 2', value: 'Branch 2' },
                                    { label: 'Branch 3', value: 'Branch 3' },
                                ]}
                                labelField="label"
                                valueField="value"
                                placeholder="Branch Key"
                                value={selectedTransferType}
                                onChange={(item) => setSelectedTransferType(item.value)}
                                style={styles.bankTransfer_dropdown}
                            />
                        </View>
                    </View>

                    {/* Row 2: Expenses Head, Trip ID, Select Button */}
                    <View style={styles.bankTransfer_inputRow}>
                        <View style={styles.bankTransfer_dropdownContainer}>
                            <Dropdown
                                data={[
                                    { label: 'Expense 1', value: 'Expense 1' },
                                    { label: 'Expense 2', value: 'Expense 2' },
                                    { label: 'Expense 3', value: 'Expense 3' },
                                ]}
                                labelField="label"
                                valueField="value"
                                placeholder="Expenses Head"
                                value={invoiceNumber}
                                onChange={(item) => setInvoiceNumber(item.value)}
                                style={styles.bankTransfer_dropdown}
                            />
                        </View>

                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Trip ID"
                            value={lotNumber}
                            onChangeText={setLotNumber}
                        />

                        <TouchableOpacity style={styles.bankTransfer_selectButton}>
                            <Text style={styles.bankTransfer_selectButtonText}>Select</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Row 3: Remarks, Amount */}
                    <View style={styles.bankTransfer_inputRow}>
                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Remarks"
                            value={remarks}
                            onChangeText={setRemarks}
                        />

                        <TextInput
                            style={styles.bankTransfer_textField}
                            placeholder="Amount"
                            value={amount}
                            onChangeText={setAmount}
                            keyboardType="numeric"
                        />
                    </View>

                    {/* Row 4: Add Button, Clear Button */}
                    <View style={styles.bankTransfer_buttonRow}>
                        <TouchableOpacity
                            style={styles.bankTransfer_addButton}
                            onPress={() => {
                                if (remarks && amount) {
                                    const newItem = {
                                        id: Date.now(),
                                        lineNumber: (bankTransferData.length + 1).toString().padStart(3, '0'),
                                        bankAccountName: selectedTransferType || 'Not specified',
                                        remarks: remarks,
                                        amount: amount
                                    };
                                    setBankTransferData([...bankTransferData, newItem]);
                                    setRemarks('');
                                    setAmount('');
                                }
                            }}
                        >
                            <Text style={styles.bankTransfer_buttonText}>Add</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.bankTransfer_clearButton}
                            onPress={() => {
                                setRemarks('');
                                setAmount('');
                            }}
                        >
                            <Text style={styles.bankTransfer_buttonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Row 5: Table */}
                    <View style={styles.bankTransfer_tableContainer}>
                        <DataTable>
                            <DataTable.Header style={styles.bankTransfer_tableHeader}>
                                <DataTable.Title><Text style={styles.bankTransfer_tableHeaderText}>Line No</Text></DataTable.Title>
                                <DataTable.Title><Text style={styles.bankTransfer_tableHeaderText}>Bank Account Name</Text></DataTable.Title>
                                <DataTable.Title><Text style={styles.bankTransfer_tableHeaderText}>Remarks</Text></DataTable.Title>
                                <DataTable.Title><Text style={styles.bankTransfer_tableHeaderText}>Amount</Text></DataTable.Title>
                            </DataTable.Header>

                            {bankTransferData.map((item) => (
                                <DataTable.Row key={item.id}>
                                    <DataTable.Cell>{item.lineNumber}</DataTable.Cell>
                                    <DataTable.Cell>{item.bankAccountName}</DataTable.Cell>
                                    <DataTable.Cell>{item.remarks}</DataTable.Cell>
                                    <DataTable.Cell>{item.amount}</DataTable.Cell>
                                </DataTable.Row>
                            ))}
                        </DataTable>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: 'black',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,

    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== BANK TRANSFER STYLES ====================
    bankTransfer_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    bankTransfer_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    bankTransfer_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    bankTransfer_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    bankTransfer_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
        gap: 10,
    },
    bankTransfer_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    bankTransfer_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    bankTransfer_addButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
        flex: 1,
        marginRight: 5,
    },
    bankTransfer_clearButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
        flex: 1,
        marginLeft: 5,
    },
    bankTransfer_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    bankTransfer_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    bankTransfer_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    bankTransfer_tableHeader: {
        backgroundColor: '#041C44',
    },
    bankTransfer_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },


});

export default HOTransferScreen;
