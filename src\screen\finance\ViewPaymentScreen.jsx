import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchExpenseDetail } from '../../apiHandling/FinanceAPI/PaymentAPI/fetchExpenseDetailAPI';

const ViewPaymentScreen = ({ route }) => {
    const navigation = useNavigation();
    const { expenseId } = route.params;

    // State for expense data
    const [expenseData, setExpenseData] = useState(null);
    const [branchData, setBranchData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    // Fetch expense details
    const fetchExpenseDetails = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            const data = await fetchExpenseDetail(bearerToken, loginBranchID, expenseId);
            setExpenseData(data);

            // Fetch branch details
            const branchResponse = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchdetails?Branchid=${loginBranchID}`,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );
            setBranchData(branchResponse.data?.[0]);
        } catch (error) {
            console.error('Error fetching expense details:', error);
            Alert.alert('Error', 'Failed to load expense details.');
        }
        setLoading(false);
    };

    // Calculate total amount
    const getTotalAmount = () => {
        if (!expenseData?.details) return 0;
        return expenseData.details.reduce((total, item) => total + (parseFloat(item.Amount) || 0), 0);
    };

    // Get expense types for display
    const getExpenseTypes = () => {
        if (!expenseData?.details) return [];
        const types = [...new Set(expenseData.details.map(item => item.ExpenseGroupName))];
        return types.filter(type => type);
    };

    // Bluetooth printer functions
    const scanForPrinters = async (showErrorAlert = false) => {
        setIsScanning(true);
        try {
            await BLEPrinter.init();
            const devices = await BLEPrinter.getDeviceList();

            const printerDevices = devices.map(device => ({
                label: device.device_name || device.inner_mac_address,
                value: device.inner_mac_address,
                device: device
            }));

            setAvailablePrinters(printerDevices);

            if (printerDevices.length === 0 && showErrorAlert) {
                Alert.alert('No Printers Found', 'No Bluetooth printers found. Please make sure your printer is discoverable and try again.');
            }
        } catch (error) {
            console.error('Error scanning for printers:', error);
            if (showErrorAlert) {
                Alert.alert('Error', 'Failed to scan for printers. Please check Bluetooth permissions and try again.');
            }
        }
        setIsScanning(false);
    };

    const printExpense = async () => {
        if (!selectedPrinter) {
            Alert.alert('No Printer Selected', 'Please select a printer first.');
            return;
        }

        if (!expenseData || !branchData) {
            Alert.alert('Error', 'Expense data not loaded.');
            return;
        }

        setIsPrinting(true);

        try {
            // Connect to printer
            await BLEPrinter.connectPrinter(selectedPrinter);

            // Build expense content using the tag format
            let receiptContent = '';

            // Header
            receiptContent += '<C>ABIS EXPORTS INDIA PVT LTD\n';
            receiptContent += `${branchData.BranchName || ''}\n`;
            receiptContent += `${branchData.AreaName || ''}\n`;
            receiptContent += `${branchData.PINCODE || ''}\n`;
            receiptContent += 'PhoneNo :\n';
            receiptContent += '<<EXPENSES>></C>\n';

            // Get expense types for display
            const expenseTypes = getExpenseTypes();
            if (expenseTypes.length > 0) {
                receiptContent += `<C>(TYPE : ${expenseTypes.join(', ')})</C>\n`;
            }

            const businessDate = expenseData.header?.[0]?.BusinessDate
                ? new Date(expenseData.header[0].BusinessDate).toLocaleDateString('en-GB')
                : '';

            receiptContent += `TranNo: ${expenseData.header?.[0]?.DailyExpenseHdrid || ''}  Trans Date: ${businessDate}\n`;
            receiptContent += `Location  :${branchData.BranchId}/${branchData.BranchName || ''}\n`;
            receiptContent += `Printed On: ${new Date().toLocaleDateString('en-GB')} ${new Date().toLocaleTimeString('en-GB')}\n`;
            receiptContent += '----------------------------------------\n';

            const totalAmount = getTotalAmount();

            expenseData.details?.forEach(item => {
                const amount = parseFloat(item.Amount) || 0;

                receiptContent += `Amount       :              ${amount.toFixed(2)}\n`;
                receiptContent += `payment mode         :                 ${item.PaymentGatewayName || ''}\n`;
                receiptContent += `expense type : ${item.ExpenseHeadName || ''}\n`;
                if (item.Remarks) {
                    receiptContent += `Remark  : ${item.Remarks}\n`;
                }
                receiptContent += '----------------------------------------\n';
            });

            receiptContent += `TOTAL :                             ${totalAmount.toFixed(2)}\n`;
            receiptContent += '\n\n\n';

            // Print the complete receipt
            await BLEPrinter.printBill(receiptContent);

            Alert.alert('Print Successful', 'The expense has been printed successfully.');

        } catch (error) {
            console.error('Print error:', error);
            Alert.alert('Print Failed', 'Failed to print expense: ' + error.message);
        }

        setIsPrinting(false);
    };

    // Load data and printers on component mount (scan printers silently)
    useEffect(() => {
        fetchExpenseDetails();
        scanForPrinters(false);
    }, []);

    if (loading) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <ActivityIndicator size="large" color="#0000ff" />
                <Text>Loading expense details...</Text>
            </View>
        );
    }

    if (!expenseData || !expenseData.header || !expenseData.details) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>No expense data found.</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={{ color: 'blue', marginTop: 10 }}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const header = expenseData.header[0];
    const totalAmount = getTotalAmount();
    const expenseTypes = getExpenseTypes();
   

    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>

            <Text style={styles.title}>ABIS EXPORTS INDIA PVT LTD</Text>
            <Text style={styles.center}>{branchData?.BranchName || ''}</Text>
            <Text style={styles.center}>{branchData?.AreaName || ''}</Text>
            <Text style={styles.center}>{branchData?.PINCODE || ''}</Text>
            <Text style={styles.center}>PhoneNo :</Text>
            <Text style={styles.center}>{'<<EXPENSES>>'}</Text>

            {expenseTypes.length > 0 && (
                <Text style={styles.center}>(TYPE : {expenseTypes.join(', ')})</Text>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>TranNo : {header.DailyExpenseHdrid || ''}</Text>
                <Text>Trans Date: {header.BusinessDate ? new Date(header.BusinessDate).toLocaleDateString('en-GB') : ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Location: {branchData?.BranchId}/{branchData?.BranchName}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Printed On: {new Date().toLocaleDateString('en-GB')} {new Date().toLocaleTimeString()}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.sectionHeader}>Expense Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={[styles.cell, styles.expenseTypeCell]}>Expense Type</Text>
                    <Text style={[styles.cell, styles.paymentModeCell]}>Payment Mode</Text>
                    <Text style={[styles.cell, styles.amountCell]}>Amount</Text>
                    <Text style={[styles.cell, styles.remarksCell]}>Remarks</Text>
                </View>
                {expenseData.details.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={[styles.cell, styles.expenseTypeCell]}>{item.ExpenseHeadName}</Text>
                        <Text style={[styles.cell, styles.paymentModeCell]}>{item.PaymentGatewayName}</Text>
                        <Text style={[styles.cell, styles.amountCell]}>{(parseFloat(item.Amount) || 0).toFixed(2)}</Text>
                        <Text style={[styles.cell, styles.remarksCell]}>{item.Remarks || '-'}</Text>
                    </View>
                ))}
            </View>

            <View style={styles.divider} />

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>TOTAL:</Text>
                <Text style={styles.bold}>{totalAmount.toFixed(2)}</Text>
            </View>

            {header.Remarks && (
                <View style={styles.remarksContainer}>
                    <Text style={styles.bold}>Remarks:</Text>
                    <Text>{header.Remarks}</Text>
                </View>
            )}

            {/* Printer Controls */}
            <View style={styles.printerControlsContainer}>
                <View style={styles.printerRow}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                            data={availablePrinters}
                            maxHeight={300}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={item => {
                                setSelectedPrinter(item.value);
                            }}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={() => scanForPrinters(true)}
                        disabled={isScanning}
                    >
                        {isScanning ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <Icon name="refresh" size={20} color="#fff" />
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printExpense}
                        disabled={isPrinting || !selectedPrinter}
                    >
                        {isPrinting ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Icon name="print" size={20} color="#fff" />
                                <Text style={styles.buttonText}>Print</Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },
    container: {
        padding: 16,
        backgroundColor: '#fff',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginTop: 40,
    },
    center: {
        textAlign: 'center',
        marginVertical: 2,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginVertical: 4,
    },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 8,
    },
    table: {
        borderWidth: 1,
        borderColor: '#000',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#000',
        minHeight: 40,
        alignItems: 'center',
    },
    cell: {
        padding: 8,
        textAlign: 'center',
        borderRightWidth: 1,
        borderRightColor: '#000',
        fontSize: 12,
    },
    expenseTypeCell: {
        flex: 2,
        textAlign: 'left',
    },
    paymentModeCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    amountCell: {
        flex: 1,
        textAlign: 'right',
    },
    remarksCell: {
        flex: 2,
        textAlign: 'left',
        borderRightWidth: 0,
    },
    bold: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#000',
    },
    remarksContainer: {
        marginTop: 16,
        padding: 8,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
    },
    printerControlsContainer: {
        marginTop: 20,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default ViewPaymentScreen;
