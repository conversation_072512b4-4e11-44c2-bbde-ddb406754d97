import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    FlatList,
    ActivityIndicator,
    Alert,
    ScrollView,
    SafeAreaView,
    Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';
import axios from 'axios';
import ScrollOptionsBar from '../../components/ScrollOptionsBar';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';

const OpeningAttendanceScreen = () => {
    const [customers, setCustomers] = useState([]);
    const [attendanceTypes, setAttendanceTypes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [userAttendanceData, setUserAttendanceData] = useState({});
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [approverName, setApproverName] = useState('');
    const [businessDate, setBusinessDate] = useState('');
    const navigation = useNavigation();

    useEffect(() => {
        fetchInitialData();
        loadApproverName();
        loadBusinessDate();
    }, []);

    const loadApproverName = async () => {
        try {
            const userDetails = await AsyncStorage.getItem('userData');
            const user = JSON.parse(userDetails);
                        console.log('------',userDetails,'------',user,'------');

            setApproverName(user?.UserName || user?.userName || user?.name || 'Unknown User');
        } catch (error) {
            console.error('Error loading approver name:', error);
            setApproverName('Unknown User');
        }
    };

    const loadBusinessDate = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const branch = selectedBranch ? JSON.parse(selectedBranch) : {};
            const branchId = branch.BranchId || branch.id || branch.branchId || 'L101';
            const date = await fetchBusinessDay(token, branchId);
            setBusinessDate(date); // date format: DD-MM-YYYY
        } catch (error) {
            console.error('Error loading business date:', error);
            setBusinessDate(new Date().toLocaleDateString());
        }
    };

    const fetchInitialData = async () => {
        try {
            setLoading(true);
            await Promise.all([fetchCustomers(), fetchAttendanceTypes()]);
        } catch (error) {
            console.error('Error fetching initial data:', error);
            Alert.alert('Error', 'Failed to load data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const fetchCustomers = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranchData = await AsyncStorage.getItem('selectedBranch');
            const branchData = JSON.parse(selectedBranchData);
            const branchId = branchData?.BranchId || 'L101';

            const response = await axios.get(
                `https://retailuat.abisibg.com/api/v1/contractemplist?BranchID=${branchId}&CompanyCode=ALL&vehicleName=ALL`,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const employeeData = response.data;
            console.log('Customers response:', employeeData);

            if (Array.isArray(employeeData) && employeeData.length > 0) {
                setCustomers(employeeData);
                // Initialize user attendance data
                const initialData = {};
                employeeData.forEach(customer => {
                    initialData[customer.userid] = {
                        attendanceType: null,
                        remarks: ''
                    };
                });
                setUserAttendanceData(initialData);
            } else {
                Alert.alert('Info', 'No employees found.');
            }
        } catch (error) {
            console.error('Error fetching customers:', error?.response?.data || error.message);
            Alert.alert('Error', 'Failed to fetch employee list');
        }
    };

    const fetchAttendanceTypes = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const response = await axios.get(
                'https://retailuat.abisibg.com/api/v1/attendancetype',
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const attendanceData = response.data;
            console.log('Attendance types response:', attendanceData);

            if (Array.isArray(attendanceData) && attendanceData.length > 0) {
                setAttendanceTypes(attendanceData);
            }
        } catch (error) {
            console.error('Error fetching attendance types:', error?.response?.data || error.message);
            Alert.alert('Error', 'Failed to fetch attendance types');
        }
    };

    const handleUserSelection = (userId) => {
        setSelectedUserId(userId);
    };

    const handleAttendanceTypeSelection = (attendanceType) => {
        if (!selectedUserId) {
            Alert.alert('Warning', 'Please select a user first');
            return;
        }

        setUserAttendanceData(prev => ({
            ...prev,
            [selectedUserId]: {
                ...prev[selectedUserId],
                attendanceType: attendanceType
            }
        }));
    };

    const handleRemarksChange = (userId, remarks) => {
        setUserAttendanceData(prev => ({
            ...prev,
            [userId]: {
                ...prev[userId],
                remarks: remarks
            }
        }));
    };

    const handleEdit = async () => {
        // Get users with attendance type selected
        const usersWithAttendance = customers.filter(customer => {
            const userData = userAttendanceData[customer.userid];
            return userData && userData.attendanceType;
        });

        if (usersWithAttendance.length === 0) {
            Alert.alert('Warning', 'Please select attendance type for at least one user');
            return;
        }

        try {
            setSubmitting(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');
            const selectedBranchData = await AsyncStorage.getItem('selectedBranch');
            const userInfo = JSON.parse(userData);
            const branchData = JSON.parse(selectedBranchData);
            const branchId = branchData?.BranchId || 'L101';
            console.log('------',userInfo,'------',userData,'------');
            
            // Format attendance details - Use local time for all dates
            const now = new Date();
            const offset = now.getTimezoneOffset();
            const localNow = new Date(now.getTime() - (offset * 60 * 1000));
            const currentDate = localNow.toISOString();
            
            // Convert business date from DD-MM-YYYY to local time format
            const convertBusinessDateToLocalTime = (businessDateStr) => {
                if (!businessDateStr) return currentDate;
                try {
                    const [day, month, year] = businessDateStr.split('-');
                    const now = new Date();
                    
                    // Create business date with current local time
                    const businessDateTime = new Date(year, month - 1, day, now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
                    
                    // Format as local time ISO string (without timezone conversion)
                    const offset = businessDateTime.getTimezoneOffset();
                    const localTime = new Date(businessDateTime.getTime() - (offset * 60 * 1000));
                    const localISOString = localTime.toISOString();
                    
                    console.log('Current local time:', now.toString());
                    console.log('Device timezone offset (minutes):', offset);
                    console.log('Business date with local time:', businessDateTime.toString());
                    console.log('Local time ISO (no UTC conversion):', localISOString);
                    
                    return localISOString;
                } catch (error) {
                    console.error('Error converting business date:', error);
                    return currentDate;
                }
            };
            
            const businessDateTime = convertBusinessDateToLocalTime(businessDate);
            
            const attendanceDtls = usersWithAttendance.map((user) => {
                const attendanceData = userAttendanceData[user.userid];
                
                return {
                    empCode: user.userid.toString(),
                    empName: user.UserName,
                    openingTime: currentDate,
                    dayEndTime: "",
                    attTypeCode: attendanceData.attendanceType.AttTypeCode,
                    attTypeName: attendanceData.attendanceType.AttTypeName,
                    openingAttTypeName: attendanceData.attendanceType.AttTypeName,
                    remarks: attendanceData.remarks || "",
                    deleted: "",
                    createdUserId: userInfo?.userId || userInfo?.username || "",
                    createdDate: currentDate,
                    modifiedUserId: userInfo?.userId || userInfo?.username || "",
                    modifiedDate: currentDate,
                    deletedUserId: "",
                    deletedDate: currentDate,
                    dml: "U"
                };
            });

            const requestBody = {
                branchId: branchId.toString(),
                attendanceHdrId: "",
                businessDate: businessDateTime,
                remarks: "Opening Attendance Edit",
                approverID: userInfo?.userId || userInfo?.username || "",
                transTypeId: "ATTOPEN",
                posted: true,
                deleted: "",
                createdUserId: userInfo?.userId || userInfo?.username || "",
                createdDate: currentDate,
                modifiedUserId: userInfo?.userId || userInfo?.username || "",
                modifiedDate: currentDate,
                deletedUserId: "",
                deletedDate: currentDate,
                attendanceDtls: attendanceDtls
            };

            console.log('=== EDIT ATTENDANCE API PAYLOAD ===');
            console.log('POST URL: https://retailUAT.abisaio.com:9001/api/AttIN');
            console.log('Headers: Authorization: Bearer [TOKEN]');
            console.log('Business Date (Original):', businessDate);
            console.log('Business Date (Local Time):', businessDateTime);
            console.log('Current Date (Local Time):', currentDate);
            console.log('All timestamps will use local time');
            console.log('Request Body:');
            console.log(JSON.stringify(requestBody, null, 2));

            const response = await axios.post(
                'https://retailUAT.abisaio.com:9001/api/AttIN',
                requestBody,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            console.log('Edit attendance response:', response.data);

            Alert.alert('Success', `Opening attendance updated for ${usersWithAttendance.length} employees`, [
                { 
                    text: 'OK', 
                    onPress: () => {
                        resetForm();
                    }
                }
            ]);
            
        } catch (error) {
            console.error('Error updating attendance:', error?.response?.data || error.message);
            Alert.alert('Error', 'Failed to update attendance data. Please try again.');
        } finally {
            setSubmitting(false);
        }
    };

    const handleSubmitNew = async () => {
        // Get users with attendance type selected
        const usersWithAttendance = customers.filter(customer => {
            const userData = userAttendanceData[customer.userid];
            return userData && userData.attendanceType;
        });

        if (usersWithAttendance.length === 0) {
            Alert.alert('Warning', 'Please select attendance type for at least one user');
            return;
        }

        try {
            setSubmitting(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');
            const selectedBranchData = await AsyncStorage.getItem('selectedBranch');
            const userInfo = JSON.parse(userData);
            const branchData = JSON.parse(selectedBranchData);
            const branchId = branchData?.BranchId || 'L101';
            
            // Format attendance details - Use local time for all dates
            const now = new Date();
            const offset = now.getTimezoneOffset();
            const localNow = new Date(now.getTime() - (offset * 60 * 1000));
            const currentDate = localNow.toISOString();
            
            // Convert business date from DD-MM-YYYY to local time format
            const convertBusinessDateToLocalTime = (businessDateStr) => {
                if (!businessDateStr) return currentDate;
                try {
                    const [day, month, year] = businessDateStr.split('-');
                    const now = new Date();
                    
                    // Create business date with current local time
                    const businessDateTime = new Date(year, month - 1, day, now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());
                    
                    // Format as local time ISO string (without timezone conversion)
                    const offset = businessDateTime.getTimezoneOffset();
                    const localTime = new Date(businessDateTime.getTime() - (offset * 60 * 1000));
                    const localISOString = localTime.toISOString();
                    
                    console.log('Current local time:', now.toString());
                    console.log('Device timezone offset (minutes):', offset);
                    console.log('Business date with local time:', businessDateTime.toString());
                    console.log('Local time ISO (no UTC conversion):', localISOString);
                    
                    return localISOString;
                } catch (error) {
                    console.error('Error converting business date:', error);
                    return currentDate;
                }
            };
            
            const businessDateTime = convertBusinessDateToLocalTime(businessDate);
            
            const attendanceDtls = usersWithAttendance.map((user) => {
                const attendanceData = userAttendanceData[user.userid];
                
                return {
                    empCode: user.userid.toString(),
                    empName: user.UserName,
                    openingTime: currentDate,
                    dayEndTime: "",
                    attTypeCode: attendanceData.attendanceType.AttTypeCode,
                    attTypeName: attendanceData.attendanceType.AttTypeName,
                    openingAttTypeName: attendanceData.attendanceType.AttTypeName,
                    remarks: attendanceData.remarks || "",
                    deleted: "",
                    createdUserId: userInfo?.userId || userInfo?.username || "",
                    createdDate: currentDate,
                    modifiedUserId: userInfo?.userId || userInfo?.username || "",
                    modifiedDate: currentDate,
                    deletedUserId: "",
                    deletedDate: currentDate,
                    dml: "I"
                };
            });

            const requestBody = {
                branchId: branchId.toString(),
                attendanceHdrId: "",
                businessDate: businessDateTime,
                remarks: "Opening Attendance",
                approverID: userInfo?.userId || userInfo?.username || "",
                transTypeId: "ATTOPEN",
                posted: true,
                deleted: "",
                createdUserId: userInfo?.userId || userInfo?.username || "",
                createdDate: currentDate,
                modifiedUserId: userInfo?.userId || userInfo?.username || "",
                modifiedDate: currentDate,
                deletedUserId: "",
                deletedDate: currentDate,
                attendanceDtls: attendanceDtls
            };

            console.log('=== SAVE ATTENDANCE API PAYLOAD ===');
            console.log('POST URL: https://retailUAT.abisaio.com:9001/api/AttIN');
            console.log('Headers: Authorization: Bearer [TOKEN]');
            console.log('Business Date (Original):', businessDate);
            console.log('Business Date (Local Time):', businessDateTime);
            console.log('Current Date (Local Time):', currentDate);
            console.log('All timestamps will use local time');
            console.log('Request Body:');
            console.log(JSON.stringify(requestBody, null, 2));

            const response = await axios.post(
                'https://retailUAT.abisaio.com:9001/api/AttIN',
                requestBody,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            console.log('Attendance response:', response.data);

            Alert.alert('Success', `Opening attendance submitted for ${usersWithAttendance.length} employees`, [
                { 
                    text: 'OK', 
                    onPress: () => {
                        resetForm();
                    }
                }
            ]);
            
        } catch (error) {
            console.error('Error submitting attendance:', error?.response?.data || error.message);
            Alert.alert('Error', 'Failed to submit attendance data. Please try again.');
        } finally {
            setSubmitting(false);
        }
    };

    const handleSubmit = async () => {
        // Get users with attendance type selected
        const usersWithAttendance = customers.filter(customer => {
            const userData = userAttendanceData[customer.userid];
            return userData && userData.attendanceType;
        });

        if (usersWithAttendance.length === 0) {
            Alert.alert('Warning', 'Please select attendance type for at least one user');
            return;
        }

        try {
            setSubmitting(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');
            const userInfo = JSON.parse(userData);
            
            const submissionPromises = usersWithAttendance.map(async (user) => {
                const attendanceData = userAttendanceData[user.userid];
                
                return axios.post(
                    'https://retailUAT.abisaio.com:9001/api/AttIN', // Replace with actual endpoint
                    {
                        userId: user.userid,
                        userName: user.UserName,
                        attendanceTypeCode: attendanceData.attendanceType.AttTypeCode,
                        attendanceTypeName: attendanceData.attendanceType.AttTypeName,
                        remarks: attendanceData.remarks,
                        submittedBy: userInfo?.userId || userInfo?.username,
                        timestamp: new Date().toISOString(),
                    },
                    {
                        headers: {
                            Authorization: `Bearer ${bearerToken}`,
                            'Content-Type': 'application/json',
                        },
                    }
                );
            });

            await Promise.all(submissionPromises);

            Alert.alert('Success', `Opening attendance submitted for ${usersWithAttendance.length} employees`, [
                { 
                    text: 'OK', 
                    onPress: () => {
                        // Reset form after successful submission
                        setSelectedUserId(null);
                        const resetData = {};
                        customers.forEach(customer => {
                            resetData[customer.userid] = {
                                attendanceType: null,
                                remarks: ''
                            };
                        });
                        setUserAttendanceData(resetData);
                    }
                }
            ]);
            
        } catch (error) {
            console.error('Error submitting attendance:', error);
            Alert.alert('Error', 'Failed to submit attendance data. Please try again.');
        } finally {
            setSubmitting(false);
        }
    };

    const resetForm = () => {
        setSelectedUserId(null);
        const resetData = {};
        customers.forEach(customer => {
            resetData[customer.userid] = {
                attendanceType: null,
                remarks: ''
            };
        });
        setUserAttendanceData(resetData);
    };

    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            handleSubmitNew();
        } else if (option === 'Edit') {
            handleEdit();
        } else if (option === 'View') {
            // Logic for view - could show submitted attendance records
        } else if (option === 'Cancel') {
            resetForm();
        }
    };

    const renderUserItem = ({ item }) => {
        const isSelected = selectedUserId === item.userid;
        const userData = userAttendanceData[item.userid] || {};
        const hasAttendanceType = userData.attendanceType;
        
        return (
            <TouchableOpacity
                style={[
                    styles.userItem, 
                    isSelected && styles.selectedUserItem,
                    hasAttendanceType && styles.userItemWithAttendance
                ]}
                onPress={() => handleUserSelection(item.userid)}
                activeOpacity={0.7}
            >
                {/* User Info */}
                <View style={styles.userInfo}>
                    <Text style={[styles.userName, isSelected && styles.selectedUserName]}>
                        {item.UserName}
                    </Text>
                    <Text style={[styles.userId, isSelected && styles.selectedUserId]}>
                        ID: {item.userid}
                    </Text>
                </View>
                
                {/* Attendance Type Display */}
                <View style={styles.attendanceTypeDisplay}>
                    {hasAttendanceType ? (
                        <Text style={styles.attendanceTypeLabel}>
                            {userData.attendanceType.AttTypeName}
                        </Text>
                    ) : (
                        <Text style={styles.noAttendanceLabel}>
                            No Attendance
                        </Text>
                    )}
                </View>

                {/* Remarks Field - Same Row */}
                <View style={styles.remarksSection}>
                    {hasAttendanceType ? (
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Enter remarks..."
                            value={userData.remarks}
                            onChangeText={(text) => handleRemarksChange(item.userid, text)}
                            multiline={true}
                            numberOfLines={2}
                            textAlignVertical="top"
                        />
                    ) : (
                        <Text style={styles.noRemarksLabel}>-</Text>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    const renderAttendanceTypeButton = (attendanceType) => {
        const selectedUserData = userAttendanceData[selectedUserId];
        const isSelected = selectedUserData?.attendanceType?.AttTypeCode === attendanceType.AttTypeCode;
        
        return (
            <TouchableOpacity
                key={attendanceType.AttTypeCode}
                style={[
                    styles.attendanceTypeButton,
                    isSelected && styles.selectedAttendanceTypeButton,
                    !selectedUserId && styles.disabledAttendanceTypeButton
                ]}
                onPress={() => handleAttendanceTypeSelection(attendanceType)}
                disabled={!selectedUserId}
                activeOpacity={0.7}
            >
                <Text style={[
                    styles.attendanceTypeText,
                    isSelected && styles.selectedAttendanceTypeText,
                    !selectedUserId && styles.disabledAttendanceTypeText
                ]}>
                    {attendanceType.AttTypeName}
                </Text>
            </TouchableOpacity>
        );
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <Navbar />
                <View style={styles.loadingContent}>
                    <ActivityIndicator size="large" color="#28a745" />
                    <Text style={styles.loadingText}>Loading employees and attendance types...</Text>
                </View>
            </View>
        );
    }

    const selectedUser = customers.find(c => c.userid === selectedUserId);

    // Full Screen Loader Component
    const renderFullScreenLoader = () => (
        <Modal
            visible={submitting}
            transparent={true}
            animationType="fade"
            statusBarTranslucent={true}
        >
            <View style={styles.fullScreenLoaderContainer}>
                <View style={styles.loaderContent}>
                    <ActivityIndicator size="large" color="#28a745" />
                    <Text style={styles.loaderText}>Saving Attendance...</Text>
                </View>
            </View>
        </Modal>
    );

    return (
        <SafeAreaView style={styles.container}>
            <Navbar />
            <ScrollOptionsBar
                title="Opening Attendance"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
                options={["New", "Save", "Edit", "View", "Cancel"]}
            />
            
            {/* <View style={styles.headerRow}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-left" size={24} color="#2c3e50" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>OPENING ATTENDANCE</Text>
                <View style={styles.headerSpacer} />
            </View> */}

            {/* Upper Scrollable Section */}
            <View style={styles.upperSection}>
                <View style={styles.section}>
                    <View style={styles.headerContainer}>
                        <Text style={styles.sectionTitle}>SELECT EMPLOYEE</Text>
                        <Text style={styles.approverLabel}>Approved by: {approverName}</Text>
                    </View>
                    <FlatList
                        data={customers}
                        renderItem={renderUserItem}
                        keyExtractor={(item) => String(item.userid)}
                        style={styles.userList}
                        showsVerticalScrollIndicator={true}
                        contentContainerStyle={styles.userListContainer}
                    />
                </View>
            </View>

            {/* Lower Fixed Section */}
            <View style={styles.lowerSection}>
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>ATTENDANCE TYPE</Text>
                    {selectedUser && (
                        <Text style={styles.selectedUserLabel}>
                            For: {selectedUser.UserName} (ID: {selectedUser.userid})
                        </Text>
                    )}
                    <View style={styles.attendanceTypesContainer}>
                        {attendanceTypes.map(renderAttendanceTypeButton)}
                    </View>
                </View>
            </View>

            {/* Submit Button */}
            {/* <View style={styles.submitContainer}>
                <TouchableOpacity
                    style={[styles.submitButton, submitting && styles.disabledSubmitButton]}
                    onPress={handleSubmit}
                    disabled={submitting}
                    activeOpacity={0.8}
                >
                    {submitting ? (
                        <ActivityIndicator color="white" size="small" />
                    ) : (
                        <Text style={styles.submitButtonText}>
                            SUBMIT ATTENDANCE
                        </Text>
                    )}
                </TouchableOpacity>
            </View> */}
            
            {/* Full Screen Loader */}
            {renderFullScreenLoader()}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        
    },
    loadingContainer: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    loadingContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 18,
        color: '#666',
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    headerTitle: {
        fontSize: 22,
        fontWeight: '700',
        color: '#2c3e50',
        marginLeft: 16,
        flex: 1,
    },
    headerSpacer: {
        width: 24,
    },
    upperSection: {
        flex: 3,
        paddingHorizontal: 20,
        paddingTop: 16,
    },
    lowerSection: {
        flex: 2,
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    section: {
        flex: 1,
        marginVertical: 16,
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '700',
        color: '#2c3e50',
        letterSpacing: 0.5,
    },
    approverLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007bff',
        backgroundColor: '#e3f2fd',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 15,
        overflow: 'hidden',
    },
    userList: {
        flex: 1,
    },
    userListContainer: {
        paddingBottom: 10,
        flexGrow: 1,
    },
    userItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 16,
        marginVertical: 6,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        borderWidth: 2,
        borderColor: '#e0e0e0',
    },
    selectedUserItem: {
        borderColor: '#28a745',
        backgroundColor: '#f8fff8',
    },
    userItemWithAttendance: {
        borderColor: '#007bff',
        backgroundColor: '#f0f8ff',
    },
    userInfo: {
        flex: 3,
    },
    userName: {
        fontSize: 18,
        fontWeight: '600',
        color: '#2c3e50',
        marginBottom: 4,
    },
    selectedUserName: {
        color: '#28a745',
    },
    userId: {
        fontSize: 14,
        color: '#6c757d',
    },
    selectedUserId: {
        color: '#28a745',
    },
    attendanceTypeDisplay: {
        flex: 2,
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    attendanceTypeLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#007bff',
        backgroundColor: '#e3f2fd',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        textAlign: 'center',
    },
    noAttendanceLabel: {
        fontSize: 12,
        color: '#6c757d',
        fontStyle: 'italic',
    },
    remarksSection: {
        flex: 4,
        // paddingHorizontal: 10,
    },
    remarksInput: {
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#dee2e6',
        paddingHorizontal: 12,
        paddingVertical: 8,
        fontSize: 14,
        minHeight: 60,
        maxHeight: 60,
    },
    noRemarksLabel: {
        fontSize: 14,
        color: '#6c757d',
        textAlign: 'center',
        paddingVertical: 18,
        fontStyle: 'italic',
    },
    selectedUserLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#28a745',
        marginBottom: 12,
        backgroundColor: '#e8f5e8',
        padding: 10,
        borderRadius: 8,
    },
    attendanceTypesContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingHorizontal: 5,
    },
    attendanceTypeButton: {
        backgroundColor: '#ffffff',
        borderRadius: 10,
        padding: 12,
        width: '23%',
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        borderWidth: 2,
        borderColor: '#e0e0e0',
        minHeight: 60,
        marginBottom: 8,
    },
    selectedAttendanceTypeButton: {
        backgroundColor: '#28a745',
        borderColor: '#28a745',
    },
    disabledAttendanceTypeButton: {
        backgroundColor: '#f8f9fa',
        borderColor: '#dee2e6',
    },
    attendanceTypeText: {
        fontSize: 13,
        fontWeight: '600',
        color: '#2c3e50',
        textAlign: 'center',
        lineHeight: 16,
    },
    selectedAttendanceTypeText: {
        color: '#ffffff',
    },
    disabledAttendanceTypeText: {
        color: '#adb5bd',
    },
    submitContainer: {
        padding: 20,
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    submitButton: {
        backgroundColor: '#28a745',
        paddingVertical: 20,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
    },
    disabledSubmitButton: {
        backgroundColor: '#9e9e9e',
    },
    submitButtonText: {
        color: '#ffffff',
        fontSize: 18,
        fontWeight: '700',
        letterSpacing: 1,
    },
    submitContainer: {
        padding: 20,
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    submitButton: {
        backgroundColor: '#28a745',
        paddingVertical: 20,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
    },
    disabledSubmitButton: {
        backgroundColor: '#9e9e9e',
    },
    submitButtonText: {
        color: '#ffffff',
        fontSize: 18,
        fontWeight: '700',
        letterSpacing: 1,
    },
    fullScreenLoaderContainer: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    loaderContent: {
        backgroundColor: '#ffffff',
        padding: 30,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 5 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        minWidth: 200,
    },
    loaderText: {
        marginTop: 15,
        fontSize: 16,
        fontWeight: '600',
        color: '#28a745',
        textAlign: 'center',
    },
});

export default OpeningAttendanceScreen;