import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    FlatList,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';
import { fetchBusinessDay } from '../../../apiHandling/businessDayAPI';

export const TripExpenses = () => {
    // Trip Details states
    const [cashOnHand, setCashOnHand] = useState('');
    const [selectedTripType, setSelectedTripType] = useState('');
    const [selectedTripTypeCode, setSelectedTripTypeCode] = useState(''); // Store the code separately
    const [tripId, setTripId] = useState('');
    const [tripDetails, setTripDetails] = useState('');

    // Expense Details states
    const [selectedExpenseHead, setSelectedExpenseHead] = useState('');
    const [selectedPaymentMode, setSelectedPaymentMode] = useState('');
    const [selectedPaymentModeId, setSelectedPaymentModeId] = useState(''); // Store the ID separately
    const [availableAmount, setAvailableAmount] = useState('');
    const [amount, setAmount] = useState('');
    const [dieselLiter, setDieselLiter] = useState('');
    const [odometerReading, setOdometerReading] = useState('');
    const [remarks, setRemarks] = useState('');
    const [totalAmount, setTotalAmount] = useState('0');

    // Table states
    const [expenseItems, setExpenseItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);

    // API states
    const [tripTypes, setTripTypes] = useState([]);
    const [tripIds, setTripIds] = useState([]);
    const [paymentModes, setPaymentModes] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentBranch, setCurrentBranch] = useState(null);

    // ScrollOptionsBar state
    const [selectedScrollOption, setSelectedScrollOption] = useState('New');

    // Dropdown options
    const expenseHeadOptions = ['Fuel', 'Toll', 'Food', 'Maintenance', 'Others'];

    // New state for business date
    const [businessDate, setBusinessDate] = useState('');

    // Filter modal data when search term changes
    useEffect(() => {
        console.log('🔍 Filtering modal data with search term:', searchTerm);
        if (searchTerm && modalData.length > 0) {
            const filtered = modalData.filter(item => {
                let searchValue = '';
                if (typeof item === 'string') {
                    searchValue = item.toLowerCase();
                } else if (item.TripTypeName) {
                    searchValue = item.TripTypeName.toLowerCase();
                } else if (item.PaymentGatewayName) {
                    searchValue = item.PaymentGatewayName.toLowerCase();
                } else if (item.tripID) {
                    searchValue = item.tripID.toLowerCase();
                } else if (item.branchName) {
                    searchValue = item.branchName.toLowerCase();
                } else if (item.DocID) {
                    searchValue = item.DocID.toLowerCase();
                }
                return searchValue.includes(searchTerm.toLowerCase());
            });
            console.log('🔍 Filtered results count:', filtered.length);
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    // Reset search when modal opens/closes
    useEffect(() => {
        if (modalVisible) {
            setSearchTerm('');
            setFilteredModalData(modalData);
        }
    }, [modalVisible, modalData]);

    // Fetch trip types from API
    const fetchTripTypes = async () => {
        try {
            setLoading(true);
            const token = await AsyncStorage.getItem('authToken');
            
            if (!token) {
                Alert.alert('Error', 'Authentication token not found');
                return;
            }

            const response = await fetch('https://retailuat.abisibg.com/api/v1/triptype', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const data = await response.json();
                console.log('🚗 Trip types fetched:', data);
                setTripTypes(data);
            } else {
                console.error('Failed to fetch trip types:', response.status);
                Alert.alert('Error', 'Failed to fetch trip types');
            }
        } catch (error) {
            console.error('Error fetching trip types:', error);
            Alert.alert('Error', 'Network error while fetching trip types');
        } finally {
            setLoading(false);
        }
    };

    // Handle ScrollOptionsBar option press
    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            saveTrip();
        } else if (option === 'View') {
            // Logic for view
            console.log('View option selected');
        } else if (option === 'Cancel') {
            resetForm();
        }
    };

    // Reset form function
    const resetForm = () => {
        setCashOnHand('');
        setSelectedTripType('');
        setSelectedTripTypeCode('');
        setTripId('');
        setTripDetails('');
        setSelectedExpenseHead('');
        setSelectedPaymentMode('');
        setSelectedPaymentModeId('');
        setAvailableAmount('');
        setAmount('');
        setDieselLiter('');
        setOdometerReading('');
        setRemarks('');
        setTotalAmount('0');
        setExpenseItems([]);
        setSelectedRows([]);
        console.log('Form reset completed');
    };

    // Save trip function (calls the POST API)
    const saveTrip = async () => {
        await submitExpenseData();
    };

    // Fetch trip IDs from API
    const fetchTripIds = async () => {
        try {
            setLoading(true);
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            
            if (!token) {
                Alert.alert('Error', 'Authentication token not found');
                return;
            }

            let branchId = 'L102'; // Default branch ID
            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                console.log('🏢 Selected Branch object:', branch);
                setCurrentBranch(branch);
                
                // Extract branch ID - try multiple possible property names
                if (branch.id) {
                    branchId = branch.id;
                    console.log('✅ Using branch.id:', branchId);
                } else if (branch.branchId) {
                    branchId = branch.branchId;
                    console.log('✅ Using branch.branchId:', branchId);
                }
            }

            // Use businessDate for both fromDate and toDate
            // Format: DD-MM-YYYY → YYYYMMDD
            let businessDateStr = '';
            if (businessDate) {
                const [day, month, year] = businessDate.split('-');
                businessDateStr = `${year}${month}${day}`;
            } else {
                // fallback to today if businessDate not loaded yet
                const today = new Date();
                businessDateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
            }
            console.log('---- Business Date String:', businessDateStr);
            
            const apiUrl = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${businessDateStr}/${businessDateStr}`;
            console.log('🔗 Trip API URL:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const data = await response.json();
                console.log('🆔 Trip IDs fetched:', data);
                setTripIds(data);
            } else {
                console.error('Failed to fetch trip IDs:', response.status);
                Alert.alert('Error', 'Failed to fetch trip IDs');
            }
        } catch (error) {
            console.error('Error fetching trip IDs:', error);
            Alert.alert('Error', 'Network error while fetching trip IDs');
        } finally {
            setLoading(false);
        }
    };

    // Fetch payment modes from API
    const fetchPaymentModes = async () => {
        try {
            setLoading(true);
            const token = await AsyncStorage.getItem('authToken');
            
            if (!token) {
                Alert.alert('Error', 'Authentication token not found');
                return;
            }

            const response = await fetch('https://retailuat.abisibg.com/api/v1/paymentmode', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const data = await response.json();
                console.log('💳 Payment modes fetched:', data);
                setPaymentModes(data);
            } else {
                console.error('Failed to fetch payment modes:', response.status);
                Alert.alert('Error', 'Failed to fetch payment modes');
            }
        } catch (error) {
            console.error('Error fetching payment modes:', error);
            Alert.alert('Error', 'Network error while fetching payment modes');
        } finally {
            setLoading(false);
        }
    };

    // Load selected branch and auth data from AsyncStorage on component mount
    useEffect(() => {
        // Fetch business date on mount
        const loadBusinessDate = async () => {
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const branch = selectedBranch ? JSON.parse(selectedBranch) : {};
            const branchId = branch.BranchId || branch.id || branch.branchId || 'L102';
            const date = await fetchBusinessDay(token, branchId);
            setBusinessDate(date); // date format: DD-MM-YYYY
        };
        loadBusinessDate();
    }, []);

    // useEffect to fetch all data on component mount
    useEffect(() => {
        fetchTripTypes();
        fetchTripIds();
        fetchPaymentModes();
    }, []);

    // Submit expense data to API
    const submitExpenseData = async () => {
        try {
            // Validation
            if (!tripId) {
                Alert.alert('Validation Error', 'Please select a Trip ID');
                return;
            }
            
            if (expenseItems.length === 0) {
                Alert.alert('Validation Error', 'Please add at least one expense item');
                return;
            }

            setLoading(true);
            const token = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            
            if (!token) {
                Alert.alert('Error', 'Authentication token not found');
                return;
            }

            // Get branch ID
            let branchId = selectedBranch; // Default branch ID
            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                if (branch.id) {
                    branchId = branch.id;
                } else if (branch.branchId) {
                    branchId = branch.branchId;
                }
            }

            // Get user ID
            let userId = '';
            if (userData) {
                const user = JSON.parse(userData);
                userId = user.userId || user.id || '';
            }

            // Format businessDate to YYYY-MM-DD for API
            const [day, month, year] = businessDate.split('-');
            const formattedBusinessDate = `${year}-${month}-${day}T00:00:00.000Z`;
            const todayISO = new Date().toISOString();

            // Prepare expense details array
            const expenseDtls = expenseItems.map((item, index) => ({
                lineNumber: (index + 1).toString(),
                expenseHeadName: item.name,
                expenseGroupName: "", // You might need to map this based on expense head
                remarks: item.remarks || "",
                billMonth: "", // You might want to add this field to your form
                unitsConsumed: parseFloat(dieselLiter || 0),
                pumpOdoReading: parseFloat(odometerReading || 0),
                paymentMode: selectedPaymentMode,
                amount: parseFloat(amount || 0),
                orderedBy: userId,
                vehicleNo: "", // You might want to extract this from trip details
                deleted: "",
                expenseHeadId: "", // You might need to map this based on expense head
                paymentGateWayID: selectedPaymentModeId,
                expenseGroupID: "", // You might need to map this based on expense head
                createdUserId: userId,
                createdDate: new Date().toISOString(),
                modifiedUserId: userId,
                modifiedDate: new Date().toISOString(),
                deletedUserId: "",
                deletedDate: new Date().toISOString(),
                dml: "I" // I for Insert, U for Update, D for Delete
            }));

            // Prepare main expense object
            const expenseData = {
                dailyExpenseHdrid: "",
                branchID: branchId,
                remarks: tripDetails,
                totalAmount: parseFloat(totalAmount || 0),
                businessDate: formattedBusinessDate, // Use business date from API
                deleted: "",
                isTripExpense: true,
                vehicleTripID: tripId,
                isO_Number: "",
                createdUserId: userId,
                createdDate: todayISO, // Use today's date for createdDate
                modifiedUserId: userId,
                modifiedDate: todayISO,
                deletedUserId: "",
                deletedDate: todayISO,
                expenseDtls: expenseDtls
            };

            console.log('📤 Submitting expense data:', JSON.stringify(expenseData, null, 2));

            const response = await fetch('https://retailUAT.abisaio.com:9001/api/EXPN', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(expenseData),
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Expense submitted successfully:', result);
                Alert.alert(
                    'Success', 
                    'Expense data submitted successfully!',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                // Clear all form data after successful submission
                                resetForm();
                                setSelectedScrollOption('New'); // Reset to New option after save
                            }
                        }
                    ]
                );
            } else {
                const errorData = await response.text();
                console.error('❌ Failed to submit expense:', response.status, errorData);
                Alert.alert('Error', `Failed to submit expense data: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Error submitting expense:', error);
            Alert.alert('Error', 'Network error while submitting expense data');
        } finally {
            setLoading(false);
        }
    };

    const handleTripClear = () => {
        setCashOnHand('');
        setSelectedTripType('');
        setSelectedTripTypeCode('');
        setTripId('');
        setTripDetails('');
    };

    const handleExpenseClear = () => {
        setSelectedExpenseHead('');
        setSelectedPaymentMode('');
        setSelectedPaymentModeId('');
        setAvailableAmount('');
        setAmount('');
        setDieselLiter('');
        setOdometerReading('');
        setRemarks('');
    };

    const handleAddExpense = () => {
        if (!selectedExpenseHead || !amount) {
            Alert.alert('Validation Error', 'Please select expense head and enter amount');
            return;
        }

        const newItem = {
            id: expenseItems.length + 1,
            name: selectedExpenseHead,
            nos: '1',
            kgs: '-',
            remarks: remarks
        };

        setExpenseItems([...expenseItems, newItem]);
        handleExpenseClear();
        
        // Update total amount
        const newTotal = parseFloat(totalAmount) + parseFloat(amount || 0);
        setTotalAmount(newTotal.toString());
    };

    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newItems = expenseItems.filter((_, index) => !selectedRows.includes(index));
        setExpenseItems(newItems);
        setSelectedRows([]);
        
        // Recalculate total amount
        calculateTotalAmount(newItems);
    };

    const calculateTotalAmount = (items) => {
        // In a real app, you would sum up the amounts from the items
        // For this example, we'll just use a placeholder value
        setTotalAmount((items.length * 100).toString());
    };

    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
        } else {
            setSelectedRows([...selectedRows, index]);
        }
    };

    const openModal = (type) => {
        switch (type) {
            case 'tripType':
                setModalData(tripTypes);
                break;
            case 'expenseHead':
                setModalData(expenseHeadOptions);
                break;
            case 'paymentMode':
                setModalData(paymentModes);
                break;
            case 'tripId':
                setModalData(tripIds);
                break;
            default:
                setModalData([]);
        }
        setModalType(type);
        setModalVisible(true);
    };

    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'tripType':
                setSelectedTripType(item.TripTypeName);
                setSelectedTripTypeCode(item.TripTypeCode);
                console.log('Selected Trip Type:', item.TripTypeName, 'Code:', item.TripTypeCode);
                break;
            case 'expenseHead':
                setSelectedExpenseHead(item);
                break;
            case 'paymentMode':
                setSelectedPaymentMode(item.PaymentGatewayName);
                setSelectedPaymentModeId(item.PaymentGatewayId);
                console.log('Selected Payment Mode:', item.PaymentGatewayName, 'ID:', item.PaymentGatewayId);
                break;
            case 'tripId':
                setTripId(item.tripID);
                // You can also store other trip details if needed
                console.log('Selected Trip ID:', item.tripID);
                break;
        }
        setModalVisible(false);
    };

    /**
     * Gets the appropriate display text for modal items based on type
     */
    const getModalItemDisplayText = (item, modalType) => {
        if (typeof item === 'object') {
            switch (modalType) {
                case 'tripType':
                    return item.TripTypeName;
                case 'paymentMode':
                    return item.PaymentGatewayName;
                case 'tripId':
                    return item.tripID;
                default:
                    return item.name || item.title || 'Unknown';
            }
        }
        return item;
    };

    /**
     * Renders different modal titles based on selection type
     */
    const renderModalTitle = () => {
        switch (modalType) {
            case 'tripType': return 'Select Trip Type';
            case 'expenseHead': return 'Select Expense Head';
            case 'paymentMode': return 'Select Payment Mode';
            case 'tripId': return 'Select Trip ID';
            default: return 'Select Item';
        }
    };

    /**
     * Renders modal items in card format
     */
    const renderModalCards = () => (
        <View style={styles.modalItemsContainer}>
            {filteredModalData.length > 0 ? (
                filteredModalData.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.modalItem}
                        onPress={() => handleModalItemSelect(item)}
                    >
                        <Text style={styles.modalItemText}>
                            {getModalItemDisplayText(item, modalType)}
                        </Text>
                        {/* Show additional info for trip IDs */}
                        {/* {modalType === 'tripId' && typeof item === 'object' && (
                            <Text style={styles.modalItemSubText}>
                                {item.vehicleNo} - {new Date(item.tripDate).toLocaleDateString()}
                            </Text>
                        )} */}
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>
                        {loading ? 'Loading...' : 'No items found'}
                    </Text>
                </View>
            )}
        </View>
    );

    // Fetch Cash on Hand (CAASH) when businessDate or branch changes
    useEffect(() => {
        const fetchCashOnHand = async () => {
            try {
                const token = await AsyncStorage.getItem('authToken');
                const selectedBranch = await AsyncStorage.getItem('selectedBranch');
                const branch = selectedBranch ? JSON.parse(selectedBranch) : {};
                const branchId = branch.BranchId || branch.id || branch.branchId || 'L102';
                if (!businessDate) return;
                // Format businessDate to YYYYMMDD
                const [day, month, year] = businessDate.split('-');
                const businessDateStr = `${year}${month}${day}`;
                const url = `https://retailuat.abisibg.com/api/v1/currentbalance?BranchID=${branchId}&BusinessDayCode=${businessDateStr}&PaymentMethod=CAASH`;
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (response.ok) {
                    const data = await response.json();
                    setCashOnHand(data?.CashBalance?.toString() ?? '0');
                } else {
                    setCashOnHand('0');
                }
            } catch {
                setCashOnHand('0');
            }
        };
        fetchCashOnHand();
    }, [businessDate]);

    // Fetch Available Amount when payment mode changes
    useEffect(() => {
        const fetchAvailableAmount = async () => {
            try {
                const token = await AsyncStorage.getItem('authToken');
                const selectedBranch = await AsyncStorage.getItem('selectedBranch');
                const branch = selectedBranch ? JSON.parse(selectedBranch) : {};
                const branchId = branch.BranchId || branch.id || branch.branchId || 'L102';
                if (!businessDate || !selectedPaymentMode) {
                    setAvailableAmount('');
                    return;
                }
                // Format businessDate to YYYYMMDD
                const [day, month, year] = businessDate.split('-');
                const businessDateStr = `${year}${month}${day}`;
                const url = `https://retailuat.abisibg.com/api/v1/currentbalance?BranchID=${branchId}&BusinessDayCode=${businessDateStr}&PaymentMethod=${selectedPaymentMode}`;
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
                if (response.ok) {
                    const data = await response.json();
                    setAvailableAmount(data?.CashBalance?.toString() ?? '0');
                } else {
                    setAvailableAmount('0');
                }
            } catch {
                setAvailableAmount('0');
            }
        };
        fetchAvailableAmount();
    }, [businessDate, selectedPaymentMode]);

    return (
        <View style={styles.container}>
            <Navbar />
            <ScrollOptionsBar
                title="Trip Expenses"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />
            <ScrollView>
                {/* Trip Details Section */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Details</Text>
                    
                    {/* Single Line Trip Details */}
                    <View style={styles.singleLineFormRow}>
                        {/* Cash on Hand - 20% */}
                        <View style={[styles.inputWrapper, { width: '20%' }]}>
                            <TextInput
                                style={styles.input}
                                value={cashOnHand}
                                editable={false}
                                keyboardType="numeric"
                                placeholder='Cash on hand'
                                placeholderTextColor="#555"
                            />
                        </View>
                        
                        {/* Trip Type - 20% */}
                        <View style={[styles.inputWrapper, { width: '20%' }]}>
                            <TouchableOpacity 
                                style={[styles.dropdown, loading && styles.disabledDropdown]}
                                onPress={() => !loading && openModal('tripType')}
                                disabled={loading}
                            >
                                <Text style={[styles.dropdownText, !selectedTripType && styles.placeholderText]}>
                                    {loading ? 'Loading...' : selectedTripType || 'Select Trip Type'}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>
                        
                        {/* Trip ID - 40% */}
                        <View style={[styles.inputWrapper, { width: '40%' }]}>
                            <TouchableOpacity 
                                style={[styles.dropdown, loading && styles.disabledDropdown]}
                                onPress={() => !loading && openModal('tripId')}
                                disabled={loading}
                            >
                                <Text style={[styles.dropdownText, !tripId && styles.placeholderText]}>
                                    {loading ? 'Loading...' : tripId || 'Select Trip ID'}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>
                        
                        {/* Select Button - 20% */}
                        <View style={[styles.inputWrapper, { width: '20%' }]}>
                            <TouchableOpacity 
                                style={[styles.selectButton, loading && styles.disabledButton]}
                                onPress={() => !loading && openModal('tripId')}
                                disabled={loading}
                            >
                                <Text style={styles.selectButtonText}>
                                    {loading ? 'Loading...' : 'Select'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.tripDetailsContainer}>
                        <TextInput
                            style={styles.tripDetailsInput}
                            multiline
                            value={tripDetails}
                            placeholder='Trip Details'
                            placeholderTextColor="#555"
                            onChangeText={setTripDetails}
                        />
                        <TouchableOpacity style={styles.clearButton} onPress={handleTripClear}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Expense Details Section */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Expense Details</Text>
                    
                    <View style={styles.formRow}>
                        <TouchableOpacity 
                            style={styles.dropdown}
                            onPress={() => openModal('expenseHead')}
                        >
                            <Text style={[styles.dropdownText, !selectedExpenseHead && styles.placeholderText]}>
                                {selectedExpenseHead || 'Select Expense Head'}
                            </Text>
                            <Text style={styles.dropdownIcon}>▼</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                            style={[styles.dropdown, loading && styles.disabledDropdown]}
                            onPress={() => !loading && openModal('paymentMode')}
                            disabled={loading}
                        >
                            <Text style={[styles.dropdownText, !selectedPaymentMode && styles.placeholderText]}>
                                {loading ? 'Loading...' : selectedPaymentMode || 'Select Payment Mode'}
                            </Text>
                            <Text style={styles.dropdownIcon}>▼</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.formRow}>
                        {/* Available Amount input (read-only) */}
                        <TextInput
                            style={styles.amountInput}
                            placeholder="Available Amount"
                            placeholderTextColor="#555"
                            value={availableAmount}
                            editable={false}
                        />
                        {/* Amount input */}
                        <TextInput
                            style={styles.amountInput}
                            placeholder="Amount"
                            placeholderTextColor="#555"
                            value={amount}
                            onChangeText={setAmount}
                            keyboardType="numeric"
                        />
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.amountInput}
                            placeholder="Diesel Liter"
                            placeholderTextColor="#555"
                            value={dieselLiter}
                            onChangeText={setDieselLiter}
                            keyboardType="numeric"
                        />
                        
                        <TextInput
                            style={styles.amountInput}
                            placeholder="Odometer Reading"
                            placeholderTextColor="#555"
                            value={odometerReading}
                            onChangeText={setOdometerReading}
                            keyboardType="numeric"
                        />
                    </View>

                    <View style={styles.remarksContainer}>
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Add Remarks"
                            placeholderTextColor="#555"
                            multiline
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                    </View>

                    <View style={styles.actionButtonRow}>
                        <TouchableOpacity style={styles.addButton} onPress={handleAddExpense}>
                            <Text style={styles.addButtonText}>Add</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity style={styles.clearButton} onPress={handleExpenseClear}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Table Section */}
                <View style={styles.tableContainer}>
                    <View style={styles.tableHeader}>
                        <View style={styles.checkboxCell}>
                            <CheckBox
                                value={false}
                                tintColors={{ true: '#333', false: '#333' }}
                                style={styles.checkbox}
                            />
                        </View>
                        <View style={styles.lineNumberCell}>
                            <Text style={styles.headerText}>LineNumber</Text>
                        </View>
                        <View style={styles.itemNameCell}>
                            <Text style={styles.headerText}>Item Name</Text>
                        </View>
                        <View style={styles.nosCell}>
                            <Text style={styles.headerText}>Nos</Text>
                        </View>
                        <View style={styles.kgsCell}>
                            <Text style={styles.headerText}>Kgs</Text>
                        </View>
                        <View style={styles.remarksCell}>
                            <Text style={styles.headerText}>Remarks</Text>
                        </View>
                    </View>

                    {expenseItems.length > 0 ? (
                        expenseItems.map((item, index) => (
                            <View key={index} style={styles.tableRow}>
                                <View style={styles.checkboxCell}>
                                    <CheckBox
                                        value={selectedRows.includes(index)}
                                        onValueChange={() => handleRowSelection(index)}
                                        tintColors={{ true: '#333', false: '#333' }}
                                        style={styles.checkbox}
                                    />
                                </View>
                                <View style={styles.lineNumberCell}>
                                    <Text style={styles.cellText}>{item.id}</Text>
                                </View>
                                <View style={styles.itemNameCell}>
                                    <Text style={styles.cellText}>{item.name}</Text>
                                </View>
                                <View style={styles.nosCell}>
                                    <Text style={styles.cellText}>{item.nos}</Text>
                                </View>
                                <View style={styles.kgsCell}>
                                    <Text style={styles.cellText}>{item.kgs}</Text>
                                </View>
                                <View style={styles.remarksCell}>
                                    <Text style={styles.cellText}>{item.remarks}</Text>
                                </View>
                            </View>
                        ))
                    ) : (
                        <View style={styles.emptyTableRow}>
                            <Text style={styles.emptyTableText}>No Data</Text>
                        </View>
                    )}
                </View>

                <View style={styles.bottomActionRow}>
                    <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                        <Text style={styles.deleteButtonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                    
                    <View style={styles.totalAmountContainer}>
                        <Text style={styles.totalAmountText}>Total Amount</Text>
                        <Text style={styles.totalAmountValue}>₹ {totalAmount}</Text>
                    </View>
                </View>
            </ScrollView>

            {/* Updated Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {renderModalTitle()}
                        </Text>

                        {/* Search input */}
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>

                        {/* Modal content */}
                        <ScrollView style={styles.modalScrollContainer}>
                            {renderModalCards()}
                        </ScrollView>

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );
};
// Note: You'll need to 

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
     disabledDropdown: {
        opacity: 0.6,
    },
    disabledButton: {
        opacity: 0.6,
    },
    modalItemSubText: {
        fontSize: 12,
        color: '#666',
        marginTop: 2,
    },
    sectionContainer: {
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        padding: 9,
        margin: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: 22,
        fontWeight: '800',
        marginBottom: 8,
        color: '#333',
        textTransform: 'uppercase',
    },
    // New single-line form row for trip details
    singleLineFormRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    inputWrapper: {
        paddingHorizontal: 2,
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
        margin:10
    },
    inputContainer: {
        flex: 1,
        marginRight: 12,
    },
    input: {
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    dropdown: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        margin:5,
        height: 48,
    },
    disabledDropdown: {
        backgroundColor: '#f5f5f5',
        opacity: 0.7,
    },
    dropdownText: {
        color: '#222',
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    placeholderText: {
        color: '#555',
        fontWeight: '500',
    },
    dropdownIcon: {
        color: '#222',
        fontSize: 16,
        fontWeight: 'bold',
    },
    selectButton: {
        backgroundColor: '#041C44',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        height: 48,
    },
    selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    // tripDetailsContainer: {
    //     marginVertical: 15,
    // },
    // tripDetailsInput: {
    //     backgroundColor: '#e8e8e8',
    //     borderRadius: 5,
    //     padding: 12,
    //     height: 48,
    //     borderWidth: 1.5,
    //     borderColor: '#aaa',
    //     fontSize: 16,
    //     fontWeight: '600',
    // },
    // buttonContainer: {
    //     flexDirection: 'row',
    //     justifyContent: 'flex-end',
    //     marginTop: 15,
    // },
    // clearButton: {
    //     backgroundColor: '#ff0000',
    //     paddingVertical: 12,
    //     paddingHorizontal: 50,
    //     borderRadius: 8,
    //     alignItems: 'center',
    // },
    // clearButtonText: {
    //     color: 'white',
    //     fontWeight: 'bold',
    //     fontSize: 16,
    // },
    tripDetailsContainer: {
    marginVertical: 15,
    flexDirection: 'row',
    gap: 10, // Space between TextInput and button
},
tripDetailsInput: {
    backgroundColor: '#e8e8e8',
    borderRadius: 5,
    padding: 12,
    height: 48,
    borderWidth: 1.5,
    borderColor: '#aaa',
    fontSize: 16,
    fontWeight: '600',
    width: '70%',
},
clearButton: {
    backgroundColor: '#ff0000',
    paddingVertical: 12,
    paddingHorizontal: 20, // Reduced padding since width is constrained
    borderRadius: 8,
    alignItems: 'center',
    width: '25%',
    height: 48, // Match the TextInput height
    justifyContent: 'center',
},
clearButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
},

    
    amountInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        marginHorizontal: 6,
        fontSize: 16,
        fontWeight: '600',
    },
    remarksContainer: {
        marginVertical: 15,
    },
    remarksInput: {
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        padding: 12,
        height: 70,
        textAlignVertical: 'top',
        borderWidth: 1.5,
        borderColor: '#aaa',
        fontSize: 16,
        fontWeight: '600',
    },
    actionButtonRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 15,
    },
    addButton: {
        backgroundColor: '#008000',
        paddingVertical: 12,
        paddingHorizontal: 50,
        borderRadius: 8,
        alignItems: 'center',
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    tableContainer: {
        margin: 10,
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        overflow: 'hidden',
        borderWidth: 1.5,
        borderColor: '#ccc',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#d3d3d3',
        borderBottomWidth: 2,
        borderBottomColor: '#999',
        padding: 8,
        height: 45,
    },
    headerText: {
        fontWeight: 'bold',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        padding: 8,
        height: 45,
    },
    cellText: {
        fontSize: 14,
        fontWeight: '600',
    },
    checkboxCell: {
        width: 35,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkbox: {
        transform: [{ scale: 1 }],
    },
    lineNumberCell: {
        width: 90,
        justifyContent: 'center',
        alignItems: 'center',
    },
    itemNameCell: {
        flex: 2,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    nosCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    kgsCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    remarksCell: {
        flex: 1.5,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    emptyTableRow: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableText: {
        color: '#777',
        fontSize: 18,
        fontWeight: '600',
    },
    bottomActionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: 10,
        marginTop: 15,
    },
    deleteButton: {
        backgroundColor: '#ff0000',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    totalAmountContainer: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1.5,
        borderColor: '#aaa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    totalAmountText: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 6,
    },
    totalAmountValue: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#008000',
    },
    // Updated Modal styles from the second document
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    modalScrollContainer: {
        maxHeight: 400,
    },
    
    // Modal Items Container (Card-based layout)
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500', // Yellow background
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333', // Black text
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Empty States
    emptySearchContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    emptySearchText: {
        textAlign: 'center',
        fontFamily: 'Poppins',
        color: '#666',
        marginTop: 20,
        marginBottom: 20,
        fontStyle: 'italic',
    },
    
    // Close Button
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    });

export default TripExpenses;