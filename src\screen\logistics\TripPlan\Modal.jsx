import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Modal as RNModal,
    ActivityIndicator,
    Alert,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBusinessDay } from '../../../apiHandling/businessDayAPI';

const Modal = ({
    visible,
    onClose,
    modalType,
    modalData,
    onItemSelect,
    loading = false,
    onFetchSalesDocuments, // NEW PROP
    onFetchTripApi, // NEW PROP
    businessDate, // NEW PROP for business date
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);
    // Date picker states
    const [fromDate, setFromDate] = useState('');
    const [toDate, setToDate] = useState('');
    const [showFromPicker, setShowFromPicker] = useState(false);
    const [showToPicker, setShowToPicker] = useState(false);
    const [currentBusinessDate, setCurrentBusinessDate] = useState('');
    const [datesInitialized, setDatesInitialized] = useState(false);

    // Filter modal data when search term changes
    useEffect(() => {
        console.log('🔍 Filtering modal data with search term:', searchTerm);
        if (searchTerm && modalData.length > 0) {
            const filtered = modalData.filter(item => {
                let searchValue = '';
                if (typeof item === 'string') {
                    searchValue = item.toLowerCase();
                } else if (item.TransTypeName) {
                    searchValue = item.TransTypeName.toLowerCase();
                } else if (item.branchName) {
                    searchValue = item.branchName.toLowerCase();
                } else if (item.tripID) {
                    searchValue = item.tripID.toLowerCase();
                } else if (item.DocID) {
                    searchValue = item.DocID.toLowerCase();
                } else if (item.displayText) {
                    searchValue = item.displayText.toLowerCase();
                }
                return searchValue.includes(searchTerm.toLowerCase());
            });
            console.log('🔍 Filtered results count:', filtered.length);
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    // Function to initialize dates for selectDoc and tripId modals
    const initializeDatesForSelectDocOrTripId = async () => {
        try {
            if (businessDate) {
                // Parse business date (DD-MM-YYYY format)
                const [day, month, year] = businessDate.split('-');
                // Ensure zero-padding for month and day
                const paddedDay = day.padStart(2, '0');
                const paddedMonth = month.padStart(2, '0');
                // Use local time to avoid timezone issues
                const businessDateObj = new Date(parseInt(year), parseInt(paddedMonth) - 1, parseInt(paddedDay));
                console.log('🔍 Date parsing debug:', {
                    originalBusinessDate: businessDate,
                    parsedParts: { day: paddedDay, month: paddedMonth, year },
                    businessDateObj: businessDateObj.toString(),
                    isoDate: businessDateObj.toISOString().split('T')[0]
                });
                
                if (modalType === 'tripId') {
                    const dateFormatted = businessDateObj.toISOString().split('T')[0];
                    setFromDate(dateFormatted);
                    setToDate(dateFormatted);
                    console.log('✅ Auto-populated dates for tripId modal:');
                    console.log('🗓️ Business Date:', businessDate);
                    console.log('🗓️ From Date:', dateFormatted);
                    console.log('🗓️ To Date:', dateFormatted);
                } else {
                    // For selectDoc: To Date = business date, From Date = business date - 1
                    const toDateFormatted = businessDateObj.toISOString().split('T')[0];
                    setToDate(toDateFormatted);
                    const fromDateObj = new Date(businessDateObj);
                    fromDateObj.setDate(fromDateObj.getDate() - 1);
                    const fromDateFormatted = fromDateObj.toISOString().split('T')[0];
                    setFromDate(fromDateFormatted);
                    console.log('✅ Auto-populated dates for selectDoc modal:');
                    console.log('🗓️ Business Date:', businessDate);
                    console.log('🗓️ From Date (Business Date - 1):', fromDateFormatted);
                    console.log('🗓️ To Date (Business Date):', toDateFormatted);
                    console.log('🔍 SelectDoc debug:', {
                        businessDateObjISO: businessDateObj.toISOString(),
                        toDateFormatted,
                        fromDateObjISO: fromDateObj.toISOString(),
                        fromDateFormatted
                    });
                }
            } else {
                // Fallback: today and today-1
                const today = new Date();
                const todayStr = today.toISOString().split('T')[0];
                const yesterday = new Date(today);
                yesterday.setDate(today.getDate() - 1);
                const yesterdayStr = yesterday.toISOString().split('T')[0];
                setFromDate(yesterdayStr);
                setToDate(todayStr);
                console.log('⚠️ No business date, fallback to today/yesterday:', { fromDate: yesterdayStr, toDate: todayStr });
            }
        } catch (error) {
            console.error('❌ Error initializing dates:', error);
            // Set default dates if error occurs
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            setFromDate(yesterdayStr);
            setToDate(todayStr);
        }
    };

    // Only auto-populate dates for selectDoc or tripId on first open
    useEffect(() => {
        if ((modalType === 'selectDoc' || modalType === 'tripId') && visible && !datesInitialized) {
            setSearchTerm('');
            setFilteredModalData(modalData);
            initializeDatesForSelectDocOrTripId();
            setDatesInitialized(true);
        }
        if (!visible) {
            setDatesInitialized(false); // Reset flag when modal closes
            setFromDate('');
            setToDate('');
        }
    }, [visible, modalType]);

    /**
     * Gets the appropriate display text for modal items based on type
     */
    const getModalItemDisplayText = (item, modalType) => {
        if (typeof item === 'string') {
            return item;
        }
        
        if (typeof item === 'object') {
            switch (modalType) {
                case 'tripBranch':
                    return item.branchName || item.displayText;
                case 'docType':
                    return item.TransTypeName || item.displayText;
                case 'tripId':
                    return item.tripID || item.displayText;
                case 'selectDoc':
                    return item.DocID || item.displayText;
                case 'place':
                    return item.displayText || item.name || item.title;
                default:
                    return item.displayText || item.name || item.title || 'Unknown';
            }
        }
        return item;
    };

    /**
     * Renders different modal titles based on selection type
     */
    const renderModalTitle = () => {
        switch (modalType) {
            case 'tripBranch': return 'Select Trip Branch';
            case 'tripId': return 'Select Trip ID';
            case 'docType': return 'Select DOC Type';
            case 'selectDoc': return 'Select Sales Document';
            case 'place': return 'Select Place';
            default: return 'Select Item';
        }
    };

    /**
     * Handles item selection and closes modal
     */
    const handleItemSelect = (item) => {
        console.log('🎯 Modal item selected:', item, 'for type:', modalType);
        onItemSelect(item);
        onClose();
    };

    /**
     * Renders trip cards for trip ID selection
     */
    const renderTripCards = () => (
        <View style={styles.tripCardsContainer}>
            {loading ? (
                <View style={styles.emptySearchContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.emptySearchText}>Loading trips...</Text>
                </View>
            ) : filteredModalData.length > 0 ? (
                filteredModalData.map((trip, index) => (
                    <TouchableOpacity
                        key={trip.tripID || index}
                        style={styles.tripCard}
                        onPress={() => handleItemSelect(trip)}
                    >
                        <Text style={styles.tripIdText}>{getModalItemDisplayText(trip, modalType)}</Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>No trips found</Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders sales document cards
     */
    const renderSalesDocCards = () => (
        <View style={styles.tripCardsContainer}>
            {loading ? (
                <View style={styles.emptySearchContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.emptySearchText}>Loading sales documents...</Text>
                </View>
            ) : filteredModalData.length > 0 ? (
                filteredModalData.map((doc, index) => (
                    <TouchableOpacity
                        key={doc.DocID || index}
                        style={styles.salesDocCard}
                        onPress={() => handleItemSelect(doc)}
                    >
                        <Text style={styles.salesDocText}>{getModalItemDisplayText(doc, modalType)}</Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>No sales documents found</Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders regular modal items grid
     */
    const renderRegularItems = () => (
        <View style={styles.modalItemsContainer}>
            {filteredModalData.length > 0 ? (
                filteredModalData.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.modalItem}
                        onPress={() => handleItemSelect(item)}
                    >
                        <Text style={styles.modalItemText}>
                            {getModalItemDisplayText(item, modalType)}
                        </Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>
                        {loading ? 'Loading...' : 'No items found'}
                    </Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders modal content based on type
     */
    const renderModalContent = () => {
        switch (modalType) {
            case 'tripId':
                return renderTripCards();
            case 'selectDoc':
                return renderSalesDocCards();
            default:
                return renderRegularItems();
        }
    };

    // Helper to format date as YYYYMMDD
    const formatDateForAPI = (date) => {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        return `${year}${month}${day}`;
    };

    // Helper to format date for display
    const formatDateForDisplay = (date) => {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('en-GB'); // DD/MM/YYYY format
    };

    // Validation function to check if fromDate is not greater than toDate
    const validateDateRange = (fromDateStr, toDateStr) => {
        if (!fromDateStr || !toDateStr) return true; // Allow if either date is empty
        
        const fromDate = new Date(fromDateStr);
        const toDate = new Date(toDateStr);
        
        return fromDate <= toDate;
    };

    // Helper to show date validation error
    const showDateValidationError = () => {
        console.log('❌ Date validation failed: From date cannot be greater than To date');
        Alert.alert('Invalid Date Range', 'From date cannot be greater than To date', [
            { text: 'OK', onPress: () => console.log('User acknowledged date validation error') }
        ]);
    };

    return (
        <RNModal
            animationType="fade"
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>
                        {renderModalTitle()}
                    </Text>

                    {/* FromDate/ToDate pickers and Fetch button for selectDoc and tripId */}
                    {(modalType === 'selectDoc' || modalType === 'tripId') && (
                        <View style={{ marginBottom: 10 }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 0 }}>
                                <TouchableOpacity onPress={() => setShowFromPicker(true)} style={{ flex: 1, marginRight: 5, backgroundColor: '#f5f5f5', borderRadius: 8, padding: 10, borderWidth: 1, borderColor: '#ddd', alignItems: 'center' }}>
                                    <Text style={{ color: fromDate ? '#333' : '#888', fontWeight: 'bold' }}>
                                        {fromDate ? formatDateForDisplay(fromDate) : 'From Date'}
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => setShowToPicker(true)} style={{ flex: 1, marginLeft: 5, backgroundColor: '#f5f5f5', borderRadius: 8, padding: 10, borderWidth: 1, borderColor: '#ddd', alignItems: 'center' }}>
                                    <Text style={{ color: toDate ? '#333' : '#888', fontWeight: 'bold' }}>
                                        {toDate ? formatDateForDisplay(toDate) : 'To Date'}
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={{ 
                                        backgroundColor: (loading || !fromDate || !toDate) ? '#666' : '#041C44', 
                                        borderRadius: 8, 
                                        paddingVertical: 12, 
                                        paddingHorizontal: 18, 
                                        alignItems: 'center', 
                                        marginLeft: 8, 
                                        borderWidth: 1, 
                                        borderColor: '#FDC500', 
                                        height: 48, 
                                        justifyContent: 'center',
                                        opacity: (loading || !fromDate || !toDate) ? 0.7 : 1
                                    }}
                                    onPress={() => {
                                        if (typeof onFetchTripApi === 'function' && fromDate && toDate) {
                                            if (!validateDateRange(fromDate, toDate)) {
                                                console.log('⚠️ Date range validation warning: From date is after To date, but proceeding with API call');
                                            }
                                            onFetchTripApi({
                                                fromDate: formatDateForAPI(fromDate),
                                                toDate: formatDateForAPI(toDate)
                                            });
                                        }
                                    }}
                                    disabled={loading || !fromDate || !toDate}
                                >
                                    <Text style={{ color: 'white', fontWeight: 'bold', fontFamily: 'Poppins', fontSize: 15 }}>
                                        {loading ? 'Loading...' : 'Fetch'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                            {showFromPicker && (
                                <DateTimePicker
                                    value={fromDate ? new Date(fromDate) : new Date()}
                                    mode="date"
                                    display="default"
                                    onChange={(event, selectedDate) => {
                                        setShowFromPicker(false);
                                        if (selectedDate) {
                                            const selectedDateStr = selectedDate.toISOString().split('T')[0];
                                            if (!toDate || validateDateRange(selectedDateStr, toDate)) {
                                                setFromDate(selectedDateStr);
                                            } else {
                                                setFromDate(selectedDateStr);
                                            }
                                        }
                                    }}
                                />
                            )}
                            {showToPicker && (
                                <DateTimePicker
                                    value={toDate ? new Date(toDate) : new Date()}
                                    mode="date"
                                    display="default"
                                    onChange={(event, selectedDate) => {
                                        setShowToPicker(false);
                                        if (selectedDate) {
                                            const selectedDateStr = selectedDate.toISOString().split('T')[0];
                                            if (!fromDate || validateDateRange(fromDate, selectedDateStr)) {
                                                setToDate(selectedDateStr);
                                            } else {
                                                setToDate(selectedDateStr);
                                            }
                                        }
                                    }}
                                />
                            )}
                        </View>
                    )}

                    {/* Search input */}
                    <View style={styles.searchContainer}>
                        <TextInput
                            style={styles.searchInput}
                            placeholder="Search"
                            placeholderTextColor="#888"
                            value={searchTerm}
                            onChangeText={setSearchTerm}
                        />
                    </View>

                    {/* Modal content */}
                    <ScrollView style={styles.modalScrollContainer}>
                        {renderModalContent()}
                    </ScrollView>

                    <TouchableOpacity
                        style={styles.modalCloseButton}
                        onPress={onClose}
                    >
                        <Text style={styles.modalCloseButtonText}>Close</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </RNModal>
    );
};

const styles = StyleSheet.create({
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    modalScrollContainer: {
        maxHeight: 400,
    },
    
    // Trip Cards Container
    tripCardsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    tripCard: {
        backgroundColor: '#FDC500', // Yellow background
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    tripIdText: {
        fontSize: 15,
        color: '#333', // Black text
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Sales Document Cards
    salesDocCard: {
        backgroundColor: '#FDC500', // Yellow background
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    salesDocText: {
        fontSize: 15,
        color: '#333', // Black text
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Regular Modal Items
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Empty States
    emptySearchContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    emptySearchText: {
        textAlign: 'center',
        fontFamily: 'Poppins',
        color: '#666',
        marginTop: 20,
        marginBottom: 20,
        fontStyle: 'italic',
    },
    
    // Close Button
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
});

export default Modal;