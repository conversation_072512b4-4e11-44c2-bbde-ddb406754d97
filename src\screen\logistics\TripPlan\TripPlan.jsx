import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    FlatList,
    ActivityIndicator,
    Modal,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';
import { local_signage, local_url } from '../../../../const';
import { fetchBusinessDay } from '../../../apiHandling/businessDayAPI';

export const TripPlan = () => {
    // Navigation and form states
    const [selectedScrollOption, setSelectedScrollOption] = useState('');

    // Trip header information
    const [selectedTripBranch, setSelectedTripBranch] = useState('');
    const [selectedTripBranchId, setSelectedTripBranchId] = useState('');
    const [tripId, setTripId] = useState('');

    // Document and item details
    const [docType, setDocType] = useState('');
    const [selectedDoc, setSelectedDoc] = useState('');
    const [customerBranch, setCustomerBranch] = useState('');
    const [place, setPlace] = useState('');
    const [remarks, setRemarks] = useState('');
    const [weight, setWeight] = useState('');
    
    // Validation states for inputs
    const [weightError, setWeightError] = useState('');

    // Data table states
    const [items, setItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [isSelectAllItems, setIsSelectAllItems] = useState(false);

    // Modal states for selection interfaces
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    
    // Date picker states for modal
    const [modalFromDate, setModalFromDate] = useState('');
    const [modalToDate, setModalToDate] = useState('');
    const [showFromPicker, setShowFromPicker] = useState(false);
    const [showToPicker, setShowToPicker] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    // Additional state variables
    const [tripDetails, setTripDetails] = useState('');
    const [tripRemarks, setTripRemarks] = useState('');
    const [businessDate, setBusinessDate] = useState('');
    const [vehicleNo, setVehicleNo] = useState('');

    // API data states
    const [docTypeOptions, setDocTypeOptions] = useState([]);
    const [selectedDocTypeId, setSelectedDocTypeId] = useState('');
    const [branchOptions, setBranchOptions] = useState([]);
    const [tripOptions, setTripOptions] = useState([]);
    const [salesDocuments, setSalesDocuments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [salesDocumentsLoaded, setSalesDocumentsLoaded] = useState(false);
    const [backgroundTrips, setBackgroundTrips] = useState([]);
    const [isVehicleDisabled, setIsVehicleDisabled] = useState(false);
    const [vehicleValidationMessage, setVehicleValidationMessage] = useState('');

    // Static dropdown options (memoized for performance)
    const docOptions = useMemo(() => ['DOC001', 'DOC002', 'DOC003', 'DOC004'], []);
    const placeOptions = useMemo(() => ['Chennai', 'Bangalore', 'Hyderabad', 'Mumbai', 'Delhi', 'Kolkata'], []);

    // Filtered modal data for search functionality
    const filteredModalData = useMemo(() => {
        if (!searchTerm || !modalData.length) return modalData;
        
        return modalData.filter(item => {
            let searchValue = '';
            if (typeof item === 'string') {
                searchValue = item.toLowerCase();
            } else if (item.TransTypeName) {
                searchValue = item.TransTypeName.toLowerCase();
            } else if (item.branchName) {
                searchValue = item.branchName.toLowerCase();
            } else if (item.tripID) {
                searchValue = item.tripID.toLowerCase();
            } else if (item.DocID) {
                searchValue = item.DocID.toLowerCase();
            } else if (item.displayText) {
                searchValue = item.displayText.toLowerCase();
            }
            return searchValue.includes(searchTerm.toLowerCase());
        });
    }, [searchTerm, modalData]);

    // Memoized calculations
    const totalWeight = useMemo(() => {
        const total = items.reduce((total, item) => total + (parseFloat(item.weight) || 0), 0).toFixed(2);
        console.log('⚖️ Calculated total weight:', total);
        return total;
    }, [items]);

    // Date formatting helpers for modal
    const formatDateForAPI = (date) => {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        return `${year}${month}${day}`;
    };

    const formatDateForDisplay = (date) => {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('en-GB'); // DD/MM/YYYY format
    };

    // Validation functions
    const validateWeight = (value) => {
        const numericRegex = /^\d*\.?\d*$/;
        if (value === '') {
            setWeightError('');
            return true;
        }
        if (!numericRegex.test(value)) {
            setWeightError('Only numeric and decimal values are allowed');
            return false;
        }
        setWeightError('');
        return true;
    };

    // Change handler with validation
    const handleWeightChange = (value) => {
        if (validateWeight(value)) {
            setWeight(value);
        }
    };

    // Initialize dates for modals that need date pickers - Fixed timezone issue
    const initializeDatesForModal = (modalType) => {
        if (!businessDate) return;

        try {
            // Parse business date (DD-MM-YYYY format) properly
            const [day, month, year] = businessDate.split('-');
            // Use UTC Date to avoid timezone issues - create date at noon UTC
            const businessDateObj = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0));
            
            console.log('🔍 Date initialization debug - Fixed UTC:', {
                originalBusinessDate: businessDate,
                parsedParts: { day, month, year },
                businessDateUTC: businessDateObj.toISOString(),
                formattedDate: businessDateObj.toISOString().split('T')[0]
            });

            if (modalType === 'tripId') {
                // For tripId: both dates = business date
                const dateFormatted = businessDateObj.toISOString().split('T')[0];
                setModalFromDate(dateFormatted);
                setModalToDate(dateFormatted);
                console.log('✅ Auto-populated dates for tripId modal (UTC Fixed):', {
                    businessDate,
                    fromDate: dateFormatted,
                    toDate: dateFormatted
                });
            } else if (modalType === 'selectDoc') {
                // For selectDoc: To Date = business date, From Date = business date - 1
                const toDateFormatted = businessDateObj.toISOString().split('T')[0];
                setModalToDate(toDateFormatted);
                
                const fromDateObj = new Date(businessDateObj);
                fromDateObj.setUTCDate(fromDateObj.getUTCDate() - 1); // Use UTC methods
                const fromDateFormatted = fromDateObj.toISOString().split('T')[0];
                setModalFromDate(fromDateFormatted);
                
                console.log('✅ Auto-populated dates for selectDoc modal (UTC Fixed):', {
                    businessDate,
                    fromDate: fromDateFormatted,
                    toDate: toDateFormatted
                });
            }
        } catch (error) {
            console.error('❌ Error initializing dates:', error);
            // Fallback to current date
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            
            setModalFromDate(yesterdayStr);
            setModalToDate(todayStr);
        }
    };

    // Auto-fetch Trip ID when Trip Branch is selected
    useEffect(() => {
        if (selectedTripBranchId && selectedTripBranch) {
            console.log('🔄 Auto-fetching trip list for branch:', selectedTripBranchId);
            fetchTripList(selectedTripBranchId);
        }
    }, [selectedTripBranchId]);

    // Enhanced Fetch Sales Documents from API with date range support
    const fetchSalesDocuments = useCallback(async (branchId, docTypeId = null, fromDate = null, toDate = null) => {
        console.log('🔄 Starting enhanced fetchSalesDocuments with params:', { branchId, docTypeId, fromDate, toDate });
        setModalLoading(true);
        setSalesDocumentsLoaded(false);

        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token for sales documents:', token ? 'Token found' : 'No token found');

            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }

            if (!branchId) {
                Alert.alert('Error', 'Please select a branch first');
                return;
            }

            // Get date range - use provided dates or default to business date range
            let fromDateStr, toDateStr;
            
            if (fromDate && toDate) {
                // Use provided dates (already in YYYYMMDD format from modal)
                fromDateStr = fromDate;
                toDateStr = toDate;
                console.log('📅 Using provided date range:', { fromDateStr, toDateStr });
            } else if (businessDate) {
                // Use business date range - same as modal logic
                const [day, month, year] = businessDate.split('-');
                const businessDateObj = new Date(year, month - 1, day);
                
                // From date: business date - 1 day (same as modal)
                const fromDateObj = new Date(businessDateObj);
                fromDateObj.setDate(fromDateObj.getDate() - 1);
                
                // To date: business date
                const toDateObj = new Date(businessDateObj);
                
                const formatDate = (date) => {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    return `${year}${month}${day}`;
                };
                
                fromDateStr = formatDate(fromDateObj);
                toDateStr = formatDate(toDateObj);
                console.log('📅 Using business date range (1 day diff):', { fromDateStr, toDateStr, businessDate });
            } else {
                // Fallback to current date range (1 day difference)
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                
                const formatDate = (date) => {
                    return date.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
                };
                
                fromDateStr = formatDate(yesterday);
                toDateStr = formatDate(today);
                console.log('📅 Using fallback date range (1 day diff):', { fromDateStr, toDateStr });
            }

            // Build API URL with optional docType filter
            let apiUrl = `https://retailuat.abisibg.com/api/v1/fetchsale?BranchId=${branchId}&stage=PL&FromDate=${fromDateStr}&ToDate=${toDateStr}`;

            // Add docType filter if provided
            if (docTypeId) {
                apiUrl += `&TransTypeId=${docTypeId}`;
            }

            console.log('🌐 Making Enhanced Sales API call to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            console.log('📡 Sales API Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ Sales API Error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ Sales API Response data received, count:', data.length);

            if (Array.isArray(data)) {
                setSalesDocuments(data);
                setSalesDocumentsLoaded(true);
                console.log('💾 Sales documents saved to state:', data.length, 'documents');

                // If modal is open for selectDoc, update modal data immediately
                if (modalVisible && modalType === 'selectDoc') {
                    const salesData = data.map(doc => ({
                        ...doc,
                        displayText: doc.DocID
                    }));
                    setModalData(salesData);
                    console.log('🔄 Updated modal data immediately with sales data:', salesData.length);
                }
            } else {
                console.log('⚠️ Unexpected sales data format:', typeof data);
                Alert.alert('Error', 'Unexpected response format from sales API');
            }

        } catch (error) {
            console.log('💥 Error in fetchSalesDocuments:', error.message);
            Alert.alert('Error', `Failed to fetch sales documents: ${error.message}`);
        } finally {
            setModalLoading(false);
        }
    }, [modalVisible, modalType, businessDate]);

    // Fetch Trip List from API
    const fetchTripList = useCallback(async (branchId) => {
        console.log('🔄 Starting fetchTripList for branchId:', branchId);
        setModalLoading(true);

        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token for trip list:', token ? 'Token found' : 'No token found');

            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }

            if (!branchId) {
                Alert.alert('Error', 'Please select a branch first');
                return;
            }

            if (!businessDate) {
                console.log('⚠️ Business date not available, skipping trip list fetch');
                return;
            }

            // Extract day, month, year from businessDate
            const [day, month, year] = businessDate.split('-');
            const businessDateStr = `${year}${month}${day}`; // Format: YYYYMMDD

            const apiUrl = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${businessDateStr}/${businessDateStr}`;
            console.log('🌐 Making API call to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            console.log('📡 Trip API Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ Trip API Error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ Trip API Response data received, count:', data.length);

            if (Array.isArray(data)) {
                setTripOptions(data);
                console.log('💾 Trip options saved to state:', data.length, 'trips');

                // If modal is open for tripId, update modal data immediately
                if (modalVisible && modalType === 'tripId') {
                    const tripData = data.map(trip => ({
                        ...trip,
                        displayText: trip.tripID
                    }));
                    setModalData(tripData);
                    console.log('🔄 Updated modal data immediately with trip data:', tripData.length);
                }

                return data;
            } else {
                console.log('⚠️ Unexpected trip data format:', typeof data);
                Alert.alert('Error', 'Unexpected response format from trip API');
                return [];
            }

        } catch (error) {
            console.log('💥 Error in fetchTripList:', error.message);
            Alert.alert('Error', `Failed to fetch trip list: ${error.message}`);
            return [];
        } finally {
            setModalLoading(false);
            console.log('🏁 fetchTripList completed');
        }
    }, [modalVisible, modalType, businessDate]);

    // Fetch Background Trips for vehicle validation
    const fetchBackgroundTrips = useCallback(async (branchId) => {
        console.log('🔄 Starting fetchBackgroundTrips for branchId:', branchId);
        
        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token for background trips:', token ? 'Token found' : 'No token found');

            if (!token) {
                console.log('⚠️ No token found for background trips fetch');
                return;
            }

            if (!branchId || !businessDate) {
                console.log('⚠️ Missing branchId or businessDate for background trips fetch');
                return;
            }

            // Extract day, month, year from businessDate and format dates
            const [day, month, year] = businessDate.split('-');
            const businessDateStr = `${year}${month}${day}`; // Format: YYYYMMDD
            
            // Create date for tomorrow (businessDate + 1)
            const businessDateObj = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day)));
            const tomorrowObj = new Date(businessDateObj);
            tomorrowObj.setUTCDate(tomorrowObj.getUTCDate() + 1);
            
            const tomorrowYear = tomorrowObj.getUTCFullYear();
            const tomorrowMonth = (tomorrowObj.getUTCMonth() + 1).toString().padStart(2, '0');
            const tomorrowDay = tomorrowObj.getUTCDate().toString().padStart(2, '0');
            const tomorrowDateStr = `${tomorrowYear}${tomorrowMonth}${tomorrowDay}`;

            const apiUrl = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${businessDateStr}/${tomorrowDateStr}`;
            console.log('🌐 Making background trips API call to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            console.log('📡 Background trips API Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ Background trips API Error response:', errorText);
                return;
            }

            const data = await response.json();
            console.log('✅ Background trips API Response data received, count:', data.length);

            if (Array.isArray(data)) {
                setBackgroundTrips(data);
                console.log('💾 Background trips saved to state:', data.length, 'trips');
                
                // Log vehicle numbers for debugging
                const vehicleNumbers = data.map(trip => trip.vehicleNo).filter(Boolean);
                console.log('🚗 Vehicle numbers from background trips:', vehicleNumbers);
                
                return data;
            } else {
                console.log('⚠️ Unexpected background trips data format:', typeof data);
                return [];
            }

        } catch (error) {
            console.log('💥 Error in fetchBackgroundTrips:', error.message);
            return [];
        }
    }, [businessDate]);

    // Vehicle validation function
    const validateVehicleNumber = useCallback((vehicleNumber) => {
        if (!vehicleNumber || !backgroundTrips.length) {
            setIsVehicleDisabled(false);
            setVehicleValidationMessage('');
            return;
        }

        const existingTrip = backgroundTrips.find(trip => 
            trip.vehicleNo && trip.vehicleNo.toLowerCase() === vehicleNumber.toLowerCase()
        );

        if (existingTrip) {
            setIsVehicleDisabled(true);
            setVehicleValidationMessage(`Vehicle ${vehicleNumber} is already in use for trip ${existingTrip.tripID}`);
            console.log('❌ Vehicle validation failed:', {
                vehicleNumber,
                existingTrip: existingTrip.tripID,
                branchId: existingTrip.branchId
            });
        } else {
            setIsVehicleDisabled(false);
            setVehicleValidationMessage('');
            console.log('✅ Vehicle validation passed:', vehicleNumber);
        }
    }, [backgroundTrips]);

    // Fetch Branch List from API
    const fetchBranchList = useCallback(async () => {
        console.log('🔄 Starting fetchBranchList...');
        setLoading(true);

        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token for branch list:', token ? 'Token found' : 'No token found');

            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }

            console.log('🌐 Making API call to fetch branch list...');
            const response = await fetch(`${local_url}/api/Company/GetBranchList/ALL/SEGMENT/ALL`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            console.log('📡 Branch API Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ Branch API Error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ Branch API Response data received, count:', data.length);

            if (Array.isArray(data)) {
                setBranchOptions(data);
                console.log('💾 Branch options saved to state:', data.length, 'branches');
            } else {
                console.log('⚠️ Unexpected branch data format:', typeof data);
                Alert.alert('Error', 'Unexpected response format from branch API');
            }

        } catch (error) {
            console.log('💥 Error in fetchBranchList:', error.message);
            Alert.alert('Error', `Failed to fetch branch list: ${error.message}`);
        } finally {
            setLoading(false);
        }
    }, []);

    // Fetch Doc Types from API
    const fetchDocTypes = useCallback(async () => {
        console.log('🔄 Starting fetchDocTypes...');
        setLoading(true);

        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token:', token ? 'Token found' : 'No token found');

            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }

            console.log('🌐 Making API call to fetch doc types...');
            const response = await fetch(`${local_signage}/api/v1/doctype`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            console.log('📡 API Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ API Error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ API Response data:', JSON.stringify(data, null, 2));

            if (Array.isArray(data)) {
                setDocTypeOptions(data);
                console.log('💾 Doc types saved to state:', data.map(item => item.TransTypeName));
            } else {
                console.log('⚠️ Unexpected data format:', typeof data);
                Alert.alert('Error', 'Unexpected response format from server');
            }

        } catch (error) {
            console.log('💥 Error in fetchDocTypes:', error.message);
            Alert.alert('Error', `Failed to fetch document types: ${error.message}`);
        } finally {
            setLoading(false);
        }
    }, []);

    // Handle select all checkbox for items
    useEffect(() => {
        if (isSelectAllItems) {
            const allIndices = items.map((_, index) => index);
            setSelectedRows(allIndices);
            console.log('✅ Selected all items:', allIndices.length);
        } else {
            setSelectedRows([]);
            console.log('❌ Deselected all items');
        }
    }, [isSelectAllItems, items]);

    // Load initial data
    useEffect(() => {
        const loadInitialData = async () => {
            console.log('🚀 Loading initial data...');
            await fetchDocTypes();
            await fetchBranchList();
        };

        loadInitialData();
    }, [fetchDocTypes, fetchBranchList]);

    // Fetch business date on mount and background trips
    useEffect(() => {
        const loadBusinessDate = async () => {
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const branch = selectedBranch ? JSON.parse(selectedBranch) : {};
            const branchId = branch.BranchId || branch.id || branch.branchId || 'L102';
            const date = await fetchBusinessDay(token, branchId);
            setBusinessDate(date); // date format: DD-MM-YYYY
            
            // Fetch background trips for vehicle validation after business date is set
            if (date) {
                console.log('🔄 Fetching background trips for vehicle validation...');
                fetchBackgroundTrips(branchId);
            }
        };
        loadBusinessDate();
    }, [fetchBackgroundTrips]);

    // Validate vehicle number when it changes
    useEffect(() => {
        validateVehicleNumber(vehicleNo);
    }, [vehicleNo, validateVehicleNumber]);

    /**
     * Handles actions from the ScrollOptionsBar
     */
 
const handleOptionPress = useCallback((option) => {
    console.log('🎯 ScrollOptionsBar option pressed:', option);
    setSelectedScrollOption(option);

    switch (option) {
        case 'New':
            console.log('🆕 New option selected - resetting form');
            resetForm();
            break;
        case 'Save':
            console.log('💾 Save option selected');
            
            // Get fresh state values at save time
            console.log('🔍 Fresh state check before save:');
            console.log('selectedTripBranch:', selectedTripBranch);
            console.log('selectedTripBranchId:', selectedTripBranchId);
            console.log('tripId:', tripId);
            console.log('items.length:', items.length);
            
            // Validate with fresh values
            if (!selectedTripBranch || selectedTripBranch.trim() === '') {
                console.log('❌ Branch name validation failed');
                Alert.alert('Validation Error', 'Please select trip branch');
                return;
            }
            
            if (!selectedTripBranchId || selectedTripBranchId.toString().trim() === '') {
                console.log('❌ Branch ID validation failed');
                Alert.alert('Validation Error', 'Please select trip branch (ID missing)');
                return;
            }
            
            if (!tripId || tripId.toString().trim() === '') {
                console.log('❌ Trip ID validation failed');
                Alert.alert('Validation Error', 'Please select trip ID');
                return;
            }
            
            if (!items || items.length === 0) {
                console.log('❌ Items validation failed');
                Alert.alert('Validation Error', 'Please add at least one item to the trip');
                return;
            }
            
            console.log('✅ All validations passed, calling saveTrip');
            
            // Call saveTrip after validation passes
            saveTripWithCurrentValues();
            break;
        case 'View':
            console.log('👁️ View option selected');
            break;
        case 'Cancel':
            console.log('❌ Cancel option selected - resetting form');
            resetForm();
            break;
        default:
            console.log('❓ Unknown option selected:', option);
            break;
    }
}, [selectedTripBranch, selectedTripBranchId, tripId, items, resetForm]); // Added all dependencies

// Add this new function to handle save with current values
const saveTripWithCurrentValues = useCallback(async () => {
    console.log('💾 Starting save with current values...');
    
    // Capture current state values
    const currentBranch = selectedTripBranch;
    const currentBranchId = selectedTripBranchId;
    const currentTripId = tripId;
    const currentItems = [...items]; // Create a copy
    const currentTripRemarks = tripRemarks;
    
    console.log('📊 Captured current state for save:', {
        currentBranch,
        currentBranchId,
        currentTripId,
        currentItemsCount: currentItems.length,
        currentTripRemarks
    });
    
    setLoading(true);

    try {
        const token = await AsyncStorage.getItem('authToken');
        console.log('🔑 Retrieved token for save:', token ? 'Token found' : 'No token found');

        if (!token) {
            Alert.alert('Error', 'Authentication token not found. Please login again.');
            return;
        }

        // Get current user ID
        const userData = await AsyncStorage.getItem('userData');
        const user = userData ? JSON.parse(userData) : null;
        const userId = user?.userId || user?.id || user?.empId || 'SYSTEM';
        const currentDate = new Date().toISOString();

        // Ensure tripId is a string for API
        const cleanTripId = currentTripId.toString().trim();
        console.log('🔧 Using cleaned tripId for API:', cleanTripId);

        // Map form data to API structure with captured values
        const tripPlanData = {
            tripID: cleanTripId,
            branchId: currentBranchId, // Use captured value
            tripTypeCode: "DELIVERY",
            remarks: currentTripRemarks || "",
            isO_Number: "",
            originBranchId: currentBranchId, // Use captured value
            posted: false,
            deleted: "N",
            createdUserId: userId,
            createdDate: currentDate,
            modifiedUserId: userId,
            modifiedDate: currentDate,
            deletedUserId: "",
            deletedDate: "1753-01-01T00:00:00.000Z",
            tripManifestDtls: currentItems.map((item, index) => { // Use captured items
                const manifestItem = {
                    lineNumber: (index + 1).toString(),
                    transTypeID: item.transTypeID || item.salesDocDetails?.TransTypeID || "SALE",
                    docName: item.docName || item.salesDocDetails?.DocName || item.chooseDoc,
                    customerName: item.customer,
                    place: item.place,
                    remarks: item.remarks || "",
                    kgweight: parseFloat(item.weight) || 0,
                    kmDistance: 0,
                    amountToCollect: item.amountToCollect || item.salesDocDetails?.TotalAmount || 0,
                    customerID: item.customerID || item.salesDocDetails?.CustomerID || item.salesDocDetails?.POSCustomerID || "",
                    placeID: "",
                    addressID: item.salesDocDetails?.DeliveryAddressID || "",
                    transID: item.salesDocDetails?.SaleId || item.chooseDoc,
                    timeSlotId: item.salesDocDetails?.TimeSlotId || "",
                    tripId: cleanTripId,
                    paymentGateWayId: "",
                    paymentMethodId: "",
                    paymentGatewayName: "",
                    deleted: "N",
                    createdUserId: userId,
                    createdDate: currentDate,
                    modifiedUserId: userId,
                    modifiedDate: currentDate,
                    deletedUserId: "",
                    deletedDate: "1753-01-01T00:00:00.000Z",
                    dml: "I"
                };
                console.log(`📤 Prepared manifest item ${index + 1}:`, manifestItem);
                return manifestItem;
            })
        };

        console.log('📤 Sending trip plan data to API:', JSON.stringify(tripPlanData, null, 2));

        const apiUrl = `${local_url}/api/TripPlan`;
        console.log('🌐 Making POST request to:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(tripPlanData)
        });

        console.log('📡 Save API Response status:', response.status);
        console.log('📡 Save API Response status text:', response.statusText);
        console.log('📡 Save API Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.log('❌ Save API Error response:', errorText);
            console.log('❌ Save API Error status:', response.status);
            console.log('❌ Save API Error status text:', response.statusText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('✅ Trip plan saved successfully!');
        console.log('📥 API Response data:', responseData);
        console.log('📥 Response data type:', typeof responseData);
        console.log('📥 Response data length:', Array.isArray(responseData) ? responseData.length : 'Not an array');
        
        if (Array.isArray(responseData)) {
            console.log('📥 Response is an array with', responseData.length, 'items');
            responseData.forEach((item, index) => {
                console.log(`📥 Item ${index + 1}:`, item);
            });
        } else if (typeof responseData === 'object') {
            console.log('📥 Response is an object with keys:', Object.keys(responseData));
            Object.entries(responseData).forEach(([key, value]) => {
                console.log(`📥 ${key}:`, value);
            });
        }

        if (responseData.result === 1 && responseData.description) {
            Alert.alert(
                'Success',
                `Trip Delivery ID created: ${responseData.description}\n\nDo you want to print?`,
                [
                    {
                        text: 'No',
                        onPress: () => {
                            resetForm(); // Clear the form
                        },
                        style: 'cancel'
                    },
                    {
                        text: 'Yes',
                        onPress: () => {
                            // Simulate print
                            console.log('Print requested for Trip Delivery ID:', responseData.description);
                            resetForm(); // Clear the form after print
                        }
                    }
                ],
                { cancelable: false }
            );
        } else {
            // fallback
            Alert.alert('Success', 'Trip delivery saved successfully!');
            resetForm();
        }

    } catch (error) {
        console.log('💥 Error saving trip plan:', error.message);
        Alert.alert('Error', `Failed to save trip plan: ${error.message}`);
    } finally {
        setLoading(false);
    }
}, [selectedTripBranch, selectedTripBranchId, tripId, items, tripRemarks, resetForm]);


    /**
     * Resets all form fields to their initial state
     */
    const resetForm = useCallback(() => {
        console.log('🔄 Resetting form to initial state...');
        console.log('📊 Items before reset:', items.length);

        setTripId('');
        setTripDetails('');
        setTripRemarks('');
        setSelectedTripBranch('');
        setSelectedTripBranchId('');
        setDocType('');
        setSelectedDocTypeId('');
        setSelectedDoc('');
        setCustomerBranch('');
        setPlace('');
        setRemarks('');
        setWeight('');
        setItems([]); // Explicitly set to empty array
        setSelectedRows([]);
        setIsSelectAllItems(false);
        setSalesDocuments([]);
        setSalesDocumentsLoaded(false);
        setTripOptions([]);

        console.log('✅ Form reset completed');
        console.log('📊 Items after reset should be 0');
    }, [items.length]);

    // Enhanced handleModalItemSelect with auto-fetch functionality
    const handleModalItemSelect = useCallback((item) => {
        console.log('🎯 Modal item selected:', item, 'for type:', modalType);

        switch (modalType) {
            case 'tripBranch':
                if (typeof item === 'object' && (item.BranchName || item.branchName) && (item.BranchId || item.branchID)) {
                    setSelectedTripBranch(item.BranchName || item.branchName);
                    setSelectedTripBranchId(item.BranchId || item.branchID);
                    // Clear dependent fields when branch changes
                    setTripId('');
                    setTripOptions([]);
                    setSalesDocuments([]);
                    setSalesDocumentsLoaded(false);
                    setDocType('');
                    setSelectedDocTypeId('');
                    setSelectedDoc('');
                    console.log('🏢 Trip branch updated:', item.BranchName || item.branchName, 'ID:', item.BranchId || item.branchID);
                }
                setModalVisible(false);
                break;
            case 'tripId':
                if (typeof item === 'object' && item.tripID) {
                    // Ensure tripID is stored as a string and trimmed
                    const cleanTripId = item.tripID.toString().trim();
                    setTripId(cleanTripId);
                    
                    // Auto-populate Trip Details with branchId and remarks
                    const tripDetails = `${item.branchId || ''} - ${item.remarks || ''}`;
                    setTripDetails(tripDetails);
                    
                    console.log('🚗 Trip ID updated:', cleanTripId);
                    console.log('🚗 Trip Details auto-populated:', tripDetails);
                    console.log('🚗 Selected trip data:', {
                        tripID: item.tripID,
                        branchId: item.branchId,
                        remarks: item.remarks
                    });
                }
                setModalVisible(false);
                break;
            case 'docType':
                if (typeof item === 'object' && item.TransTypeName && item.TransTypeId) {
                    setDocType(item.TransTypeName);
                    setSelectedDocTypeId(item.TransTypeId);
                    setSelectedDoc(''); // Clear selected document when doc type changes

                    console.log('📋 Doc type updated:', item.TransTypeName, 'ID:', item.TransTypeId);

                    // Automatically fetch sales documents when doc type is selected
                    if (selectedTripBranchId) {
                        console.log('🔄 Auto-fetching sales documents for doc type:', item.TransTypeId);
                        fetchSalesDocuments(selectedTripBranchId, item.TransTypeId);
                    } else {
                        console.log('⚠️ Cannot fetch sales documents: No branch selected');
                        Alert.alert('Error', 'Please select a branch first');
                    }
                } else {
                    setDocType(item);
                    setSelectedDoc(''); // Clear selected document
                    console.log('📋 Doc type updated (legacy):', item);
                }
                setModalVisible(false);
                break;
            case 'selectDoc':
                if (typeof item === 'object' && item.DocID) {
                    setSelectedDoc(item.DocID);
                    // Auto-populate customer information from sales document
                    if (item.POSCustomerID) {
                        setCustomerBranch(item.POSCustomerID);
                    }
                    console.log('📄 Selected sales doc updated:', item.DocID);
                    console.log('👤 Auto-populated customer:', item.POSCustomerID);
                } else {
                    setSelectedDoc(item);
                    console.log('📄 Selected doc updated (legacy):', item);
                }
                setModalVisible(false);
                break;
            case 'place':
                setPlace(item);
                console.log('📍 Place updated:', item);
                setModalVisible(false);
                break;
            default:
                console.log('❓ Unknown modal type for selection:', modalType);
                setModalVisible(false);
                break;
        }
    }, [modalType, selectedTripBranchId, fetchSalesDocuments]);
    // Enhanced handleAddItem function that includes sales document details
    // Enhanced handleAddItem function with better state management
    const handleAddItem = useCallback(() => {
        console.log('➕ Attempting to add new item with enhanced details...');
        console.log('📊 Item data:', {
            docType,
            selectedDoc,
            customerBranch,
            place,
            remarks,
            weight
        });
        console.log('📊 Current items array length before adding:', items.length);

        // Validation - check required fields
        if (!docType) {
            console.log('❌ Validation failed: No document type selected');
            Alert.alert('Validation Error', 'Please select document type');
            return;
        }

        if (!selectedDoc) {
            console.log('❌ Validation failed: No document selected');
            Alert.alert('Validation Error', 'Please select a document');
            return;
        }

        if (!customerBranch) {
            console.log('❌ Validation failed: No customer/branch entered');
            Alert.alert('Validation Error', 'Please enter customer/branch');
            return;
        }

        // 'place' and 'weight' are now optional, so no validation for them

        // Find the selected sales document to get additional details
        const selectedSalesDoc = salesDocuments.find(doc => doc.DocID === selectedDoc);
        console.log('🔍 Found sales document details:', selectedSalesDoc);

        const newItem = {
            id: Date.now(), // Add unique ID for better tracking
            lineNumber: items.length + 1,
            docType: docType,
            chooseDoc: selectedDoc,
            customer: customerBranch,
            place: place || '',
            remarks: remarks || '',
            weight: weight || '0',
            // Additional fields from sales document for API submission
            salesDocDetails: selectedSalesDoc,
            transTypeID: selectedSalesDoc?.TransTypeID || selectedDocTypeId || "SALE",
            customerID: selectedSalesDoc?.CustomerID || selectedSalesDoc?.POSCustomerID || '',
            amountToCollect: selectedSalesDoc?.TotalAmount || 0,
            docName: selectedSalesDoc?.DocName || selectedDoc,
            branchId: selectedSalesDoc?.BranchId || selectedSalesDoc?.branchId || selectedTripBranchId
        };

        console.log('🆕 New item to be added:', newItem);

        // Use functional update to ensure state is updated correctly
        setItems(prevItems => {
            const updatedItems = [...prevItems, newItem];
            console.log('✅ Item added successfully. New items array:', updatedItems);
            console.log('📊 Total items after adding:', updatedItems.length);
            return updatedItems;
        });

        console.log('✅ Item added successfully with enhanced details:', newItem);

        // Reset fields for next entry
        setDocType('');
        setSelectedDocTypeId('');
        setSelectedDoc('');
        setCustomerBranch('');
        setPlace('');
        setRemarks('');
        setWeight('');
        setSalesDocumentsLoaded(false); // Reset to require doc type selection again
    }, [docType, selectedDoc, customerBranch, place, remarks, weight, salesDocuments, selectedDocTypeId, selectedTripBranchId, items]);

    // Enhanced saveTrip function
    const saveTrip = useCallback(async () => {
        // Get fresh state values at execution time
        console.log('💾 Starting saveTrip with fresh state check...');
        
        // Use a small delay to ensure we get the latest state
        await new Promise(resolve => setTimeout(resolve, 10));
        
        console.log('💾 Current form data at save time:', {
            selectedTripBranch,
            selectedTripBranchId,
            tripId,
            tripIdType: typeof tripId,
            tripIdLength: tripId ? tripId.length : 0,
            tripDetails,
            tripRemarks,
            docType,
            selectedDocTypeId,
            itemsCount: items.length,
            itemsArray: items
        });
    
        // Enhanced validation with fresh state checks
        if (!selectedTripBranch || selectedTripBranch.trim() === '') {
            console.log('❌ Validation failed: No trip branch selected');
            console.log('selectedTripBranch value:', `"${selectedTripBranch}"`);
            Alert.alert('Validation Error', 'Please select trip branch');
            return;
        }
    
        if (!selectedTripBranchId || selectedTripBranchId.toString().trim() === '') {
            console.log('❌ Validation failed: No trip branch ID selected');
            console.log('selectedTripBranchId value:', `"${selectedTripBranchId}"`);
            Alert.alert('Validation Error', 'Please select trip branch (ID missing)');
            return;
        }
    
        // Fixed validation for tripId - check for empty string and trim whitespace
        if (!tripId || tripId.toString().trim() === '') {
            console.log('❌ Validation failed: No trip ID selected');
            console.log('tripId value:', tripId);
            console.log('tripId type:', typeof tripId);
            console.log('tripId after trim:', tripId ? tripId.toString().trim() : 'null/undefined');
            Alert.alert('Validation Error', 'Please select trip ID');
            return;
        }
    
        // Enhanced items validation with detailed logging
        console.log('🔍 Validating items array...');
        console.log('Items array length:', items.length);
        console.log('Items array:', items);
    
        if (!Array.isArray(items)) {
            console.log('❌ Validation failed: Items is not an array');
            console.log('Items type:', typeof items);
            console.log('Items value:', items);
            Alert.alert('Validation Error', 'Items data is corrupted. Please refresh and try again.');
            return;
        }
    
        if (items.length === 0) {
            console.log('❌ Validation failed: No items added');
            console.log('Items array is empty:', items);
            Alert.alert('Validation Error', 'Please add at least one item to the trip');
            return;
        }
    
        // Validate each item has required fields with detailed logging
        console.log('🔍 Validating individual items...');
        const invalidItems = items.filter((item, index) => {
            // Only docType, chooseDoc, customer are required
            const isValid = item.docType && item.chooseDoc && item.customer;
            console.log(`Item ${index + 1} validation:`, {
                docType: !!item.docType,
                chooseDoc: !!item.chooseDoc,
                customer: !!item.customer,
                isValid: isValid
            });
            return !isValid;
        });
    
        if (invalidItems.length > 0) {
            console.log('❌ Validation failed: Some items missing required fields');
            console.log('Invalid items count:', invalidItems.length);
            console.log('Invalid items:', invalidItems);
            Alert.alert('Validation Error', 'All items must have Document Type, Document, and Customer');
            return;
        }
    
        console.log('✅ All validations passed, proceeding with save...');
        console.log(`✅ Found ${items.length} valid items to save`);
        setLoading(true);
    
        try {
            const token = await AsyncStorage.getItem('authToken');
            console.log('🔑 Retrieved token for save:', token ? 'Token found' : 'No token found');
    
            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }
    
            // Get current user ID
            const userData = await AsyncStorage.getItem('userData');
            const user = userData ? JSON.parse(userData) : null;
            const userId = user?.userId || user?.id || user?.empId || 'SYSTEM';
            const currentDate = new Date().toISOString();
    
            // Ensure tripId is a string for API
            const cleanTripId = tripId.toString().trim();
            console.log('🔧 Using cleaned tripId for API:', cleanTripId);
    
            // Map form data to API structure with correct field mappings
            const tripPlanData = {
                tripID: cleanTripId,
                branchId: selectedTripBranchId,
                tripTypeCode: "DELIVERY",
                remarks: tripRemarks || "",
                isO_Number: "",
                originBranchId: selectedTripBranchId,
                posted: false,
                deleted: "N",
                createdUserId: userId,
                createdDate: currentDate,
                modifiedUserId: userId,
                modifiedDate: currentDate,
                deletedUserId: "",
                deletedDate: "1753-01-01T00:00:00.000Z",
                tripManifestDtls: items.map((item, index) => {
                    const manifestItem = {
                        lineNumber: (index + 1).toString(),
                        transTypeID: item.transTypeID || item.salesDocDetails?.TransTypeID || "SALE",
                        docName: item.docName || item.salesDocDetails?.DocName || item.chooseDoc,
                        customerName: item.customer,
                        place: item.place || '',
                        remarks: item.remarks || "",
                        kgweight: parseFloat(item.weight) || 0,
                        kmDistance: 0,
                        amountToCollect: item.amountToCollect || item.salesDocDetails?.TotalAmount || 0,
                        customerID: item.customerID || item.salesDocDetails?.CustomerID || item.salesDocDetails?.POSCustomerID || "",
                        placeID: "",
                        addressID: item.salesDocDetails?.DeliveryAddressID || "",
                        transID: item.salesDocDetails?.SaleId || item.chooseDoc,
                        timeSlotId: item.salesDocDetails?.TimeSlotId || "",
                        tripId: cleanTripId,
                        paymentGateWayId: "",
                        paymentMethodId: "",
                        paymentGatewayName: "",
                        deleted: "N",
                        createdUserId: userId,
                        createdDate: currentDate,
                        modifiedUserId: userId,
                        modifiedDate: currentDate,
                        deletedUserId: "",
                        deletedDate: "1753-01-01T00:00:00.000Z",
                        dml: "I"
                    };
                    console.log(`📤 Prepared manifest item ${index + 1}:`, manifestItem);
                    return manifestItem;
                })
            };
    
                        console.log('📤 Sending trip plan data to API:', JSON.stringify(tripPlanData, null, 2));

            const apiUrl = `${local_url}/api/TripPlan`;
            console.log('🌐 Making POST request to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(tripPlanData)
            });

            console.log('📡 Save API Response status:', response.status);
            console.log('📡 Save API Response status text:', response.statusText);
            console.log('📡 Save API Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.log('❌ Save API Error response:', errorText);
                console.log('❌ Save API Error status:', response.status);
                console.log('❌ Save API Error status text:', response.statusText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const responseData = await response.json();
            console.log('✅ Trip plan saved successfully!');
            console.log('📥 API Response data:', responseData);
            console.log('📥 Response data type:', typeof responseData);
            console.log('📥 Response data length:', Array.isArray(responseData) ? responseData.length : 'Not an array');
            
            if (Array.isArray(responseData)) {
                console.log('📥 Response is an array with', responseData.length, 'items');
                responseData.forEach((item, index) => {
                    console.log(`📥 Item ${index + 1}:`, item);
                });
            } else if (typeof responseData === 'object') {
                console.log('📥 Response is an object with keys:', Object.keys(responseData));
                Object.entries(responseData).forEach(([key, value]) => {
                    console.log(`📥 ${key}:`, value);
                });
            }
    
            if (responseData.result === 1 && responseData.description) {
                Alert.alert(
                    'Success',
                    `Trip Delivery ID created: ${responseData.description}\n\nDo you want to print?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                resetForm(); // Clear the form
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                // Simulate print
                                console.log('Print requested for Trip Delivery ID:', responseData.description);
                                resetForm(); // Clear the form after print
                            }
                        }
                    ],
                    { cancelable: false }
                );
            } else {
                // fallback
                Alert.alert('Success', 'Trip delivery saved successfully!');
                resetForm();
            }
    
        } catch (error) {
            console.log('💥 Error saving trip plan:', error.message);
            Alert.alert('Error', `Failed to save trip plan: ${error.message}`);
        } finally {
            setLoading(false);
        }
    }, [selectedTripBranch, selectedTripBranchId, tripId, tripDetails, tripRemarks, items, resetForm]);
    
    /**
     * Toggles selection of a table row
     */
    const handleRowSelection = useCallback((index) => {
        console.log('🎯 Row selection toggled for index:', index);

        setSelectedRows(prevSelected => {
            const newSelection = prevSelected.includes(index)
                ? prevSelected.filter(i => i !== index)
                : [...prevSelected, index];

            // Update select all checkbox state
            setIsSelectAllItems(newSelection.length === items.length);

            console.log(prevSelected.includes(index) ? '❌ Deselected row:' : '✅ Selected row:', index);
            console.log('Total selected:', newSelection.length);

            return newSelection;
        });
    }, [items.length]);

    /**
     * Removes selected rows from the items table
     */
    const handleDeleteSelectedRows = useCallback(() => {
        console.log('🗑️ Attempting to delete selected rows...');
        console.log('📊 Selected rows for deletion:', selectedRows);

        if (selectedRows.length === 0) {
            console.log('⚠️ No rows selected for deletion');
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newItems = items.filter((_, index) => !selectedRows.includes(index));
        setItems(newItems);
        setSelectedRows([]);
        setIsSelectAllItems(false);

        console.log('✅ Deleted rows successfully. Remaining items:', newItems.length);
    }, [selectedRows, items]);

    /**
     * Clears the document and item input fields
     */
    const handleClear = useCallback(() => {
        console.log('🧹 Clearing input fields...');
        setDocType('');
        setSelectedDocTypeId('');
        setSelectedDoc('');
        setCustomerBranch('');
        setPlace('');
        setRemarks('');
        setWeight('');
        setSalesDocumentsLoaded(false);
        console.log('✅ Input fields cleared');
    }, []);

    /**
     * Opens selection modal with appropriate data and sets data immediately
     */
    const openModal = useCallback((type) => {
        console.log('🔍 Opening modal for type:', type);

        // Handle type-specific logic and prepare data
        switch (type) {
            case 'tripBranch':
                const branchData = branchOptions.map(branch => ({
                    ...branch,
                    displayText: branch.BranchName || branch.branchName
                }));
                setModalType(type);
                setModalData(branchData);
                setModalVisible(true);
                console.log('✅ Modal opened successfully with branch data count:', branchData.length);
                break;

            case 'tripId':
                if (!selectedTripBranchId) {
                    Alert.alert('Error', 'Please select a branch first');
                    return;
                }

                // Initialize dates for tripId modal
                initializeDatesForModal('tripId');

                // Set modal type and show modal immediately
                setModalType(type);
                setModalVisible(true);

                // Check if we already have trip data
                if (tripOptions.length > 0) {
                    const tripData = tripOptions.map(trip => ({
                        ...trip,
                        displayText: trip.tripID
                    }));
                    setModalData(tripData);
                    console.log('✅ Modal opened with existing trip data count:', tripData.length);
                } else {
                    // Set loading state and empty data initially
                    setModalLoading(true);
                    setModalData([]);
                    console.log('🔄 Modal opened, fetching trip data...');

                    // The fetchTripList will be called by useEffect when selectedTripBranchId changes
                    // We need to wait for it to complete
                }
                break;

            case 'docType':
                const docData = docTypeOptions.map(doc => ({
                    ...doc,
                    displayText: doc.TransTypeName
                }));
                setModalType(type);
                setModalData(docData);
                setModalVisible(true);
                console.log('✅ Modal opened successfully with doc type data count:', docData.length);
                break;

            case 'selectDoc':
                if (!salesDocumentsLoaded) {
                    Alert.alert('Error', 'Please select Document Type first to load sales documents');
                    return;
                }

                // Initialize dates for selectDoc modal
                initializeDatesForModal('selectDoc');

                const salesData = salesDocuments.map(doc => ({
                    ...doc,
                    displayText: doc.DocID
                }));
                setModalType(type);
                setModalData(salesData);
                setModalVisible(true);
                console.log('✅ Modal opened successfully with sales doc data count:', salesData.length);
                break;

            case 'place':
                setModalType(type);
                setModalData(placeOptions);
                setModalVisible(true);
                console.log('✅ Modal opened successfully with place data count:', placeOptions.length);
                break;

            default:
                setModalType(type);
                setModalData([]);
                setModalVisible(true);
                console.log('✅ Modal opened with empty data');
        }
    }, [selectedTripBranchId, salesDocumentsLoaded, branchOptions, tripOptions, docTypeOptions, salesDocuments, placeOptions]);

    useEffect(() => {
        // If modal is open and showing tripId, update the modal data when tripOptions changes
        if (modalVisible && modalType === 'tripId' && tripOptions.length > 0) {
            const tripData = tripOptions.map(trip => ({
                ...trip,
                displayText: trip.tripID
            }));
            setModalData(tripData);
            setModalLoading(false);
            console.log('🔄 Updated modal data with new trip options:', tripData.length);
        }
    }, [tripOptions, modalVisible, modalType]);


    useEffect(() => {
        if (selectedTripBranchId && selectedTripBranch) {
            console.log('🔄 Auto-fetching trip list for branch:', selectedTripBranchId);
            fetchTripList(selectedTripBranchId);
        }
    }, [selectedTripBranchId, fetchTripList]);

    // Enhanced handler for the Fetch button in the modal with branch selection
    const handleFetchTripApi = async ({ fromDate, toDate }) => {
        console.log('🔄 Starting enhanced trip fetch with date range:', { fromDate, toDate });
        setModalLoading(true);
        
        try {
            const token = await AsyncStorage.getItem('authToken');
            if (!token) {
                Alert.alert('Error', 'Authentication token not found. Please login again.');
                return;
            }

            // Different handling based on modal type
            if (modalType === 'selectDoc') {
                // For selectDoc modal, fetch sales documents with date range
                console.log('📄 Fetching sales documents for selectDoc modal with date range');
                
                if (!selectedTripBranchId) {
                    Alert.alert('Error', 'Please select a branch first');
                    return;
                }
                
                // Call enhanced fetchSalesDocuments with date range
                await fetchSalesDocuments(selectedTripBranchId, selectedDocTypeId, fromDate, toDate);
                
            } else {
                // For other modal types (like tripId), fetch trip data
                console.log('🚗 Fetching trip data for tripId modal with date range');
                
                // Use selected branch ID or default to L101
                const branchCode = selectedTripBranchId || 'L101';
                const apiUrl = `https://retailUAT.abisaio.com:9001/api/Trip/${branchCode}/${fromDate}/${toDate}`;
                console.log('🌐 Fetching trip data from:', apiUrl);
                console.log('🏢 Using branch code:', branchCode);
                console.log('📅 Date range:', { fromDate, toDate });

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                console.log('📡 Trip API Response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.log('❌ Trip API Error response:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ Trip API Response data received, count:', data.length);

                if (Array.isArray(data)) {
                    // Use trip data directly
                    const tripData = data.map(trip => ({ ...trip, displayText: trip.tripID }));
                    setModalData(tripData);
                    console.log('📊 Updated modal data with trip data:', tripData.length);
                } else {
                    console.log('⚠️ Unexpected trip data format:', typeof data);
                    setModalData([]);
                }
            }
        } catch (error) {
            console.error('❌ Error fetching data:', error);
            Alert.alert('Error', `Failed to fetch data: ${error.message}`);
            setModalData([]);
        } finally {
            setModalLoading(false);
        }
    };


    // Memoized form components for better performance
    const HeaderSection = useMemo(() => (
        <View>
            <View style={styles.formRow}>
                {/* Trip Branch Selection */}
                <View style={styles.inputWithButtonContainer}>
                    <TextInput
                        style={[styles.inputWithButton, styles.boldText]}
                        placeholder="Select Trip Branch"
                        placeholderTextColor="#888"
                        value={selectedTripBranch}
                        editable={false}
                    />
                    <TouchableOpacity
                        style={styles.selectButton}
                        onPress={() => openModal('tripBranch')}
                        accessibilityLabel="Select Trip Branch"
                        activeOpacity={0.7}
                    >
                        <Text style={styles.selectButtonText}>Select</Text>
                    </TouchableOpacity>
                </View>

                {/* Trip ID Input */}
                <View style={styles.inputWithButtonContainer}>
                    <TextInput
                        style={[styles.inputWithButton, styles.boldText]}
                        placeholder="Trip ID"
                        placeholderTextColor="#888"
                        value={tripId}
                        editable={false}
                    />
                    <TouchableOpacity
                        style={[
                            styles.selectButton,
                            !selectedTripBranchId && styles.disabledButton
                        ]}
                        onPress={() => openModal('tripId')}
                        accessibilityLabel="Select Trip ID"
                        disabled={!selectedTripBranchId}
                        activeOpacity={0.7}
                    >
                        <Text style={styles.selectButtonText}>
                            {modalLoading && modalType === 'tripId' ? 'Loading...' : 'Select'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Trip Details Row */}
            <View style={styles.formRow}>
                <TextInput
                    style={[styles.inputWithButton, styles.boldText, { marginRight: 10 }]}
                    placeholder="Trip Details"
                    placeholderTextColor="#888"
                    value={tripDetails}
                    onChangeText={setTripDetails}
                    multiline={true}
                    numberOfLines={1}
                    editable={false}
                />
            </View>

            {/* Trip Remarks Row */}
            <View style={styles.formRow}>
                <TextInput
                    style={[styles.remarksInput, styles.boldText]}
                    placeholder="Enter trip remarks..."
                    placeholderTextColor="#999"
                    value={tripRemarks}
                    onChangeText={setTripRemarks}
                    multiline={true}
                    numberOfLines={3}
                    textAlignVertical="top"
                />
            </View>
        </View>
    ), [selectedTripBranch, tripId, selectedTripBranchId, tripDetails, tripRemarks, modalLoading, modalType, openModal]);

    return (
        <KeyboardAvoidingView 
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
            {/* App Navigation Bar */}
            <Navbar />

            {/* Action Options Bar */}
            <ScrollOptionsBar
                title="Trip Plan"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            {/* Loading Overlay */}
            {loading && (
                <View style={styles.loadingOverlay}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.loadingText}>Processing...</Text>
                </View>
            )}

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    {/* ===== HEADER SECTION ===== */}
                    {HeaderSection}

                    {/* ===== TRIP DETAILS SECTION ===== */}
                    <View style={styles.sectionHeaderContainer}>
                        <Text style={styles.sectionTitle}>Trip Detail</Text>
                    </View>

                    {/* Document Type and Selection */}
                    <View style={styles.formRow}>
                        {/* Document Type Dropdown */}
                        <View style={styles.dropdownContainer}>
                            <TouchableOpacity
                                style={styles.dropdown}
                                onPress={() => openModal('docType')}
                                accessibilityLabel="Select Document Type"
                                disabled={loading}
                                activeOpacity={0.7}
                            >
                                <Text style={[
                                    styles.inputText,
                                    styles.boldText,
                                    { flex: 2, marginRight: 10 },
                                    !docType && styles.placeholderText
                                ]}>
                                    {loading ? 'Loading...' : (docType || 'DOC Type')}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>



                        {/* Document Selection */}
                        <View style={[styles.inputWithButtonContainer, styles.boldText, { flex: 2, marginRight: 10 }]}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Choose DOC"
                                placeholderTextColor="#888"
                                value={selectedDoc}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={[
                                    styles.selectButton,
                                    (!salesDocumentsLoaded || !selectedDocTypeId) && styles.disabledButton
                                ]}
                                onPress={() => openModal('selectDoc')}
                                accessibilityLabel="Select Document"
                                disabled={!salesDocumentsLoaded || !selectedDocTypeId}
                                activeOpacity={0.7}
                            >
                                <Text style={styles.selectButtonText}>
                                    {modalLoading && modalType === 'selectDoc' ? 'Loading...' : 'Select'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Customer and Place Information */}
                    <View style={styles.formRow}>
                        <TextInput
                            style={[styles.inputWithButton, styles.boldText, { marginRight: 10 }]}
                            placeholder="Customer/Branch"
                            placeholderTextColor="#888"
                            value={customerBranch}
                            onChangeText={setCustomerBranch}
                        />

                        <View style={[styles.inputWithButtonContainer, styles.boldText]}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Place"
                                placeholderTextColor="#888"
                                value={place}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('place')}
                                accessibilityLabel="Select Place"
                                activeOpacity={0.7}
                                disabled
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                    {/* <TouchableOpacity
                        style={{ backgroundColor: 'red', padding: 10, margin: 10 }}
                        onPress={() => {
                            Alert.alert('State Check',
                                `Branch: "${selectedTripBranch}"\nBranch ID: "${selectedTripBranchId}"`
                            );
                            console.log('-----', selectedTripBranch, '--s---', selectedTripBranchId);

                        }}
                    >
                        <Text style={{ color: 'white' }}>CHECK STATE</Text>
                    </TouchableOpacity> */}
                    {/* Remarks and Weight Inputs */}
                    <View style={styles.formRow}>
                        <TextInput
                            style={[styles.remarksRowInput, styles.boldText]}
                            placeholder="Add Remarks"
                            placeholderTextColor="#888"
                            value={remarks}
                            onChangeText={setRemarks}
                            multiline={false}
                        />

                        <View style={styles.weightInputContainer}>
                            <TextInput
                                style={[styles.weightInput, styles.boldText]}
                                placeholder="Wt(Kg)"
                                placeholderTextColor="#888"
                                value={weight}
                                onChangeText={handleWeightChange}
                                keyboardType="decimal-pad"
                            />
                            {weightError ? (
                                <Text style={styles.errorText}>{weightError}</Text>
                            ) : null}
                        </View>

                    {/* Add and Clear buttons */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={styles.addButton}
                            onPress={handleAddItem}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.addButtonText}>Add</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.clearButton}
                            onPress={handleClear}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Items Table with Updated Structure */}
                    <ScrollView horizontal={true} style={styles.tableScrollContainer}>
                        <View style={styles.tableContainer}>
                            {/* Table Header */}
                            <View style={styles.tableHeader}>
                                <View style={styles.checkboxCell}>
                                    <CheckBox
                                        value={isSelectAllItems}
                                        onValueChange={setIsSelectAllItems}
                                        tintColors={{ true: '#FDC500', false: '#FDC500' }}
                                        style={styles.headerCheckbox}
                                    />
                                </View>
                                <View style={styles.lineNumberCell}>
                                    <Text style={styles.tableHeaderText}>Line No</Text>
                                </View>
                                <View style={styles.docTypeCell}>
                                    <Text style={styles.tableHeaderText}>Doc Type</Text>
                                </View>
                                <View style={styles.chooseDocCell}>
                                    <Text style={styles.tableHeaderText}>Choose Doc</Text>
                                </View>
                                <View style={styles.customerCell}>
                                    <Text style={styles.tableHeaderText}>Customer</Text>
                                </View>
                                <View style={styles.placeCell}>
                                    <Text style={styles.tableHeaderText}>Place</Text>
                                </View>
                                <View style={styles.remarksCell}>
                                    <Text style={styles.tableHeaderText}>Remarks</Text>
                                </View>
                                <View style={styles.weightCell}>
                                    <Text style={styles.tableHeaderText}>Weight (Kg)</Text>
                                </View>
                            </View>

                            {/* Table Content */}
                            {items.length > 0 ? (
                                items.map((item, index) => (
                                    <View key={index} style={index % 2 === 0 ? styles.tableRow : styles.tableRowAlternate}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.lineNumberCell}>
                                            <Text style={styles.tableCell}>{item.lineNumber}</Text>
                                        </View>
                                        <View style={styles.docTypeCell}>
                                            <Text style={styles.tableCell}>{item.docType}</Text>
                                        </View>
                                        <View style={styles.chooseDocCell}>
                                            <Text style={styles.tableCell}>{item.chooseDoc}</Text>
                                        </View>
                                        <View style={styles.customerCell}>
                                            <Text style={styles.tableCell}>{item.customer}</Text>
                                        </View>
                                        <View style={styles.placeCell}>
                                            <Text style={styles.tableCell}>{item.place}</Text>
                                        </View>
                                        <View style={styles.remarksCell}>
                                            <Text style={styles.tableCell}>{item.remarks}</Text>
                                        </View>
                                        <View style={styles.weightCell}>
                                            <Text style={styles.tableCell}>{item.weight}</Text>
                                        </View>
                                    </View>
                                ))
                            ) : (
                                <View style={styles.emptyTableRow}>
                                    <Text style={styles.emptyTableText}>No Data</Text>
                                </View>
                            )}
                        </View>
                    </ScrollView>

                    {/* Action buttons at the bottom */}
                    <View style={styles.buttonRow}>
                        <TouchableOpacity
                            style={styles.deleteButton}
                            onPress={handleDeleteSelectedRows}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.deleteButtonText}>Delete Selected Row</Text>
                        </TouchableOpacity>

                        <View style={styles.weightButtonsContainer}>
                            <TouchableOpacity style={styles.readingButton}>
                                <Text style={styles.readingButtonText}>Total Weight: {totalWeight} Kg</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </View>
            </ScrollView>

            {/* Integrated Modal with Date Pickers */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'tripBranch' && 'Select Trip Branch'}
                            {modalType === 'tripId' && 'Select Trip ID'}
                            {modalType === 'docType' && 'Select DOC Type'}
                            {modalType === 'selectDoc' && 'Select Sales Document'}
                            {modalType === 'place' && 'Select Place'}
                            {!['tripBranch', 'tripId', 'docType', 'selectDoc', 'place'].includes(modalType) && 'Select Item'}
                        </Text>

                        {/* Date pickers for selectDoc and tripId modals */}
                        {(modalType === 'selectDoc' || modalType === 'tripId') && (
                            <View style={{ marginBottom: 10 }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 0 }}>
                                    <TouchableOpacity 
                                        onPress={() => setShowFromPicker(true)} 
                                        style={{ flex: 1, marginRight: 5, backgroundColor: '#f5f5f5', borderRadius: 8, padding: 10, borderWidth: 1, borderColor: '#ddd', alignItems: 'center' }}
                                    >
                                        <Text style={{ color: modalFromDate ? '#333' : '#888', fontWeight: 'bold' }}>
                                            {modalFromDate ? formatDateForDisplay(modalFromDate) : 'From Date'}
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity 
                                        onPress={() => setShowToPicker(true)} 
                                        style={{ flex: 1, marginLeft: 5, backgroundColor: '#f5f5f5', borderRadius: 8, padding: 10, borderWidth: 1, borderColor: '#ddd', alignItems: 'center' }}
                                    >
                                        <Text style={{ color: modalToDate ? '#333' : '#888', fontWeight: 'bold' }}>
                                            {modalToDate ? formatDateForDisplay(modalToDate) : 'To Date'}
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{ 
                                            backgroundColor: (modalLoading || !modalFromDate || !modalToDate) ? '#666' : '#041C44', 
                                            borderRadius: 8, 
                                            paddingVertical: 12, 
                                            paddingHorizontal: 18, 
                                            alignItems: 'center', 
                                            marginLeft: 8, 
                                            borderWidth: 1, 
                                            borderColor: '#FDC500', 
                                            height: 48, 
                                            justifyContent: 'center',
                                            opacity: (modalLoading || !modalFromDate || !modalToDate) ? 0.7 : 1
                                        }}
                                        onPress={() => {
                                            if (modalFromDate && modalToDate) {
                                                handleFetchTripApi({
                                                    fromDate: formatDateForAPI(modalFromDate),
                                                    toDate: formatDateForAPI(modalToDate)
                                                });
                                            }
                                        }}
                                        disabled={modalLoading || !modalFromDate || !modalToDate}
                                    >
                                        <Text style={{ color: 'white', fontWeight: 'bold', fontFamily: 'Poppins', fontSize: 15 }}>
                                            {modalLoading ? 'Loading...' : 'Fetch'}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                {showFromPicker && (
                                    <DateTimePicker
                                        value={modalFromDate ? new Date(modalFromDate) : new Date()}
                                        mode="date"
                                        display="default"
                                        onChange={(event, selectedDate) => {
                                            setShowFromPicker(false);
                                            if (selectedDate) {
                                                const selectedDateStr = selectedDate.toISOString().split('T')[0];
                                                setModalFromDate(selectedDateStr);
                                            }
                                        }}
                                    />
                                )}
                                {showToPicker && (
                                    <DateTimePicker
                                        value={modalToDate ? new Date(modalToDate) : new Date()}
                                        mode="date"
                                        display="default"
                                        onChange={(event, selectedDate) => {
                                            setShowToPicker(false);
                                            if (selectedDate) {
                                                const selectedDateStr = selectedDate.toISOString().split('T')[0];
                                                setModalToDate(selectedDateStr);
                                            }
                                        }}
                                    />
                                )}
                            </View>
                        )}

                        {/* Search input */}
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>

                        {/* Modal content */}
                        <ScrollView style={styles.modalScrollContainer}>
                            {modalType === 'tripId' && (
                                <View style={styles.tripCardsContainer}>
                                    {modalLoading ? (
                                        <View style={styles.emptySearchContainer}>
                                            <ActivityIndicator size="large" color="#007AFF" />
                                            <Text style={styles.emptySearchText}>Loading trips...</Text>
                                        </View>
                                    ) : filteredModalData.length > 0 ? (
                                        filteredModalData.map((trip, index) => (
                                            <TouchableOpacity
                                                key={trip.tripID || index}
                                                style={styles.tripCard}
                                                onPress={() => handleModalItemSelect(trip)}
                                            >
                                                <Text style={styles.tripIdText}>
                                                    {trip.tripID || trip.displayText || trip}
                                                </Text>
                                            </TouchableOpacity>
                                        ))
                                    ) : (
                                        <View style={styles.emptySearchContainer}>
                                            <Text style={styles.emptySearchText}>No trips found</Text>
                                        </View>
                                    )}
                                </View>
                            )}

                            {modalType === 'selectDoc' && (
                                <View style={styles.tripCardsContainer}>
                                    {modalLoading ? (
                                        <View style={styles.emptySearchContainer}>
                                            <ActivityIndicator size="large" color="#007AFF" />
                                            <Text style={styles.emptySearchText}>Loading sales documents...</Text>
                                        </View>
                                    ) : filteredModalData.length > 0 ? (
                                        filteredModalData.map((doc, index) => (
                                            <TouchableOpacity
                                                key={doc.DocID || index}
                                                style={styles.salesDocCard}
                                                onPress={() => handleModalItemSelect(doc)}
                                            >
                                                <Text style={styles.salesDocText}>
                                                    {doc.DocID || doc.displayText || doc}
                                                </Text>
                                            </TouchableOpacity>
                                        ))
                                    ) : (
                                        <View style={styles.emptySearchContainer}>
                                            <Text style={styles.emptySearchText}>No sales documents found</Text>
                                        </View>
                                    )}
                                </View>
                            )}

                            {!['tripId', 'selectDoc'].includes(modalType) && (
                                <View style={styles.modalItemsContainer}>
                                    {filteredModalData.length > 0 ? (
                                        filteredModalData.map((item, index) => (
                                            <TouchableOpacity
                                                key={index}
                                                style={styles.modalItem}
                                                onPress={() => handleModalItemSelect(item)}
                                            >
                                                <Text style={styles.modalItemText}>
                                                    {typeof item === 'string' ? item : 
                                                     item.branchName || item.TransTypeName || item.displayText || 
                                                     item.name || item.title || 'Unknown'}
                                                </Text>
                                            </TouchableOpacity>
                                        ))
                                    ) : (
                                        <View style={styles.emptySearchContainer}>
                                            <Text style={styles.emptySearchText}>
                                                {modalLoading ? 'Loading...' : 'No items found'}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    // Layout Containers
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    contentContainer: {
        flex: 1,
        padding: 10,
    },
    sectionContainer: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 20,
        marginHorizontal: 8,
        marginVertical: 8,
        borderWidth: 1,
        borderColor: '#e3f2fd',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.12,
        shadowRadius: 4,
        elevation: 5,
    },
    sectionHeaderContainer: {
        backgroundColor: '#f5f5f5',
        padding: 12,
        marginVertical: 8,
        borderRadius: 8,
        borderLeftWidth: 4,
        borderLeftColor: '#3f51b5',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '700',
        color: '#2c3e50',
        textAlign: 'left',
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
        marginBottom: 0,
    },

    // Loading Overlay
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255,255,255,0.9)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#02096A',
        fontFamily: 'Poppins',
        fontWeight: '500',
    },

    // Form Layout
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 15,
    },

    // Dropdowns
    dropdownContainer: {
        flex: 1,
        marginRight: 10,
    },
    dropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 14,
        height: 52,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    inputText: {
        color: '#2c3e50',
        fontFamily: 'Poppins',
        fontSize: 15,
        fontWeight: '600',
    },
    placeholderText: {
        color: '#999',
        fontWeight: '400',
    },
    dropdownIcon: {
        color: '#333',
        fontSize: 16,
    },

    // Input Fields
    inputWithButtonContainer: {
        flex: 1,
        flexDirection: 'row',
        marginRight: 10,
        marginBottom: 5,
    },
    inputWithButton: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 14,
        height: 52,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#2c3e50',
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    weightInputContainer: {
        flex: 1,
        marginLeft: 10,
    },
    remarksRowInput: {
        flex: 2,
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 14,
        height: 52,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#2c3e50',
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
        marginRight: 10,
    },
    weightInput: {
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 14,
        height: 52,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#2c3e50',
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
        textAlign: 'center',
    },
    errorText: {
        color: '#f44336',
        fontSize: 12,
        fontFamily: 'Poppins',
        fontWeight: '500',
        marginTop: 4,
        marginLeft: 4,
    },
    remarksInput: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 14,
        minHeight: 80,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#2c3e50',
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
        textAlignVertical: 'top',
    },

    // Buttons
    selectButton: {
        backgroundColor: '#3f51b5',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 18,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: '#ffd54f',
        height: 52,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 3,
    },
    selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: 10,
    },
    addButton: {
        backgroundColor: '#4caf50',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 10,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
        marginRight: 10,
        flex: 1,
    },
    boldText: {
        fontWeight: 'bold',
    },
    addButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 15,
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
    },
    clearButton: {
        backgroundColor: '#f44336',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#ffd54f',
        height: 52,
        flex: 1,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
    },
    clearButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 15,
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
    },
    disabledButton: {
        backgroundColor: '#666',
        opacity: 0.7,
    },

    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        minWidth: 150,
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },

    // Weight Buttons
    weightButtonsContainer: {
        flexDirection: 'row',
    },
    readingButton: {
        backgroundColor: '#d8d8d8',
        justifyContent: 'center',
        alignItems: 'center',
        height: 50,
        borderRadius: 8,
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        paddingHorizontal: 15,
    },
    readingButtonText: {
        fontSize: 15,
        color: '#333',
        fontFamily: 'Poppins',
    },

    // Table Styles
    tableScrollContainer: {
        marginBottom: 15,
        marginTop: 15,
    },
    tableContainer: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 6,
        minWidth: 900,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#3f51b5',
        borderBottomWidth: 2,
        borderBottomColor: '#1a237e',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 50,
        alignItems: 'center',
    },
    tableHeaderText: {
        fontWeight: '700',
        fontSize: 13,
        color: '#ffffff',
        fontFamily: 'Poppins',
        textAlign: 'center',
        letterSpacing: 0.5,
        textTransform: 'uppercase',
        lineHeight: 16,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 2,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#e8eaf6',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 48,
        backgroundColor: '#ffffff',
        alignItems: 'center',
    },
    tableRowAlternate: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#e8eaf6',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 48,
        backgroundColor: '#f8f9fa',
        alignItems: 'center',
    },
    tableCell: {
        fontSize: 13,
        color: '#2c3e50',
        fontFamily: 'Poppins',
        textAlign: 'center',
        fontWeight: '600',
        lineHeight: 18,
        paddingVertical: 2,
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    lineNumberCell: {
        width: 70,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    docTypeCell: {
        width: 120,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    chooseDocCell: {
        width: 120,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    customerCell: {
        width: 150,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    placeCell: {
        width: 100,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    remarksCell: {
        width: 150,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    weightCell: {
        width: 100,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    emptyTableRow: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
        fontStyle: 'italic',
    },
    headerCheckbox: {
        transform: [{ scale: 1.2 }],
    },

    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContent: {
        width: '90%',
        maxHeight: '85%',
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 24,
        borderWidth: 1,
        borderColor: '#e3f2fd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.25,
        shadowRadius: 6,
        elevation: 8,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: '700',
        marginBottom: 20,
        textAlign: 'center',
        color: '#2c3e50',
        fontFamily: 'Poppins',
        paddingBottom: 12,
        borderBottomWidth: 2,
        borderBottomColor: '#e3f2fd',
        letterSpacing: 0.5,
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f8f9fa',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        borderRadius: 10,
        padding: 12,
        fontSize: 15,
        fontFamily: 'Poppins',
        color: '#2c3e50',
        fontWeight: '500',
    },
    modalScrollContainer: {
        maxHeight: 400,
    },
    
    // Trip Cards Container
    tripCardsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    tripCard: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    tripIdText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Sales Document Cards
    salesDocCard: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    salesDocText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Regular Modal Items
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Empty States
    emptySearchContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    emptySearchText: {
        textAlign: 'center',
        fontFamily: 'Poppins',
        color: '#666',
        marginTop: 20,
        marginBottom: 20,
        fontStyle: 'italic',
    },
    
    // Close Button
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
});

export default TripPlan;