import React, { useState, useEffect, useMemo } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    Platform,
    PermissionsAndroid,
    KeyboardAvoidingView,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';
import CustomLoader from '../../../components/CustomLoader';
import { local_signage } from '../../../../const';
import { fetchBusinessDay } from '../../../apiHandling/businessDayAPI'; // <-- Add this import

export const TripRoaster = () => {
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [vehicleNo, setVehicleNo] = useState('');
    const [startReading, setStartReading] = useState('');
    const [endReading, setEndReading] = useState('');
    
    // Validation states for reading inputs
    const [startReadingError, setStartReadingError] = useState('');
    const [endReadingError, setEndReadingError] = useState('');
    const [remarks, setRemarks] = useState('');
    const [selectedOperator, setSelectedOperator] = useState('');
    const [selectedOperatorId, setSelectedOperatorId] = useState('');
    const [selectedRows, setSelectedRows] = useState([]);
    const [operatorsList, setOperatorsList] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [isSelectAllOperators, setIsSelectAllOperators] = useState(false);
    const [authToken, setAuthToken] = useState('');
    const [currentBranchId, setCurrentBranchId] = useState('');
    const [currentBranch, setCurrentBranch] = useState(null);
    const [loading, setLoading] = useState(false);
    const [businessDate, setBusinessDate] = useState('');

    // Background trips for vehicle validation
    const [backgroundTrips, setBackgroundTrips] = useState([]);
    
    // Trip list for View functionality
    const [tripList, setTripList] = useState([]);
    const [tripListLoading, setTripListLoading] = useState(false);
    
    // Date picker states for API filtering
    const [fromDate, setFromDate] = useState('');
    const [toDate, setToDate] = useState('');
    const [showFromPicker, setShowFromPicker] = useState(false);
    const [showToPicker, setShowToPicker] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [tripDetails, setTripDetails] = useState(null);
    const [loadingTripDetails, setLoadingTripDetails] = useState(false);
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [tripsList, setTripsList] = useState([]);
    const [selectedTripForView, setSelectedTripForView] = useState(null);

    // API Data states
    const [vehicleOptions, setVehicleOptions] = useState([]);
    const [operatorOptions, setOperatorOptions] = useState([]);
    const [branchOptions, setBranchOptions] = useState([]);
    const [cachedTripDetails, setCachedTripDetails] = useState({});

    // Memoized trip details to prevent unnecessary API calls
    const memoizedTripDetails = useMemo(() => {
        return cachedTripDetails;
    }, [cachedTripDetails]);

    // Get branch name for display in header
    const getBranchName = () => {
        if (currentBranch) {
            return currentBranch.BranchName || currentBranch.branchName || 'Selected Branch';
        }
        return 'No Branch Selected';
    };

    // Validation functions for reading inputs
    const validateStartReading = (value) => {
        const numericRegex = /^\d*\.?\d*$/;
        if (value === '') {
            setStartReadingError('');
            return true;
        }
        if (!numericRegex.test(value)) {
            setStartReadingError('Only numeric and decimal values are allowed');
            return false;
        }
        setStartReadingError('');
        return true;
    };

    // Basic numeric validation for typing
    const validateEndReadingInput = (value) => {
        const numericRegex = /^\d*\.?\d*$/;
        if (value === '') {
            return true;
        }
        return numericRegex.test(value);
    };

    // Full validation for onBlur
    const validateEndReadingComplete = (value) => {
        const numericRegex = /^\d*\.?\d*$/;
        if (value === '') {
            setEndReadingError('');
            return true;
        }
        if (!numericRegex.test(value)) {
            setEndReadingError('Only numeric and decimal values are allowed');
            return false;
        }
        
        // Check if end reading is less than start reading
        const startValue = parseFloat(startReading) || 0;
        const endValue = parseFloat(value) || 0;
        
        if (startValue > 0 && endValue > 0 && endValue < startValue) {
            setEndReadingError('End reading cannot be less than start reading');
            return false;
        }
        
        setEndReadingError('');
        return true;
    };

    // Change handlers with validation
    const handleStartReadingChange = (value) => {
        if (validateStartReading(value)) {
            setStartReading(value);
            // Re-validate end reading when start reading changes
            if (endReading && endReading.trim() !== '') {
                validateEndReadingComplete(endReading);
            }
        }
    };

    const handleEndReadingChange = (value) => {
        // Only check basic numeric format during typing
        if (validateEndReadingInput(value)) {
            setEndReading(value);
            // Clear error while typing if it was showing
            if (endReadingError) {
                setEndReadingError('');
            }
        }
    };

    const handleEndReadingBlur = () => {
        // Full validation when user leaves the field
        validateEndReadingComplete(endReading);
    };

    useEffect(() => {
        const initializeApp = async () => {
            await getAuthToken();
        };
        initializeApp();
    }, []);

    useEffect(() => {
        // Load branches after auth token is available
        if (authToken) {
            loadBranches();
        }
    }, [authToken]);

    useEffect(() => {
        if (authToken && currentBranchId) {
            console.log('🚀 Prerequisites met - loading initial data...');
            loadVehicleNumbers(currentBranchId);
            loadOperators();
        }
    }, [authToken, currentBranchId]);

    // Load business date on mount
    useEffect(() => {
        const loadBusinessDate = async () => {
            if (authToken && currentBranchId) {
                try {
                    const date = await fetchBusinessDay(authToken, currentBranchId);
                    setBusinessDate(date);
                    console.log('📅 Business date loaded:', date);
                    
                    // Fetch background trips for vehicle validation
                    if (date) {
                        console.log('🔄 Fetching background trips for vehicle validation...');
                        fetchBackgroundTrips(currentBranchId, date);
                    }
                } catch (error) {
                    console.error('❌ Error loading business date:', error);
                }
            }
        };
        loadBusinessDate();
    }, [authToken, currentBranchId]);

    useEffect(() => {
        if (searchTerm) {
            const filtered = modalData.filter(item => {
                if (typeof item === 'string') {
                    return item.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.VehicleId) {
                    return item.VehicleId.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.EmpName) {
                    return item.EmpName.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.UserName) {
                    return item.UserName.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.branchName) {
                    return item.branchName.toLowerCase().includes(searchTerm.toLowerCase());
                }
                return false;
            });
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    useEffect(() => {
        if (isSelectAllOperators) {
            const allIndices = operatorsList.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllOperators]);

    useEffect(() => {
        if (modalVisible && modalType === 'vehicleNo' && vehicleOptions.length > 0) {
            setModalData(vehicleOptions);
            setFilteredModalData(vehicleOptions);
        }
    }, [vehicleOptions, modalVisible, modalType]);

    useEffect(() => {
        if (modalVisible && modalType === 'operator' && operatorOptions.length > 0) {
            setModalData(operatorOptions);
            setFilteredModalData(operatorOptions);
        }
    }, [operatorOptions, modalVisible, modalType]);

    useEffect(() => {
        if (modalVisible && modalType === 'branch' && branchOptions.length > 0) {
            setModalData(branchOptions);
            setFilteredModalData(branchOptions);
        }
    }, [branchOptions, modalVisible, modalType]);

    // Remove duplicate useEffect for business date (already handled above)

    const getAuthToken = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            if (token) {
                setAuthToken(token);
            } else {
                Alert.alert('Authentication Error', 'Please login again to continue');
            }

            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                setCurrentBranch(branch);

                // Handle different property name variations
                let branchId = branch.BranchId || branch.branchID || branch.id || branch.branchId;
                if (branchId) {
                    setCurrentBranchId(branchId);
                }
            }
        } catch (error) {
            console.error('Error getting stored data:', error);
            Alert.alert('Error', 'Failed to retrieve stored data');
        }
    };

    // Helper function to format date as YYYYMMDD
    const formatDateForAPI = (date) => {
        if (!date) return '';
        if (typeof date === 'string' && date.includes('-')) {
            const [day, month, year] = date.split('-');
            return `${year}${month.padStart(2, '0')}${day.padStart(2, '0')}`;
        }
        return date;
    };

    // Helper function to fetch background trips for vehicle validation
    const fetchBackgroundTrips = async (branchId, businessDate) => {
        try {
            const dateString = formatDateForAPI(businessDate);
            const url = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${dateString}/${dateString}`;
            console.log('🔄 Fetching background trips from:', url);
            
            const data = await makeApiCall(url);
            if (data && Array.isArray(data)) {
                setBackgroundTrips(data);
                console.log('✅ Background trips fetched:', data.length, 'trips');
            } else {
                console.log('⚠️ No background trips found');
                setBackgroundTrips([]);
            }
        } catch (error) {
            console.error('❌ Error fetching background trips:', error);
            setBackgroundTrips([]);
        }
    };

    // Helper function to check if vehicle is already in use
    const isVehicleInUse = (vehicleId) => {
        if (!vehicleId || !backgroundTrips.length) {
            return false;
        }
        const existingTrip = backgroundTrips.find(trip => 
            trip.vehicleNo && trip.vehicleNo.toLowerCase() === vehicleId.toLowerCase()
        );
        return existingTrip ? existingTrip : false;
    };

    // Helper function to fetch trip list for View functionality
    const fetchTripList = async (branchId, fromDate, toDate) => {
        setTripListLoading(true);
        try {
            const fromDateFormatted = formatDateForAPI(fromDate);
            const toDateFormatted = formatDateForAPI(toDate);
            const url = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${fromDateFormatted}/${toDateFormatted}`;
            console.log('🔄 Fetching trip list from:', url);
            
            const data = await makeApiCall(url);
            if (data && Array.isArray(data)) {
                setTripList(data);
                console.log('✅ Trip list fetched:', data.length, 'trips');
            } else {
                console.log('⚠️ No trips found');
                setTripList([]);
            }
        } catch (error) {
            console.error('❌ Error fetching trip list:', error);
            setTripList([]);
        } finally {
            setTripListLoading(false);
        }
    };

    // Helper function to fetch vehicle trip details
    const fetchVehicleTripDetails = async (branchId, tripId) => {
        try {
            const url = `https://retailuat.abisibg.com/api/v1/fetchvehicletrip?branchID=${branchId}&TripId=${tripId}`;
            console.log('🔄 Fetching vehicle trip details from:', url);
            
            const data = await makeApiCall(url);
            if (data && Array.isArray(data) && data.length > 0) {
                console.log('✅ Vehicle trip details fetched:', data[0]);
                return data[0];
            } else {
                console.log('⚠️ No vehicle trip details found');
                return null;
            }
        } catch (error) {
            console.error('❌ Error fetching vehicle trip details:', error);
            return null;
        }
    };

    const makeApiCall = async (url, options = {}) => {
        if (!authToken) {
            console.error('❌ No auth token available');
            Alert.alert('Authentication Error', 'Please login again to continue');
            return null;
        }

        console.log('🌐 Making API call to:', url);
        console.log('🔧 Request options:', {
            method: options.method || 'GET',
            headers: {
                'Authorization': `Bearer ${authToken.substring(0, 20)}...`,
                'Content-Type': 'application/json',
                ...options.headers,
            },
            body: options.body ? 'Present' : 'None'
        });

        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
            });

            console.log('📡 Response status:', response.status);
            console.log('📡 Response status text:', response.statusText);
            console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

            if (response.status === 401) {
                console.error('❌ Unauthorized - Session expired');
                Alert.alert('Session Expired', 'Please login again to continue');
                return null;
            }

            if (!response.ok) {
                console.error('❌ HTTP error:', response.status, response.statusText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ API call successful');
            console.log('📥 Response data:', data);
            return data;
        } catch (error) {
            console.error('❌ API call error:', error);
            console.error('❌ Error message:', error.message);
            console.error('❌ Error stack:', error.stack);
            Alert.alert('Error', `Failed to fetch data: ${error.message}`);
            return null;
        }
    };

    const loadBranches = async () => {
        if (!authToken) return;

        setLoading(true);
        try {
            // Get stored branch from AsyncStorage
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            console.log('🟡 Loading stored branch from AsyncStorage:', selectedBranch);

            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                console.log('✅ Stored branch loaded:', branch);
                
                // Set as single branch option
                const branchData = [branch];
                setBranchOptions(branchData);
                
                if (modalType === 'branch') {
                    setModalData(branchData);
                    setFilteredModalData(branchData);
                }
            } else {
                console.warn('⚠️ No stored branch found');
                setBranchOptions([]);
                if (modalType === 'branch') {
                    setModalData([]);
                    setFilteredModalData([]);
                }
            }
        } catch (error) {
            console.error('❌ Error loading stored branch:', error);
            setBranchOptions([]);
            if (modalType === 'branch') {
                setModalData([]);
                setFilteredModalData([]);
            }
        } finally {
            setLoading(false);
            console.log('🟡 Branch loading completed');
        }
    };

    const loadVehicleNumbers = async (branchId) => {
        if (!branchId) return;

        setLoading(true);
        try {
            const url = `${local_signage}/api/v1/vehicleno?BranchID=L102`;
            const data = await makeApiCall(url);
            if (data) {
                setVehicleOptions(data);
                if (modalType === 'vehicleNo') {
                    setModalData(data);
                    setFilteredModalData(data);
                }
            }
        } catch (error) {
            console.error('Error loading vehicle numbers:', error);
        } finally {
            setLoading(false);
        }
    };

const loadOperators = async () => {
    if (!currentBranchId) return;

    setLoading(true);
    try {
        // Use the new operator API like in TripScreen.jsx
        const url = `https://retailuat.abisibg.com/api/v1/operator?CurrentBranchId=${currentBranchId}&DesigTypeCode=VEHST`;
        console.log('🟡 Loading operators from new API:', url);
        
        const data = await makeApiCall(url);
        if (data) {
            console.log('✅ Operators loaded from new API:', data.length, 'items');
            setOperatorOptions(data);
            if (modalType === 'operator') {
                setModalData(data);
                setFilteredModalData(data);
            }
        }
        
        // COMMENTED OUT: Old roster employee API
        // // Convert businessDate (DD-MM-YYYY) to YYYYMMDD
        // let formattedBusinessDate = '';
        // if (businessDate && businessDate.includes('-')) {
        //     const [day, month, year] = businessDate.split('-');
        //     formattedBusinessDate = `${year}${month}${day}`;
        // } else if (businessDate && businessDate.length === 8) {
        //     // Already in YYYYMMDD
        //     formattedBusinessDate = businessDate;
        // } else {
        //     // fallback: use today's date
        //     const today = new Date();
        //     formattedBusinessDate =
        //         today.getFullYear().toString() +
        //         String(today.getMonth() + 1).padStart(2, '0') +
        //         String(today.getDate()).padStart(2, '0');
        // }
        // const url = `https://retailuat.abisibg.com/api/v1/rosteremployee?BranchID=${currentBranchId}&BusinessDate=${formattedBusinessDate}`;
        
    } catch (error) {
        console.error('Error loading operators:', error);
    } finally {
        setLoading(false);
    }
};

    const fetchVehicleLastReading = async (vehicleCode) => {
        console.log('🟡 Fetching vehicle last reading for:', vehicleCode);
        
        try {
            // Format business date as YYYYMMDD
            const currentDate = new Date();
            const businessDate = currentDate.getFullYear().toString() +
                (currentDate.getMonth() + 1).toString().padStart(2, '0') +
                currentDate.getDate().toString().padStart(2, '0');

            const url = `https://retailuat.abisibg.com/api/v1/fetchvehiclelastreading?VehicleCode=${vehicleCode}&BusinessDate=${businessDate}`;
            console.log('🟡 Vehicle Last Reading API URL:', url);

            const data = await makeApiCall(url);
            if (data && data.length > 0 && data[0].LastReading !== undefined) {
                console.log('✅ Vehicle last reading fetched:', data[0].LastReading);
                setStartReading(data[0].LastReading.toString());
            } else {
                console.warn('⚠️ No last reading data received for vehicle:', vehicleCode);
                setStartReading('');
            }
        } catch (error) {
            console.error('❌ Error fetching vehicle last reading:', error);
            setStartReading('');
        }
    };

    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            saveTrip();
        } else if (option === 'View') {
            // Auto-fetch trips for business date and open modal
            if (!currentBranchId) {
                Alert.alert('Error', 'Branch information is missing. Please select a branch.');
                return;
            }
            
            if (!businessDate) {
                Alert.alert('Error', 'Business date is not available. Please try again.');
                return;
            }
            
            // Set dates to business date
            setFromDate(businessDate);
            setToDate(businessDate);
            
            // Open modal and auto-fetch trips
            setModalType('tripList');
            setModalVisible(true);
            
            // Auto-fetch trips for business date
            fetchTripList(currentBranchId, businessDate, businessDate);
        } else if (option === 'Cancel') {
            resetForm();
        }
    };

    const viewTrips = async () => {
        if (!currentBranchId) {
            Alert.alert('Validation Error', 'Branch information is missing. Please select a branch.');
            return;
        }

        setLoading(true);
        try {
            // Format businessDate as YYYYMMDD
            let dateString = '';
            if (businessDate) {
                const [day, month, year] = businessDate.split('-');
                dateString = `${year}${month}${day}`;
            } else {
                const today = new Date();
                dateString = today.toISOString().slice(0, 10).replace(/-/g, '');
            }

            const url = `https://retailUAT.abisaio.com:9001/api/Trip/${currentBranchId}/${dateString}/${dateString}`;
            console.log('🟡 Fetching trips from:', url);

            const data = await makeApiCall(url);
            
            if (data && data.length > 0) {
                console.log('✅ Trips fetched successfully, count:', data.length);
                setTripsList(data);
                setViewModalVisible(true);
            } else {
                console.log('ℹ️ No trips found for today');
                Alert.alert(
                    'No Trips Found', 
                    'No trips found for today. Please check if there are any trips created for the current date.',
                    [
                        {
                            text: 'OK',
                            onPress: () => console.log('User acknowledged no trips found')
                        }
                    ]
                );
            }
        } catch (error) {
            console.error('❌ Error fetching trips:', error);
            console.error('❌ Error message:', error.message);
            Alert.alert(
                'Error', 
                `Failed to fetch trips: ${error.message}`,
                [
                    {
                        text: 'OK',
                        onPress: () => console.log('User acknowledged error')
                    }
                ]
            );
        } finally {
            setLoading(false);
        }
    };

    const fetchTripDetails = async (tripId) => {
        if (!tripId || !currentBranchId) {
            Alert.alert('Error', 'Missing trip ID or branch information');
            return;
        }

        // Check if trip details are already cached
        if (memoizedTripDetails[tripId]) {
            console.log('✅ Using cached trip details for Trip ID:', tripId);
            const cachedData = memoizedTripDetails[tripId];
            setTripDetails(cachedData);
            
            try {
                await generatePDF(cachedData);
            } catch (pdfError) {
                console.error('❌ PDF generation failed, showing cached trip details in alert:', pdfError);
                showTripDetailsAlert(cachedData);
            }
            return;
        }

        setLoadingTripDetails(true);
        try {
            console.log('🟡 Fetching trip details for Trip ID:', tripId);
            console.log('🟡 Using branch ID:', currentBranchId);
            
            const url = `https://retailuat.abisibg.com/api/v1/fetchvehicletrip?branchID=${currentBranchId}&TripId=${tripId}`;
            console.log('🟡 Fetching trip details from:', url);

            const data = await makeApiCall(url);
            
            if (data && data.length > 0) {
                console.log('✅ Trip details fetched successfully:', data[0]);
                const tripData = data[0];
                
                // Cache the trip details
                setCachedTripDetails(prev => ({
                    ...prev,
                    [tripId]: tripData
                }));
                
                setTripDetails(tripData);
                
                // Generate PDF with the fetched data
                try {
                    await generatePDF(tripData);
                } catch (pdfError) {
                    console.error('❌ PDF generation failed, showing trip details in alert:', pdfError);
                    showTripDetailsAlert(tripData);
                }
            } else {
                console.warn('⚠️ No trip details found for Trip ID:', tripId);
                Alert.alert(
                    'No Data Found', 
                    `No details found for Trip ID: ${tripId}. Please check if the trip exists and try again.`,
                    [
                        {
                            text: 'OK',
                            onPress: () => console.log('User acknowledged no data found')
                        }
                    ]
                );
            }
        } catch (error) {
            console.error('❌ Error fetching trip details:', error);
            console.error('❌ Error message:', error.message);
            Alert.alert(
                'Error', 
                `Failed to fetch trip details: ${error.message}`,
                [
                    {
                        text: 'OK',
                        onPress: () => console.log('User acknowledged error')
                    }
                ]
            );
        } finally {
            setLoadingTripDetails(false);
        }
    };

    // Helper function to show trip details alert
    const showTripDetailsAlert = (tripData) => {
        const tripDetails = `
TRIP DETAILS REPORT
===================

Trip ID: ${tripData.TripId || 'N/A'}
Branch: ${tripData.VehBranchName || 'N/A'} (${tripData.BranchID || 'N/A'})
Trip Date: ${tripData.TripDate ? new Date(tripData.TripDate).toLocaleDateString() : 'N/A'}
Trip Type: ${tripData.TripTypeCode || 'N/A'}
Status: ${tripData.Closed ? 'Closed' : 'Open'}

Vehicle Information:
- Vehicle Number: ${tripData.vehicleNo || tripData.VehicleNo || 'N/A'}
- Start Reading: ${tripData.StartReading || '0'} km
- End Reading: ${tripData.EndReading || '0'} km
- Total Distance: ${(tripData.EndReading || 0) - (tripData.StartReading || 0)} km

Additional Details:
- Destination Branch: ${tripData.DestBranchName || 'N/A'}
- Remarks: ${tripData.remarks || tripData.Remarks || 'No remarks'}
- Created Date: ${tripData.CreatedDate ? new Date(tripData.CreatedDate).toLocaleString() : 'N/A'}
- Posted: ${tripData.Posted ? 'Yes' : 'No'}

Generated on: ${new Date().toLocaleString()}
        `.trim();

        Alert.alert(
            'Trip Details (PDF Generation Failed)',
            tripDetails,
            [
                {
                    text: 'Copy Details',
                    onPress: () => {
                        // In a real app, you would copy to clipboard here
                        console.log('Trip details for copying:', tripDetails);
                        Alert.alert('Info', 'Trip details are ready for copying. In a production app, this would copy to clipboard.');
                    }
                },
                {
                    text: 'OK',
                    onPress: () => console.log('User viewed trip details in alert')
                }
            ]
        );
    };

    const generatePDF = async (tripData) => {
        try {
            console.log('🟡 Starting trip report generation for trip:', tripData.TripId);
            
            // Helper function to safely format dates
            const formatDate = (dateString) => {
                if (!dateString) return 'N/A';
                try {
                    const date = new Date(dateString);
                    return date.toLocaleDateString();
                } catch (error) {
                    return 'Invalid Date';
                }
            };

            const formatDateTime = (dateString) => {
                if (!dateString) return 'N/A';
                try {
                    const date = new Date(dateString);
                    return date.toLocaleString();
                } catch (error) {
                    return 'Invalid Date';
                }
            };

            // Helper function to safely get values
            const safeValue = (value, defaultValue = 'N/A') => {
                return value !== null && value !== undefined ? value.toString() : defaultValue;
            };

            // Calculate total distance
            const startReading = parseFloat(tripData.StartReading) || 0;
            const endReading = parseFloat(tripData.EndReading) || 0;
            const totalDistance = endReading - startReading;

            // Create comprehensive trip report with enhanced vehicleNo and remarks display
            const tripReport = `
TRIP DETAILS REPORT
===================

Trip ID: ${safeValue(tripData.TripId)}
Branch: ${safeValue(tripData.VehBranchName)} (${safeValue(tripData.BranchID)})
Trip Date: ${formatDate(tripData.TripDate)}
Trip Type: ${safeValue(tripData.TripTypeCode)}
Status: ${tripData.Closed ? 'Closed' : 'Open'}

Vehicle Information:
- Vehicle Number: ${safeValue(tripData.vehicleNo || tripData.VehicleNo)}
- Start Reading: ${safeValue(tripData.StartReading, '0')} km
- End Reading: ${safeValue(tripData.EndReading, '0')} km
- Total Distance: ${totalDistance} km

Additional Details:
- Destination Branch: ${safeValue(tripData.DestBranchName)}
- Remarks: ${safeValue(tripData.remarks || tripData.Remarks, 'No remarks')}
- Created Date: ${formatDateTime(tripData.CreatedDate)}
- Posted: ${tripData.Posted ? 'Yes' : 'No'}

Generated on: ${new Date().toLocaleString()}
Report ID: ${Date.now()}
            `.trim();

            console.log('✅ Trip report generated successfully');

            // Show the report in a modal-like alert with options
            Alert.alert(
                'Trip Report Generated Successfully!',
                tripReport,
                [
                    {
                        text: 'Copy Report',
                        onPress: () => {
                            console.log('🟡 User requested to copy trip report');
                            // In a production app, you would copy to clipboard here
                            Alert.alert(
                                'Copy Ready',
                                'Trip report is ready for copying. In a production app, this would copy the report to your clipboard.',
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => console.log('User acknowledged copy ready')
                                    }
                                ]
                            );
                        }
                    },
                    {
                        text: 'Save as Note',
                        onPress: () => {
                            console.log('🟡 User requested to save as note');
                            Alert.alert(
                                'Save as Note',
                                'Trip report can be saved as a note. In a production app, this would save to your notes app.',
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => console.log('User acknowledged save as note')
                                    }
                                ]
                            );
                        }
                    },
                    {
                        text: 'OK',
                        onPress: () => console.log('User viewed trip report')
                    }
                ],
                { cancelable: false }
            );

        } catch (error) {
            console.error('❌ Error generating trip report:', error);
            console.error('❌ Error stack:', error.stack);
            
            // Show trip details in a simple alert as final fallback
            const tripData = error.tripData || tripData;
            if (tripData) {
                Alert.alert(
                    'Trip Details',
                    `Trip ID: ${tripData.TripId || 'N/A'}\n` +
                    `Branch: ${tripData.VehBranchName || 'N/A'}\n` +
                    `Vehicle: ${tripData.vehicleNo || tripData.VehicleNo || 'N/A'}\n` +
                    `Start Reading: ${tripData.StartReading || '0'} km\n` +
                    `End Reading: ${tripData.EndReading || '0'} km\n` +
                    `Total Distance: ${(tripData.EndReading || 0) - (tripData.StartReading || 0)} km\n` +
                    `Status: ${tripData.Closed ? 'Closed' : 'Open'}\n` +
                    `Remarks: ${tripData.remarks || tripData.Remarks || 'No remarks'}`,
                    [
                        {
                            text: 'OK',
                            onPress: () => console.log('User viewed trip details in fallback alert')
                        }
                    ]
                );
            } else {
                Alert.alert('Error', 'Failed to generate trip report: ' + error.message);
            }
        }
    };

    const handleTripSelection = (trip) => {
        setSelectedTripForView(trip);
        console.log('🟡 Trip selected for viewing:', trip);
        
        Alert.alert(
            'Trip Details',
            `Do you want to view and print details for Trip ID: ${trip.tripID}?\n\nVehicle: ${trip.vehicleNo || 'N/A'}\nRemarks: ${trip.remarks || 'No remarks'}\nDate: ${trip.tripDate ? new Date(trip.tripDate).toLocaleDateString() : 'N/A'}`,
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                    onPress: () => console.log('User cancelled trip selection')
                },
                {
                    text: 'View & Print',
                    onPress: () => {
                        console.log('🟡 User confirmed trip selection, closing modal and fetching details...');
                        setViewModalVisible(false);
                        // Small delay to ensure modal closes before starting PDF generation
                        setTimeout(() => {
                            fetchTripDetails(trip.tripID);
                        }, 300);
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setRemarks('');
        setSelectedOperator('');
        setSelectedOperatorId('');
        setSelectedRows([]);
        setOperatorsList([]);
    };

    const saveTrip = async () => {
        if (operatorsList.length === 0) {
            Alert.alert('Validation Error', 'Please add at least one operator');
            return;
        }

        if (!currentBranchId) {
            Alert.alert('Validation Error', 'Branch information is missing. Please login again.');
            return;
        }

        setLoading(true);
        try {
            const userData = await AsyncStorage.getItem('userData');
            const user = userData ? JSON.parse(userData) : null;
            const currentUserId = user?.userId || user?.id || user?.empId || 'SYSTEM';

            // Format tripDate as YYYYMMDD
            let tripDate = '';
            if (businessDate) {
                const [day, month, year] = businessDate.split('-');
                tripDate = `${year}${month}${day}`;
            } else {
                const today = new Date();
                tripDate = today.toISOString().slice(0, 10).replace(/-/g, '');
            }
            const todayISO = new Date().toISOString();

            const instaTrips = operatorsList.map(operator => ({
                remarks: operator.remarks || "",
                startReading: parseInt(operator.startReading) || 0,
                endReading: parseInt(operator.endReading) || 0,
                operatorEmpId: operator.operatorId,
                vehicleNo: operator.vehicleNo,
                tpVehicleNo: operator.vehicleNo
            }));

            const tripData = {
                branchId: currentBranchId,
                tripDate: tripDate, // Use business date
                createdUserId: currentUserId,
                createdDate: todayISO, // Use today's date
                instaTrips: instaTrips
            };
            console.log(tripData);

            console.log('🚀 Making POST API call to TripRoster...');
            console.log('📤 Request URL:', `https://retailuat.abisaio.com:9001/api/TripRoster`);
            console.log('📤 Request Method: POST');
            console.log('📤 Request Body:', JSON.stringify(tripData, null, 2));
            
            const response = await makeApiCall(`https://retailuat.abisaio.com:9001/api/TripRoster`, {
                method: 'POST', // Changed from PUT to POST
                body: JSON.stringify(tripData),
            });
            
            console.log('📥 API Response:', response);
            console.log('📥 Response Type:', typeof response);
            console.log('📥 Response Status:', response ? 'Success' : 'Failed');

            if (response) {
                // Extract trip IDs from response
                let tripIds = [];
                if (Array.isArray(response)) {
                    tripIds = response.map(trip => trip.tripID).filter(id => id);
                } else if (response.tripID) {
                    tripIds = [response.tripID];
                }

                const tripIdsText = tripIds.length > 0 
                    ? `\n\nTrip IDs created:\n${tripIds.join('\n')}`
                    : '';

                Alert.alert(
                    'Success',
                    `Trip saved successfully!${tripIdsText}`,
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                resetForm();
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Error', 'Failed to save trip. Please try again.');
            }
        } catch (error) {
            console.error('Error saving trip:', error);
            Alert.alert('Error', `Failed to save trip: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllOperators(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            if (selectedRows.length + 1 === operatorsList.length) {
                setIsSelectAllOperators(true);
            }
        }
    };

    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newOperators = operatorsList.filter((_, index) => !selectedRows.includes(index));
        setOperatorsList(newOperators);
        setSelectedRows([]);
        setIsSelectAllOperators(false);
    };

    const handleClearTripDetails = () => {
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setRemarks('');
        setSelectedOperator('');
        setSelectedOperatorId('');
    };

    const openModal = async (type) => {
        setSearchTerm('');
        setModalType(type);
        setModalVisible(true);

        switch (type) {
            case 'branch':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadBranches();
                
                // Auto-select the stored branch if available
                const selectedBranch = await AsyncStorage.getItem('selectedBranch');
                if (selectedBranch) {
                    const branch = JSON.parse(selectedBranch);
                    console.log('🟢 Auto-selecting stored branch:', branch.BranchName || branch.branchName);
                    
                    // Set the branch as selected
                    setCurrentBranch(branch);
                    setCurrentBranchId(branch.BranchId || branch.branchID || branch.id || branch.branchId);
                    
                    // Clear vehicle and operator data when branch changes
                    setVehicleOptions([]);
                    setOperatorOptions([]);
                    setVehicleNo('');
                    setSelectedOperator('');
                    setSelectedOperatorId('');
                    
                    // Load new data for the selected branch
                    if (branch.BranchId || branch.branchID || branch.id || branch.branchId) {
                        loadVehicleNumbers(branch.BranchId || branch.branchID || branch.id || branch.branchId);
                        loadOperators();
                    }
                }
                break;
            case 'vehicleNo':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadVehicleNumbers(currentBranchId);
                break;
            case 'operator':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadOperators();
                break;
            default:
                setModalData([]);
                setFilteredModalData([]);
        }
    };

    const getLoadingMessage = (type) => {
        switch (type) {
            case 'branch':
                return 'Loading branches...';
            case 'vehicleNo':
                return 'Loading vehicles...';
            case 'operator':
                return 'Loading operators...';
            default:
                return 'Loading data...';
        }
    };

    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'branch':
                console.log('🟢 Setting branch:', item.BranchName || item.branchName, 'with ID:', item.BranchId || item.branchID);

                setCurrentBranch(item);
                setCurrentBranchId(item.BranchId || item.branchID);

                // Save to AsyncStorage for persistence
                AsyncStorage.setItem('selectedBranch', JSON.stringify(item));

                // Clear vehicle and operator data when branch changes
                setVehicleOptions([]);
                setOperatorOptions([]);
                setVehicleNo('');
                setSelectedOperator('');
                setSelectedOperatorId('');

                // Load new data for the selected branch
                if (item.BranchId || item.branchID) {
                    loadVehicleNumbers(item.BranchId || item.branchID);
                    loadOperators();
                }
                break;
            case 'vehicleNo':
                setVehicleNo(item.VehicleId);
                // Fetch vehicle's last reading
                fetchVehicleLastReading(item.VehicleId);
                break;
            case 'operator':
                // Handle new operator API response format (EmpName, EmpId) and old format (UserName, UserId)
                setSelectedOperator(item.EmpName || item.UserName);
                setSelectedOperatorId(item.EmpId || item.UserId);
                break;
            case 'tripList':
                // Handle trip selection from trip list
                console.log('🟢 Trip selected:', item.tripID);
                setModalVisible(false);
                // Fetch vehicle trip details
                fetchVehicleTripDetails(currentBranchId, item.tripID).then(details => {
                    if (details) {
                        console.log('✅ Vehicle trip details:', details);
                        Alert.alert(
                            'Trip Details',
                            `Trip ID: ${details.TripId || 'N/A'}\n` +
                            `Vehicle: ${details.vehicleNo || details.VehicleNo || 'N/A'}\n` +
                            `Start Reading: ${details.StartReading || '0'}\n` +
                            `End Reading: ${details.EndReading || '0'}\n` +
                            `Status: ${details.Closed ? 'Closed' : 'Open'}\n` +
                            `Remarks: ${details.remarks || details.Remarks || 'No remarks'}`,
                            [{ text: 'OK' }]
                        );
                    } else {
                        Alert.alert('Error', 'Failed to fetch trip details');
                    }
                });
                break;
        }
        setModalVisible(false);
    };

const addOperator = () => {
    if (!selectedOperator) {
        Alert.alert('Validation Error', 'Please select an Operator');
        return;
    }

    if (!vehicleNo) {
        Alert.alert('Validation Error', 'Please select a Vehicle Number');
        return;
    }

    if (!startReading) {
        Alert.alert('Validation Error', 'Please enter Start Reading');
        return;
    }

    // Check for duplicate vehicle+operator or duplicate operator or duplicate vehicle
    const duplicateVehicleOperator = operatorsList.find(
        op => op.vehicleNo === vehicleNo && op.operatorId === selectedOperatorId
    );
    if (duplicateVehicleOperator) {
        Alert.alert('Validation Error', 'This vehicle and operator combination is already added.');
        return;
    }

    const duplicateVehicle = operatorsList.find(
        op => op.vehicleNo === vehicleNo && op.operatorId !== selectedOperatorId
    );
    if (duplicateVehicle) {
        Alert.alert('Validation Error', 'This vehicle is already assigned to another operator.');
        return;
    }

    const duplicateOperator = operatorsList.find(
        op => op.operatorId === selectedOperatorId && op.vehicleNo !== vehicleNo
    );
    if (duplicateOperator) {
        Alert.alert('Validation Error', 'This operator is already assigned to another vehicle.');
        return;
    }

    const newOperator = {
        lineNumber: operatorsList.length + 1,
        operatorName: selectedOperator,
        operatorId: selectedOperatorId,
        vehicleNo: vehicleNo,
        startReading: startReading,
        endReading: endReading,
        remarks: remarks || ""
    };

    setOperatorsList([...operatorsList, newOperator]);

    setSelectedOperator('');
    setSelectedOperatorId('');
    setVehicleNo('');
    setStartReading('');
    setEndReading('');
    setRemarks('');
};

    const renderModalItem = (item, index) => {
        let displayText = '';
        let isDisabled = false;

        if (modalType === 'branch') {
            displayText = item.BranchName || item.branchName || 'Branch';
        } else if (modalType === 'operator' && (item.EmpName || item.UserName)) {
            displayText = item.EmpName || item.UserName;
        } else if (modalType === 'vehicleNo' && item.VehicleId) {
            displayText = item.VehicleId;
            // Check if vehicle is already in use
            const vehicleInUse = isVehicleInUse(item.VehicleId);
            isDisabled = Boolean(vehicleInUse);
        } else if (modalType === 'tripList' && item.tripID) {
            displayText = item.tripID;
        }

        return (
            <TouchableOpacity
                key={index}
                style={[
                    styles.modalItem,
                    isDisabled && styles.disabledModalItem
                ]}
                onPress={() => {
                    if (isDisabled) {
                        const vehicleInUse = isVehicleInUse(item.VehicleId);
                        Alert.alert(
                            'Vehicle Already in Use',
                            `Vehicle ${item.VehicleId} is already assigned to Trip ID: ${vehicleInUse.tripID}\n\n` +
                            `Vehicle: ${vehicleInUse.vehicleNo}\n` +
                            `Date: ${vehicleInUse.tripDate ? new Date(vehicleInUse.tripDate).toLocaleDateString() : 'N/A'}\n` +
                            `Please select a different vehicle.`,
                            [{ text: 'OK' }]
                        );
                    } else {
                        handleModalItemSelect(item);
                    }
                }}
                disabled={isDisabled}
            >
                <Text style={[
                    styles.modalItemText,
                    isDisabled && styles.disabledModalItemText
                ]}>
                    {displayText}
                    {isDisabled && ' (In Use)'}
                </Text>
            </TouchableOpacity>
        );
    };

    return (
        <KeyboardAvoidingView 
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
            <Navbar />

            <ScrollOptionsBar
                title="Trips"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <TouchableOpacity
                style={styles.branchHeader}
                onPress={() => openModal('branch')}
            >
                <Text style={styles.branchHeaderText}>
                    Branch: {getBranchName()} ({currentBranchId || 'No ID'})
                </Text>
                <Text style={styles.branchSubText}>
                    {currentBranch ? 'Branch auto-selected from storage' : 'Tap to select branch'}
                </Text>
            </TouchableOpacity>

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Entry</Text>

                    <View style={styles.formRow}>
                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Vehicle No."
                                placeholderTextColor="#888"
                                value={vehicleNo}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('vehicleNo')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Operator"
                                placeholderTextColor="#888"
                                value={selectedOperator}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('operator')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <View style={styles.readingInputContainer}>
                            <TextInput
                                style={[styles.readingInput, vehicleNo && { backgroundColor: '#f0f0f0' }]}
                                placeholder="Start Reading"
                                placeholderTextColor="#888"
                                value={startReading}
                                onChangeText={handleStartReadingChange}
                                keyboardType="decimal-pad"
                                editable={!vehicleNo}
                            />
                            {startReadingError ? (
                                <Text style={styles.errorText}>{startReadingError}</Text>
                            ) : null}
                        </View>

                        <View style={styles.readingInputContainer}>
                            <TextInput
                                style={[styles.readingInput, vehicleNo && { backgroundColor: '#f0f0f0' }]}
                                placeholder="End Reading (Optional)"
                                placeholderTextColor="#888"
                                value={endReading}
                                onChangeText={handleEndReadingChange}
                                onBlur={handleEndReadingBlur}
                                keyboardType="decimal-pad"
                                editable={!vehicleNo}
                            />
                            {endReadingError ? (
                                <Text style={styles.errorText}>{endReadingError}</Text>
                            ) : null}
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Add Remarks"
                            placeholderTextColor="#888"
                            multiline
                            numberOfLines={3}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                    </View>

                    <View style={styles.buttonRow}>
                        <TouchableOpacity style={styles.addButton} onPress={addOperator}>
                            <Text style={styles.addButtonText}>Add Entry</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearButton} onPress={handleClearTripDetails}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Entries</Text>

                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <View style={styles.checkboxCell}>
                                <CheckBox
                                    value={!!isSelectAllOperators}
                                    onValueChange={setIsSelectAllOperators}
                                    tintColors={{ true: 'white', false: 'white' }}
                                    style={styles.headerCheckbox}
                                />
                            </View>
                            <View style={styles.lineNumberCell}>
                                <Text style={styles.tableHeaderText}>#</Text>
                            </View>
                            <View style={styles.operatorNameCell}>
                                <Text style={styles.tableHeaderText}>Operator</Text>
                            </View>
                            <View style={styles.vehicleCell}>
                                <Text style={styles.tableHeaderText}>Vehicle</Text>
                            </View>
                            <View style={styles.readingCell}>
                                <Text style={styles.tableHeaderText}>Readings</Text>
                            </View>
                        </View>

                        <ScrollView style={styles.tableBody} contentContainerStyle={{ flexGrow: 1 }}>
                            {operatorsList.length > 0 ? (
                                operatorsList.map((operator, index) => (
                                    <View key={index} style={index % 2 === 0 ? styles.tableRow : styles.tableRowAlternate}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.lineNumberCell}>
                                            <Text style={styles.tableCell}>{operator.lineNumber}</Text>
                                        </View>
                                        <View style={styles.operatorNameCell}>
                                            <Text style={styles.tableCell}>{operator.operatorName}</Text>
                                        </View>
                                        <View style={styles.vehicleCell}>
                                            <Text style={styles.tableCell}>{operator.vehicleNo}</Text>
                                        </View>
                                        <View style={styles.readingCell}>
                                            <Text style={styles.tableCell}>
                                                {operator.startReading}{operator.endReading ? `-${operator.endReading}` : ''}
                                            </Text>
                                        </View>
                                    </View>
                                ))
                            ) : (
                                <View style={styles.emptyTableRow}>
                                    <Text style={styles.emptyTableText}>No Entries</Text>
                                </View>
                            )}
                        </ScrollView>
                    </View>

                    {operatorsList.length > 0 && (
                        <View style={styles.buttonRow}>
                            <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                                <Text style={styles.deleteButtonText}>Delete Selected</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </ScrollView>

            {loading && !modalVisible && (
                <CustomLoader
                    isVisible={loading}
                    message="Loading data..."
                    size="large"
                    color="#02096A"
                    backgroundColor="rgba(0, 0, 0, 0.5)"
                    containerStyle={styles.globalLoaderContainer}
                />
            )}

            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'branch' ? 'Stored Branch' :
                                modalType === 'vehicleNo' ? 'Select Vehicle No.' :
                                    modalType === 'operator' ? 'Select Operator' :
                                        modalType === 'tripList' ? 'Trip List' : 'Select Item'}
                        </Text>

                        {/* Date pickers for trip list modal */}
                        {modalType === 'tripList' && (
                            <View style={styles.datePickerContainer}>
                                <View style={styles.datePickerRow}>
                                    <TouchableOpacity 
                                        onPress={() => setShowFromPicker(true)} 
                                        style={styles.datePickerButton}
                                    >
                                        <Text style={styles.datePickerText}>
                                            From: {fromDate || 'Select Date'}
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity 
                                        onPress={() => setShowToPicker(true)} 
                                        style={styles.datePickerButton}
                                    >
                                        <Text style={styles.datePickerText}>
                                            To: {toDate || 'Select Date'}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <TouchableOpacity
                                    style={styles.fetchButton}
                                    onPress={() => {
                                        if (fromDate && toDate) {
                                            fetchTripList(currentBranchId, fromDate, toDate);
                                        } else {
                                            Alert.alert('Error', 'Please select both from and to dates');
                                        }
                                    }}
                                >
                                    <Text style={styles.fetchButtonText}>
                                        {tripListLoading ? 'Loading...' : 'Fetch Trips'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        )}

                        {/* Date pickers */}
                        {showFromPicker && (
                            <DateTimePicker
                                value={fromDate ? new Date(fromDate.split('-').reverse().join('-')) : new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => {
                                    setShowFromPicker(false);
                                    if (selectedDate) {
                                        const formattedDate = selectedDate.toLocaleDateString('en-GB');
                                        setFromDate(formattedDate);
                                    }
                                }}
                            />
                        )}
                        {showToPicker && (
                            <DateTimePicker
                                value={toDate ? new Date(toDate.split('-').reverse().join('-')) : new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => {
                                    setShowToPicker(false);
                                    if (selectedDate) {
                                        const formattedDate = selectedDate.toLocaleDateString('en-GB');
                                        setToDate(formattedDate);
                                    }
                                }}
                            />
                        )}

                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search..."
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>

                        <CustomLoader
                            isVisible={loading}
                            message={getLoadingMessage(modalType)}
                            size="large"
                            color="#02096A"
                            backgroundColor="rgba(255, 255, 255, 0.95)"
                        />

                        {!loading && (
                            <ScrollView>
                                <View style={styles.modalItemsContainer}>
                                    {modalType === 'tripList' ? (
                                        tripListLoading ? (
                                            <View style={styles.noDataContainer}>
                                                <Text style={styles.noDataText}>Loading trips...</Text>
                                            </View>
                                        ) : tripList.length > 0 ? (
                                            tripList.map((item, index) => renderModalItem(item, index))
                                        ) : (
                                            <View style={styles.noDataContainer}>
                                                <Text style={styles.noDataText}>No trips found</Text>
                                            </View>
                                        )
                                    ) : filteredModalData.length > 0 ? (
                                        filteredModalData.map((item, index) => renderModalItem(item, index))
                                    ) : (
                                        <View style={styles.noDataContainer}>
                                            <Text style={styles.noDataText}>No data available</Text>
                                        </View>
                                    )}
                                </View>
                            </ScrollView>
                        )}

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* View Trips Modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={viewModalVisible}
                onRequestClose={() => setViewModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={[styles.modalContent, { maxHeight: '90%' }]}>
                        <Text style={styles.modalTitle}>Today's Trips</Text>
                        
                        <ScrollView style={{ maxHeight: '80%' }}>
                            {tripsList.length > 0 ? (
                                tripsList.map((trip, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.tripItem}
                                        onPress={() => handleTripSelection(trip)}
                                    >
                                        <View style={styles.tripItemHeader}>
                                            <Text style={styles.tripId}>Trip ID: {trip.tripID}</Text>
                                            <Text style={styles.tripStatus}>
                                                {trip.closed ? '✓ Closed' : '○ Open'}
                                            </Text>
                                        </View>
                                        <View style={styles.tripItemDetails}>
                                            <Text style={styles.tripDetailText}>
                                                Vehicle: {trip.vehicleNo || 'N/A'}
                                            </Text>
                                            <Text style={styles.tripDetailText}>
                                                Date: {trip.tripDate ? new Date(trip.tripDate).toLocaleDateString() : 'N/A'}
                                            </Text>
                                            <Text style={styles.tripDetailText}>
                                                Reading: {trip.startReading} - {trip.endReading || 'N/A'}
                                            </Text>
                                            <Text style={styles.tripDetailText}>
                                                Remarks: {trip.remarks || 'No remarks'}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                ))
                            ) : (
                                <View style={styles.noDataContainer}>
                                    <Text style={styles.noDataText}>No trips found for today</Text>
                                </View>
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setViewModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {loadingTripDetails && (
                <CustomLoader
                    isVisible={loadingTripDetails}
                    message="Generating trip report..."
                    size="large"
                    color="#02096A"
                    backgroundColor="rgba(0, 0, 0, 0.5)"
                    containerStyle={styles.globalLoaderContainer}
                />
            )}
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    contentContainer: {
        flex: 1,
    },
    branchHeader: {
        backgroundColor: '#02096A',
        paddingVertical: 12,
        paddingHorizontal: 15,
        marginHorizontal: 8,
        marginVertical: 4,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#FDC500',
    },
    branchHeaderText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        textAlign: 'center',
    },
    branchSubText: {
        color: '#FDC500',
        fontSize: 12,
        fontFamily: 'Poppins',
        textAlign: 'center',
        marginTop: 2,
    },
    sectionContainer: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 18,
        marginHorizontal: 8,
        marginVertical: 6,
        borderWidth: 1,
        borderColor: '#e3f2fd',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 4,
    },
    sectionTitle: {
        fontSize: 26,
        fontWeight: '700',
        fontFamily: 'Poppins',
        letterSpacing: 1,
        marginBottom: 18,
        color: '#3f51b5',
        textAlign: 'center',
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 15,
    },
    inputWithButtonContainer: {
        flex: 1,
        flexDirection: 'row',
        marginRight: 10,
        marginBottom: 5,
    },
    inputWithButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 50,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: '700',
    },
    selectButton: {
        backgroundColor: '#041C44',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
    },
    selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    readingInput: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderRadius: 10,
        padding: 14,
        height: 52,
        fontFamily: 'Poppins',
        fontSize: 16,
        color: '#2c3e50',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        marginRight: 10,
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    readingInputContainer: {
        flex: 1,
        marginRight: 10,
    },
    errorText: {
        color: '#f44336',
        fontSize: 12,
        fontFamily: 'Poppins',
        fontWeight: '500',
        marginTop: 4,
        marginLeft: 4,
    },
    remarksInput: {
        flex: 1,
        backgroundColor: '#ffffff',
        borderRadius: 10,
        padding: 14,
        height: 85,
        textAlignVertical: 'top',
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#2c3e50',
        borderWidth: 2,
        borderColor: '#e3f2fd',
        fontWeight: '600',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 15,
        marginTop: 15,
    },
    addButton: {
        backgroundColor: '#4caf50',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 10,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ffd54f',
        height: 52,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
    },
    addButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 15,
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
    },
    clearButton: {
        backgroundColor: '#f44336',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 10,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ffd54f',
        height: 52,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.15,
        shadowRadius: 3,
        elevation: 4,
    },
    clearButtonText: {
        color: '#ffffff',
        fontWeight: '700',
        fontSize: 15,
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
    },
    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    tableContainer: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 18,
        marginTop: 18,
        borderWidth: 2,
        borderColor: '#e3f2fd',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 6,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#3f51b5',
        borderBottomWidth: 2,
        borderBottomColor: '#1a237e',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 50,
        alignItems: 'center',
    },
    tableHeaderText: {
        fontWeight: '700',
        fontSize: 13,
        color: '#ffffff',
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
        textTransform: 'uppercase',
        lineHeight: 16,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 2,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#e8eaf6',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 48,
        backgroundColor: '#ffffff',
        alignItems: 'center',
    },
    tableRowAlternate: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#e8eaf6',
        paddingVertical: 12,
        paddingHorizontal: 8,
        minHeight: 48,
        backgroundColor: '#f8f9fa',
        alignItems: 'center',
    },
    tableCell: {
        fontSize: 13,
        color: '#2c3e50',
        fontFamily: 'Poppins',
        fontWeight: '600',
        lineHeight: 18,
        paddingVertical: 2,
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    lineNumberCell: {
        width: 50,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    operatorNameCell: {
        flex: 2,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    vehicleCell: {
        flex: 1.5,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    readingCell: {
        flex: 1.5,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    emptyTableRow: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 0,
        borderRadius: 8,
        margin: 10,
    },
    emptyTableText: {
        color: '#9e9e9e',
        fontSize: 18,
        fontWeight: '600',
        fontFamily: 'Poppins',
        fontStyle: 'italic',
        textAlign: 'center',
        letterSpacing: 0.5,
    },

    // Modal styles with white background and yellow boxes
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        textAlign: 'center',
        color: '#02096A',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    noDataContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 200,
        width: '100%',
    },
    noDataText: {
        fontSize: 16,
        color: '#999',
        fontFamily: 'Poppins',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    globalLoaderContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    tableBody: {
        height: 200, // or any fixed height you want
    },
    
    // Trip view styles
    tripItem: {
        backgroundColor: '#f8f8f8',
        borderRadius: 10,
        marginBottom: 10,
        padding: 15,
        borderWidth: 1,
        borderColor: '#ddd',
        elevation: 2,
    },
    tripItemHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 10,
    },
    tripId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#02096A',
        fontFamily: 'Poppins',
    },
    tripStatus: {
        fontSize: 14,
        color: '#666',
        fontFamily: 'Poppins',
    },
    tripItemDetails: {
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        paddingTop: 10,
    },
    tripDetailText: {
        fontSize: 14,
        color: '#555',
        marginBottom: 5,
        fontFamily: 'Poppins',
    },
    
    // Disabled modal item styles
    disabledModalItem: {
        backgroundColor: '#cccccc',
        opacity: 0.6,
    },
    disabledModalItemText: {
        color: '#666666',
    },
    
    // Date picker styles
    datePickerContainer: {
        marginBottom: 15,
    },
    datePickerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    datePickerButton: {
        backgroundColor: '#f5f5f5',
        borderRadius: 8,
        padding: 10,
        borderWidth: 1,
        borderColor: '#ddd',
        flex: 1,
        marginHorizontal: 5,
        alignItems: 'center',
    },
    datePickerText: {
        color: '#333',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    fetchButton: {
        backgroundColor: '#041C44',
        borderRadius: 8,
        padding: 12,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    fetchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
});

export default TripRoaster;