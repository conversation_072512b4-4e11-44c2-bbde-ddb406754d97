import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import Navbar from '../../components/Navbar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBranchDetails } from '../../apiHandling/ReportAPI/fetchBillReportsAPI';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';

const BillReportPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { posTerminal, billData, fromDate, toDate } = route.params;

  const [businessDate, setBusinessDate] = useState(null);
  const [branchName, setBranchName] = useState('');
  const [areaName, setAreaName] = useState('');
  const [pincode, setPincode] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPageData();
  }, []);

  const fetchPageData = async () => {
    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      const branchDetails = await fetchBranchDetails(loginBranchID, bearerToken);
      const dateStr = await fetchBusinessDay(bearerToken, loginBranchID);

      // Parse date from DD-MM-YYYY format
      const parsedDate = dateStr ? new Date(dateStr.split('-').reverse().join('-')) : null;

      setBranchName(branchDetails.BranchName || '');
      setAreaName(branchDetails.AreaName || '');
      setPincode(branchDetails.PINCODE || '');
      setBusinessDate(parsedDate);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching report metadata:', error);
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return '';
    const options = { day: '2-digit', month: 'short', year: '2-digit' };
    return date.toLocaleDateString('en-GB', options);
  };

  const formatDateTime = (date) => {
    if (!date) return '';
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };
    return date.toLocaleDateString('en-GB', options).replace(',', '');
  };

  if (loading || !businessDate) {
    return (
      <View style={styles.container}>
        <Navbar />
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </View>
    );
  }

  // Sort bills by ISO_Number
  const sortedBills = [...billData].sort((a, b) => {
    const aIso = (a.ISO_Number || '').toString();
    const bIso = (b.ISO_Number || '').toString();
    return aIso.localeCompare(bIso);
  });

  let posTotal = 0.0;
  const paymodeSummary = {};
  const paymodeCount = {};

  sortedBills.forEach(bill => {
    const amt = parseFloat(bill.Amount || 0);
    const mode = bill.Mode || 'Unknown';

    posTotal += amt;
    paymodeSummary[mode] = (paymodeSummary[mode] || 0) + amt;
    paymodeCount[mode] = (paymodeCount[mode] || 0) + 1;
  });

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <Text style={styles.title}>Billwise Report</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Company Header */}
        <Text style={styles.companyName}>ABIS Exports India Pvt Ltd</Text>
        <Text style={styles.branchInfo}>{branchName}</Text>
        <Text style={styles.branchInfo}>
          {areaName}{pincode ? `, ${pincode}` : ''}
        </Text>

        <View style={styles.divider} />

        <Text style={styles.reportTitle}>Billwise Report</Text>

        <View style={styles.divider} />

        <Text style={styles.dateInfo}>
          Business Date: {formatDate(businessDate)}      POS# {posTerminal === 0 ? "ALL" : posTerminal}
        </Text>
        <Text style={styles.dateInfo}>Print on: {formatDateTime(new Date())}</Text>

        <View style={styles.thinDivider} />

        {/* Table Header */}
        <View style={styles.tableHeaderRow}>
          <Text style={[styles.tableHeaderText, { flex: 1 }]}>Sr.No</Text>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>Trans</Text>
          <Text style={[styles.tableHeaderText, { flex: 3 }]}>BillNo.</Text>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>Amount</Text>
          <Text style={[styles.tableHeaderText, { flex: 3 }]}>Paymode</Text>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>Channel</Text>
        </View>

        <View style={styles.thinDivider} />

        {/* Bills List */}
        {sortedBills.map((bill, index) => {
          const isoNumber = (bill.ISO_Number || '').toString();
          const last4Digits = isoNumber.length >= 4
            ? isoNumber.substring(isoNumber.length - 4)
            : isoNumber;

          return (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableText, { flex: 1 }]}>
                {(index + 1).toString().padStart(3, '0')}
              </Text>
              <Text style={[styles.tableText, { flex: 2 }]}>
                {bill.TransTypeID || ''}
              </Text>
              <Text style={[styles.tableText, { flex: 3 }]}>
                {last4Digits}
              </Text>
              <Text style={[styles.tableText, { flex: 2 }]}>
                {bill.Amount || ''}
              </Text>
              <Text style={[styles.tableText, { flex: 3 }]}>
                {bill.Mode || ''}
              </Text>
              <Text style={[styles.tableText, { flex: 2 }]}>
                {bill.ChannelID || ''}
              </Text>
            </View>
          );
        })}

        <View style={styles.thinDivider} />

        <Text style={styles.totalText}>
          POS Total........Rs.{posTotal.toFixed(2)}
        </Text>

        <View style={styles.spacing} />

        <Text style={styles.summaryTitle}>Summary by Pay mode :</Text>

        {/* Summary Header */}
        <View style={styles.summaryHeaderRow}>
          <Text style={[styles.summaryHeaderText, { flex: 3 }]}>Paymode</Text>
          <Text style={[styles.summaryHeaderText, { flex: 2 }]}>BillCount</Text>
          <Text style={[styles.summaryHeaderText, { flex: 3 }]}>Amount</Text>
        </View>

        {/* Summary Data */}
        {Object.keys(paymodeSummary).map(mode => (
          <View key={mode} style={styles.summaryRow}>
            <Text style={[styles.summaryText, { flex: 3 }]}>{mode}</Text>
            <Text style={[styles.summaryText, { flex: 2 }]}>
              {paymodeCount[mode].toString().padStart(4, '0')}
            </Text>
            <Text style={[styles.summaryText, { flex: 3 }]}>
              {paymodeSummary[mode].toFixed(2)}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6E6E6',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  companyName: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  branchInfo: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 2,
  },
  divider: {
    height: 2,
    backgroundColor: '#000',
    marginVertical: 8,
  },
  thinDivider: {
    height: 1,
    backgroundColor: '#000',
    marginVertical: 4,
  },
  reportTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  dateInfo: {
    fontSize: 14,
    marginBottom: 2,
  },
  tableHeaderRow: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  tableHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 2,
  },
  tableText: {
    fontSize: 14,
  },
  totalText: {
    fontSize: 14,
    marginVertical: 4,
  },
  spacing: {
    height: 10,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryHeaderRow: {
    flexDirection: 'row',
    paddingVertical: 2,
  },
  summaryHeaderText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  summaryRow: {
    flexDirection: 'row',
    paddingVertical: 2,
  },
  summaryText: {
    fontSize: 14,
  },
});

export default BillReportPage;
