import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import Navbar from '../../components/Navbar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBranchDetails } from '../../apiHandling/ReportAPI/fetchBillReportsAPI';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';

const ItemReportPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { itemData, fromDate, toDate } = route.params;

  const [businessDate, setBusinessDate] = useState(null);
  const [branchName, setBranchName] = useState('');
  const [areaName, setAreaName] = useState('');
  const [pincode, setPincode] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPageData();
  }, []);

  const fetchPageData = async () => {
    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      const branchDetails = await fetchBranchDetails(loginBranchID, bearerToken);
      const dateStr = await fetchBusinessDay(bearerToken, loginBranchID);

      // Parse date from DD-MM-YYYY format
      const parsedDate = dateStr ? new Date(dateStr.split('-').reverse().join('-')) : null;

      setBranchName(branchDetails.BranchName || '');
      setAreaName(branchDetails.AreaName || '');
      setPincode(branchDetails.PINCODE || '');
      setBusinessDate(parsedDate);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching report metadata:', error);
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return '';
    const options = { day: '2-digit', month: 'short', year: '2-digit' };
    return date.toLocaleDateString('en-GB', options);
  };

  const formatDateTime = (date) => {
    if (!date) return '';
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };
    return date.toLocaleDateString('en-GB', options).replace(',', '');
  };

  if (loading || !businessDate) {
    return (
      <View style={styles.container}>
        <Navbar />
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </View>
    );
  }

  const printDate = formatDateTime(new Date());

  // Group data by ItemFamilyName
  const groupedData = {};
  let posTotal = 0.0;
  let deliveryTotal = 0.0;

  itemData.forEach(item => {
    const family = item.ItemFamilyName || 'Unknown';
    if (!groupedData[family]) {
      groupedData[family] = [];
    }
    groupedData[family].push(item);
    posTotal += parseFloat(item.Amount || 0);
    deliveryTotal += parseFloat(item.DeliveryCharge || 0);
  });

  const grandTotal = posTotal + deliveryTotal;

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <Text style={styles.title}>Item Wise Billing Report</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Company Header */}
        <Text style={styles.companyName}>ABIS Exports India Pvt Ltd</Text>
        <Text style={styles.branchInfo}>{branchName}</Text>
        <Text style={styles.branchInfo}>
          {areaName}{pincode ? `, ${pincode}` : ''}
        </Text>

        <View style={styles.divider} />

        <Text style={styles.reportTitle}>Item Wise Billing Report</Text>

        <View style={styles.divider} />

        <Text style={styles.dateInfo}>Business Date: {formatDate(businessDate)}</Text>
        <Text style={styles.dateInfo}>Print on: {printDate}</Text>

        <View style={styles.thinDivider} />

        <Text style={styles.tableHeader}>
          ItemGroup/SaleType            Qty         DayRate         TotalAmount
        </Text>

        <View style={styles.thinDivider} />

        {/* Grouped Items */}
        {Object.entries(groupedData).map(([family, items]) => {
          let subQty = 0;
          let subAmount = 0;

          return (
            <View key={family} style={styles.groupContainer}>
              <Text style={styles.groupTitle}>
                {family} - {items[0]?.TranSubTypeID || ''}
              </Text>
              <View style={styles.thinDivider} />

              {items.map((item, index) => {
                const nos = parseFloat(item.Nos || 0);
                const kgs = parseFloat(item.Kgs || 0);
                const rate = parseFloat(item.Rate || 0);
                const amt = parseFloat(item.Amount || 0);

                const displayQty = kgs > 0 ? kgs : (nos > 0 ? nos : 0.0);
                subQty += displayQty;
                subAmount += amt;

                return (
                  <View key={index} style={styles.itemRow}>
                    <Text style={styles.itemDetail}>
                      {item.itemName || item.ItemName || ''} - {item.TranSubTypeID || ''}
                    </Text>
                    <Text style={styles.itemNumbers}>
                      {displayQty.toFixed(3).padStart(10)}  {rate.toFixed(2).padStart(13)}  {amt.toFixed(2).padStart(17)}
                    </Text>
                  </View>
                );
              })}

              <View style={styles.thinDivider} />

              <View style={styles.subtotalRow}>
                <Text style={styles.subtotalText}>
                  Sub Total                    {subQty.toFixed(3).padStart(10)}               {subAmount.toFixed(2).padStart(17)}
                </Text>
              </View>

              <View style={styles.thinDivider} />
            </View>
          );
        })}

        {/* Totals */}
        <View style={styles.totalsContainer}>
          <Text style={styles.totalText}>POS Total: Rs. {posTotal.toFixed(2)}</Text>
          <Text style={styles.totalText}>Delivery charges: Rs. {deliveryTotal.toFixed(2)}</Text>
          <Text style={styles.grandTotalText}>Grand total: Rs. {grandTotal.toFixed(2)}</Text>
        </View>

        <View style={styles.thinDivider} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6E6E6',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 12,
    backgroundColor: 'white',
  },
  companyName: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  branchInfo: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 2,
  },
  divider: {
    height: 2,
    backgroundColor: '#000',
    marginVertical: 8,
  },
  thinDivider: {
    height: 1,
    backgroundColor: '#000',
    marginVertical: 4,
  },
  reportTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  dateInfo: {
    fontSize: 14,
    marginBottom: 2,
  },
  tableHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'monospace',
    paddingVertical: 4,
  },
  groupContainer: {
    marginVertical: 4,
  },
  groupTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  itemRow: {
    marginLeft: 12,
    marginBottom: 4,
  },
  itemDetail: {
    fontSize: 14,
    lineHeight: 20,
  },
  itemNumbers: {
    fontSize: 14,
    fontFamily: 'monospace',
    lineHeight: 20,
  },
  subtotalRow: {
    marginLeft: 12,
    marginBottom: 8,
  },
  subtotalText: {
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  totalsContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  totalText: {
    fontSize: 14,
    marginBottom: 2,
  },
  grandTotalText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
});

export default ItemReportPage;
