import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    StyleSheet,
    Modal,
    TouchableOpacity,
    ActivityIndicator,
    FlatList,
    ScrollView,
    Alert,
    Dimensions,
    Platform
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { DataTable } from 'react-native-paper';

// Import API functions from receiving screen
import { fetchBranchList } from '../../apiHandling/StockAPI/fetchBranchListAPI';
import { fetchBranchDetails } from '../../apiHandling/StockAPI/fetchBranchDetailsAPI';
import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail, fetchItemStock } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { saveIndent, fetchBusinessDate } from '../../apiHandling/StockAPI/saveIndentAPI';
import { fetchIndentList } from '../../apiHandling/StockAPI/fetchIndentListAPI';

const Indent = () => {
    const navigation = useNavigation();

    // Basic form states
    const [selectedTransferType, setSelectedTransferType] = useState(null);
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [nos, setNos] = useState('');
    const [kgs, setKgs] = useState('');
    const [rate, setRate] = useState('');
    const [remarks, setRemarks] = useState('');

    // Location states (following receiving screen pattern)
    const [location, setLocation] = useState('');
    const [selectedBranchId, setSelectedBranchId] = useState('');
    const [loginBranchId, setLoginBranchId] = useState('');
    const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);
    const [branchList, setBranchList] = useState([]);
    const [filteredBranchList, setFilteredBranchList] = useState([]);
    const [branchSearchText, setBranchSearchText] = useState('');
    const [loadingBranches, setLoadingBranches] = useState(false);

    // Item states (following receiving screen pattern)
    const [itemList, setItemList] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [itemDetail, setItemDetail] = useState(null);

    // Item properties (following receiving screen pattern)
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);

    // Stock quantities (following stock take pattern)
    const [stockQty, setStockQty] = useState(0);
    const [stockAltQty, setStockAltQty] = useState(0);

    // Table states (following receiving screen pattern)
    const [tableData, setTableData] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [editingItemIndex, setEditingItemIndex] = useState(null);

    // View modal states
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [indentList, setIndentList] = useState([]);
    const [filteredIndents, setFilteredIndents] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);
    const [loading, setLoading] = useState(false);


    // Load item list when screen mounts (following receiving screen pattern)
    useEffect(() => {
        loadItemList();
    }, []);

    const loadItemList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        setLoginBranchId(loginBranchID); // Set login branch ID for filtering
        const data = await fetchItemList(bearerToken, loginBranchID);
        setItemList(data);
    };

    // Handle item selection (following receiving screen pattern)
    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);
        setSearchText(''); // Clear search when item is added

        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const detail = await fetchItemDetail(bearerToken, item.ItemID);
        setItemDetail(detail);

        if (!detail) {
            // Default assumption: sell by weight is false, alt qty is false
            setSellByWeight(false);
            setAltQtyEnabled(false);
            setNos('0');
            setKgs('0');
            return;
        }

        const { SellByWeight, AltQtyEnabled } = detail;

        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);

        // Fetch stock data for the selected item (following stock take pattern)
        const stockData = await fetchItemStock(bearerToken, loginBranchID, item.ItemID);
        console.log("Stock Data:", stockData);

        // Set stock quantities (following stock take pattern)
        if (Array.isArray(stockData) && stockData.length > 0) {
            const stock = stockData[0];
            const qty = stock.StockQty || 0;
            const altQty = stock.StockAltQty || 0;
            setStockQty(qty);
            setStockAltQty(altQty);
        } else {
            // No stock data available
            setStockQty(0);
            setStockAltQty(0);
        }

        // Set fields based on flags (following receiving screen pattern)
        if (SellByWeight && AltQtyEnabled) {
            setKgs('');
            setNos('');
        } else if (SellByWeight) {
            setKgs('');
            setNos('0');
        } else {
            setNos('');
            setKgs('0');
        }
    };

    // Handle item select button press (following receiving screen pattern)
    const handleItemSelectPress = () => {
        setModalVisible(true);
        if (itemList.length === 0) {
            loadItemList();
        }
    };


    // Function to load branch list (following receiving screen pattern)
    const loadBranchList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            setLoadingBranches(true);
            const branches = await fetchBranchList(bearerToken);

            // Filter out the login branch from the list
            const filteredBranches = branches.filter(branch => branch.branchID !== loginBranchId);

            setBranchList(filteredBranches);
            setFilteredBranchList(filteredBranches);
        } catch (error) {
            console.error('Error loading branch list:', error);
            Alert.alert('Error', 'Failed to load branch list');
        } finally {
            setLoadingBranches(false);
        }
    };

    // Filter branches based on search text
    useEffect(() => {
        if (branchSearchText.trim() === '') {
            setFilteredBranchList(branchList);
        } else {
            const filtered = branchList.filter(branch =>
                branch.branchName.toLowerCase().includes(branchSearchText.toLowerCase()) ||
                branch.branchID.toLowerCase().includes(branchSearchText.toLowerCase())
            );
            setFilteredBranchList(filtered);
        }
    }, [branchSearchText, branchList]);

    // Function to handle location select button press (following receiving screen pattern)
    const handleLocationSelectPress = () => {
        setIsLocationModalVisible(true);
        if (branchList.length === 0) {
            loadBranchList();
        }
    };

    // Function to handle branch selection (following receiving screen pattern)
    const handleBranchSelection = async (branch) => {
        setSelectedBranchId(branch.branchID);
        setLocation(`${branch.branchID} - ${branch.branchName}`);
        setIsLocationModalVisible(false);
        setBranchSearchText(''); // Reset search when selecting branch

        // Load related data when branch is selected
        await loadBranchDetails(branch.branchID);
    };

    // Function to load branch details (addresses) (following receiving screen pattern)
    const loadBranchDetails = async (branchId) => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            const addresses = await fetchBranchDetails(bearerToken, branchId);
            // Handle addresses if needed for indent
        } catch (error) {
            console.error('Error loading branch details:', error);
            Alert.alert('Error', 'Failed to load branch addresses');
        }
    };

    // Function to close location modal (following receiving screen pattern)
    const closeLocationModal = () => {
        setIsLocationModalVisible(false);
        setBranchSearchText(''); // Reset search when closing modal
    };

    // Reset and reload function (following receiving screen pattern)
    const handleResetAndReload = () => {
        // Reset all form states
        setSelectedTransferType('');

        // Reset location details
        setLocation('');
        setSelectedBranchId('');
        setBranchSearchText('');

        // Reset item details
        handleClearFields();

        // Reset table data
        setTableData([]);
        setSelectedRows([]);
        setEditingItemIndex(null);

        // Reset modal states
        setModalVisible(false);
        setIsLocationModalVisible(false);

        // Reset lists
        setBranchList([]);
        setFilteredBranchList([]);

        // Reload item list
        loadItemList();
    };


    // Add item to table (following receiving screen pattern)
    const handleAddItem = () => {
        // Validation
        if (!itemName || !itemId) {
            Alert.alert('Error', 'Please select an item');
            return;
        }

        if (!nos && !kgs) {
            Alert.alert('Error', 'Please enter quantity (Nos or Kgs)');
            return;
        }

        if (editingItemIndex !== null) {
            // Update existing item
            const updatedTableData = [...tableData];
            updatedTableData[editingItemIndex] = {
                ...updatedTableData[editingItemIndex],
                itemId: itemId,
                itemName: itemName,
                nos: nos || '0',
                kgs: kgs || '0',
                rate: rate || '0',
                remarks: remarks || '',
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || '',
                currentStockQty: stockQty,
                currentStockAltQty: stockAltQty
            };
            setTableData(updatedTableData);
            setEditingItemIndex(null);
        } else {
            // Check for duplicate items
            const existingItem = tableData.find(item =>
                item.itemId === itemId
            );

            if (existingItem) {
                Alert.alert('Error', 'Item already exists in the table');
                return;
            }

            const newItem = {
                id: Date.now().toString(),
                lineNo: String(tableData.length + 1).padStart(3, '0'),
                itemId: itemId,
                itemName: itemName,
                batch: '',
                nos: nos || '0',
                kgs: kgs || '0',
                rate: rate || '0',
                remarks: remarks || '',
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || '',
                currentStockQty: stockQty,
                currentStockAltQty: stockAltQty
            };

            setTableData(prev => [...prev, newItem]);
        }

        handleClearFields(); // Reset form after add
    };

    const handleClearFields = () => {
        setItemName('');
        setItemId('');
        setNos('');
        setKgs('');
        setRate('');
        setRemarks('');
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setItemDetail(null);
        setStockQty(0);
        setStockAltQty(0);
        setEditingItemIndex(null);
    };

    // Function to handle item row click for editing (following billing screen pattern)
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        setItemName(item.itemName);
        setItemId(item.itemId);
        setNos(item.nos);
        setKgs(item.kgs);
        setRate(item.rate);
        setRemarks(item.remarks || '');
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);
        setStockQty(item.currentStockQty);
        setStockAltQty(item.currentStockAltQty);

        // Set item detail if available
        if (item.itemFamilyID || item.stockGroupId) {
            setItemDetail({
                ItemFamilyID: item.itemFamilyID,
                StockGroupId: item.stockGroupId,
                SellByWeight: item.sellByWeight,
                AltQtyEnabled: item.altQtyEnabled
            });
        }
    };

    const deleteSelectedItems = () => {
        const filtered = tableData.filter(item => !selectedRows.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setTableData(reIndexed);
        setSelectedRows([]);
    };

    // Helper function to get IST date time (following stock take pattern)
    const getISTISOString = () => {
        const now = new Date();
        // Offset IST (+5:30) in milliseconds
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);
        // Format as ISO string (will still end in Z, but value is IST)
        return istTime.toISOString(); // Includes milliseconds and Z
    };

    // Save functionality (following receiving screen pattern)
    const handleSave = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const loginBranchName = parsedBranch.BranchName;
        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
        const loginUserID = user.userId;

        try {
            // Validation
            if (!selectedTransferType) {
                Alert.alert('Error', 'Please select location type');
                return;
            }
            if (!selectedBranchId) {
                Alert.alert('Error', 'Please select location');
                return;
            }
            if (tableData.length === 0) {
                Alert.alert('Error', 'Please add at least one item');
                return;
            }

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = getISTISOString();

            // Get selected branch name from location string
            const toBranchName = location.split(' - ')[1] || '';

            const payload = {
                indentId: "",
                branchId: loginBranchID,
                indentType: "IB ST",
                toBranchType: "ST",
                toBranchId: selectedBranchId,
                businessDate: businessDate,
                remarks: "",
                isO_Number: "",
                deleted: "N",
                branchName: loginBranchName,
                toBranchName: toBranchName,
                posted: true,
                createdUserID: loginUserID,
                createdDate: systemDate,
                modifiedUserID: "",
                modifiedDate: "1753-01-01T00:00:00.000Z",
                deletedUserID: "",
                deletedDate: "1753-01-01T00:00:00.000Z",
                postedUserID: "",
                postedDate: "1753-01-01T00:00:00.000Z",
                piDetails: tableData.map((item) => ({
                    lineNumber: item.lineNo,
                    itemName: item.itemName,
                    nos: item.nos,
                    kgs: item.kgs,
                    rate: parseFloat(item.rate) || 0,
                    remarks: item.remarks,
                    deleted: "N",
                    itemID: item.itemId,
                    currentStockQty: item.currentStockQty || 0,
                    currentStockAltQty: item.currentStockAltQty || 0,
                    createdUserId: loginUserID,
                    createdDate: systemDate,
                    modifiedUserId: "",
                    modifiedDate: "1753-01-01T00:00:00.000Z",
                    deletedUserId: "",
                    deletedDate: "1753-01-01T00:00:00.000Z",
                    altQtyEnabled: item.altQtyEnabled,
                    sellByWeight: item.sellByWeight,
                    dml: "I",
                    qty: parseFloat(item.nos) || parseFloat(item.kgs) || 0,
                    altQty: 0,
                    uomid: item.sellByWeight ? "KG" : "NOS",
                    isOtherUOMGR: false
                }))
            };

            const result = await saveIndent(bearerToken, payload);

            if (result.result === 1) {
                // Show success dialog with print option (following billing screen pattern)
                Alert.alert(
                    'Success',
                    `Indent saved successfully\nIndent ID: ${result.description}\n\nDo you want to print the indent?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                handleResetAndReload();
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                fetchLatestIndentAndNavigate(result.description);
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Save Failed', result.description || 'Unexpected response from server');
            }

        } catch (error) {
            console.error('Save error:', error);
            Alert.alert('Error', 'Failed to save indent data');
        }
    };

    // Function to fetch latest indent and navigate to view screen (following billing screen pattern)
    const fetchLatestIndentAndNavigate = async (indentId) => {
        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Navigate to ViewIndentScreen with the saved indent details
            navigation.navigate('ViewIndentScreen', {
                indentId: indentId,
                branchId: loginBranchID,
                withBranchType: 'ST'
            });
        } catch (error) {
            console.error('Error navigating to view indent:', error);
            Alert.alert('Error', 'Failed to navigate to view indent');
            handleResetAndReload();
        }
    };

    // View modal functions
    const openViewModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            // Set default dates
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const nextDay = new Date(businessDateObj);
            nextDay.setDate(nextDay.getDate() + 1);

            setFromDate(businessDateObj);
            setToDate(nextDay);
            setSearchQuery('');
            setViewModalVisible(true);

            // Load indent list with default dates
            await loadIndentList(loginBranchID, businessDateObj, nextDay);
        } catch (err) {
            console.error('Error opening view modal:', err);
            Alert.alert('Error', 'Failed to load view modal');
        }
        setLoading(false);
    };

    const loadIndentList = async (branchId = null, fromDateParam = null, toDateParam = null) => {
        if (!branchId && !fromDate && !toDate) return;

        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = branchId || parsedBranch.BranchId;

            const fromDateToUse = fromDateParam || fromDate;
            const toDateToUse = toDateParam || toDate;

            const fromDateStr = fromDateToUse.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = toDateToUse.toISOString().split('T')[0].replace(/-/g, '');

            const indents = await fetchIndentList(bearerToken, loginBranchID, fromDateStr, toDateStr);

            // Sort by IndentID
            const sortedIndents = indents.sort((a, b) => {
                return a.IndentID.localeCompare(b.IndentID);
            });

            setIndentList(sortedIndents);
            setFilteredIndents(sortedIndents);
        } catch (err) {
            console.error('Error loading indent list:', err);
            Alert.alert('Error', 'Failed to load indent list');
        }
        setLoading(false);
    };

    const handleIndentSelect = (indent) => {
        setViewModalVisible(false);
        navigation.navigate('ViewIndentScreen', {
            indentId: indent.IndentID,
            branchId: indent.BranchId,
            withBranchType: indent.ToBranchType || 'ST'
        });
    };

    const handleDateChange = (event, selectedDate, isFromDate) => {
        if (isFromDate) {
            setShowFromDatePicker(false);
            if (selectedDate) {
                setFromDate(selectedDate);
            }
        } else {
            setShowToDatePicker(false);
            if (selectedDate) {
                setToDate(selectedDate);
            }
        }
    };

    // Load indent list when dates change
    useEffect(() => {
        if (viewModalVisible && fromDate && toDate) {
            loadIndentList();
        }
    }, [fromDate, toDate]);

    // Filter indents when search query changes
    useEffect(() => {
        if (!searchQuery) {
            setFilteredIndents(indentList);
        } else {
            const filtered = indentList.filter(indent =>
                indent.IndentID.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (indent.ISO_Number && indent.ISO_Number.toLowerCase().includes(searchQuery.toLowerCase()))
            );
            setFilteredIndents(filtered);
        }
    }, [searchQuery, indentList]);

    return (
        <View style={styles.container}>
            <Navbar />
            <View style={styles.headerRow}>
                <Text style={styles.headerTitle}>INDENT</Text>
                <View style={styles.actionButtons}>
                    <TouchableOpacity style={styles.newBtn} onPress={handleResetAndReload}>
                        <Icon name="user-plus" size={16} color="#000" />
                        <Text style={styles.actionText}> New</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.viewBtn} onPress={openViewModal}>
                        <Icon name="eye" size={16} color="#000" />
                        <Text style={styles.actionText}> View</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[
                            styles.saveBtn,
                            { backgroundColor: (!selectedTransferType || !selectedBranchId || tableData.length === 0) ? '#ccc' : '#28A745' }
                        ]}
                        onPress={handleSave}
                        disabled={!selectedTransferType || !selectedBranchId || tableData.length === 0}
                    >
                        <Text style={styles.saveText}>Save</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.cancelBtn} onPress={handleResetAndReload}>
                        <Text style={styles.cancelText}>Cancel</Text>
                    </TouchableOpacity>
                </View>
            </View>


            {/* Location Section */}
            <View style={[styles.section, { opacity: tableData.length > 0 ? 0.5 : 1 }]}>
                <Text style={styles.sectionTitle}>Location Details</Text>
                <View style={styles.row}>
                    <Dropdown
                        data={[
                            { label: 'HO', value: 'HO' },
                            { label: 'IB ST', value: 'IB ST' },
                            { label: 'LOCAL VENDOR', value: 'LOCAL VENDOR' },
                            { label: 'GROUP COMPANY', value: 'GROUP COMPANY' },
                        ]}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Location Type"
                        value={selectedTransferType}
                        onChange={(item) => setSelectedTransferType(item.value)}
                        style={styles.indentDetails_dropdown}
                        disable={tableData.length > 0}
                    />

                    <View style={styles.rightInputContainer}>
                        <TextInput
                            placeholder="Choose Location"
                            value={location}
                            editable={false}
                            style={styles.input}
                        />
                        <TouchableOpacity
                            onPress={handleLocationSelectPress}
                            style={[styles.selectBtn, { height: 50 }]}
                            disabled={tableData.length > 0}
                        >
                            <Text style={styles.selectBtnText}>Select</Text>
                        </TouchableOpacity>
                    </View>

                </View>

                {/* Location Selection Modal (following receiving screen pattern) */}
                <Modal
                    visible={isLocationModalVisible}
                    transparent={true}
                    animationType="slide"
                    onRequestClose={closeLocationModal}
                >
                    <View style={styles.modalOverlay}>
                        <View style={styles.modalContainer}>
                            <View style={styles.modalHeader}>
                                <Text style={styles.modalTitle}>Select Location</Text>
                                <TouchableOpacity
                                    style={styles.modalCloseButton}
                                    onPress={closeLocationModal}
                                >
                                    <Text style={styles.modalCloseButtonText}>×</Text>
                                </TouchableOpacity>
                            </View>

                            {/* Search Input */}
                            <TextInput
                                style={styles.ModalSearchinput}
                                placeholder="Search branches..."
                                value={branchSearchText}
                                onChangeText={setBranchSearchText}
                            />

                            {loadingBranches ? (
                                <View style={styles.modalLoadingContainer}>
                                    <Text style={styles.modalLoadingText}>Loading branches...</Text>
                                </View>
                            ) : (
                                <FlatList
                                    data={filteredBranchList}
                                    keyExtractor={(item) => item.branchID}
                                    renderItem={({ item }) => (
                                        <TouchableOpacity
                                            style={styles.modalBranchItem}
                                            onPress={() => handleBranchSelection(item)}
                                        >
                                            <Text style={styles.modalBranchId}>{item.branchID}</Text>
                                            <Text style={styles.modalBranchName}>{item.branchName}</Text>
                                        </TouchableOpacity>
                                    )}
                                    style={styles.modalBranchList}
                                    ListEmptyComponent={
                                        <Text style={styles.noResultsText}>No branches found</Text>
                                    }
                                />
                            )}
                        </View>
                    </View>
                </Modal>


            </View>

            {/* Item Details Section */}
            <View style={[styles.section, { opacity: (!selectedTransferType || !selectedBranchId) ? 0.5 : 1 }]}>
                <Text style={styles.sectionTitle}>Item Details</Text>
                <View style={styles.itemRow}>
                    <TextInput
                        placeholder="Item Name"
                        style={styles.searchInputInline}
                        value={itemName}
                        onChangeText={setItemName}
                        editable={Boolean(selectedTransferType && selectedBranchId)}
                    />
                    <TouchableOpacity
                        style={[styles.searchBtn, { height: 50 }]}
                        onPress={handleItemSelectPress}
                        disabled={!selectedTransferType || !selectedBranchId}
                    >
                        <Text style={styles.searchBtnText}>Search Item</Text>
                    </TouchableOpacity>
                </View>

                {/* Item Selection Modal (following billing screen pattern) */}
                <Modal visible={modalVisible} transparent animationType="slide">
                    <View
                        style={{
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        }}
                    >
                        <View
                            style={{
                                width: '90%',
                                maxHeight: '80%',
                                backgroundColor: 'white',
                                borderRadius: 10,
                                padding: 16,
                                position: 'relative',
                            }}
                        >
                            {/* Header */}
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginBottom: 16,
                                }}
                            >
                                <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Select Item</Text>
                            </View>

                            {/* Search Input */}
                            <TextInput
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                    marginBottom: 16,
                                    fontSize: 16,
                                }}
                                placeholder="Search items..."
                                value={searchText}
                                onChangeText={setSearchText}
                            />

                            {/* Filtered ScrollView with 4-column layout (following billing screen pattern) */}
                            <ScrollView contentContainerStyle={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                paddingVertical: 10,
                            }}>
                                {itemList.filter(
                                    (item) =>
                                        item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                        item.ItemID.toLowerCase().includes(searchText.toLowerCase())
                                ).map((item) => (
                                    <TouchableOpacity
                                        key={item.ItemID}
                                        style={{
                                            backgroundColor: '#FDC500',
                                            borderRadius: 8,
                                            padding: 10,
                                            width: '23%',
                                            minHeight: 100,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 10,
                                            marginRight: '2%',
                                        }}
                                        onPress={() => handleItemSelect(item)}
                                    >
                                        <Text style={{
                                            fontSize: 14,
                                            color: 'rgb(2, 2, 2)',
                                            textAlign: 'center',
                                        }}>{item.ItemName}</Text>
                                    </TouchableOpacity>
                                ))}
                            </ScrollView>

                            {/* Close X Button (following billing screen pattern) */}
                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                                style={{
                                    position: 'absolute',
                                    top: 10,
                                    right: 10,
                                    width: 30,
                                    height: 30,
                                    backgroundColor: '#000',
                                    borderRadius: 15,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    zIndex: 1000,
                                }}
                            >
                                <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>×</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>



                <View style={styles.row}>
                    <TextInput
                        placeholder="Wt(Kg)"
                        style={[
                            styles.inputSmall,
                            { backgroundColor: (sellByWeight && selectedTransferType && selectedBranchId) ? 'white' : '#f0f0f0' }
                        ]}
                        keyboardType="numeric"
                        value={kgs}
                        onChangeText={setKgs}
                        editable={Boolean(sellByWeight && selectedTransferType && selectedBranchId)}
                    />
                    <TextInput
                        placeholder="Rate"
                        style={[
                            styles.inputSmall,
                            { backgroundColor: (selectedTransferType === 'LOCAL VENDOR' && selectedBranchId) ? 'white' : '#f0f0f0' }
                        ]}
                        keyboardType="numeric"
                        value={rate}
                        onChangeText={setRate}
                        editable={Boolean(selectedTransferType === 'LOCAL VENDOR' && selectedBranchId)}
                    />
                    <TextInput
                        placeholder="Nos"
                        style={[
                            styles.inputSmall,
                            { backgroundColor: ((!sellByWeight || altQtyEnabled) && selectedTransferType && selectedBranchId) ? 'white' : '#f0f0f0' }
                        ]}
                        keyboardType="numeric"
                        value={nos}
                        onChangeText={setNos}
                        editable={Boolean((!sellByWeight || altQtyEnabled) && selectedTransferType && selectedBranchId)}
                    />
                </View>
                <TextInput
                    placeholder="Add Remarks"
                    style={{
                        height: 70,
                        borderColor: '#ccc',
                        backgroundColor: (selectedTransferType && selectedBranchId) ? 'white' : '#f0f0f0',
                        borderWidth: 1,
                        borderRadius: 8,
                        padding: 10,
                        textAlignVertical: 'top', // Important for Android to align text at top
                    }}
                    multiline={true}
                    numberOfLines={4}
                    value={remarks}
                    onChangeText={setRemarks}
                    editable={Boolean(selectedTransferType && selectedBranchId)}
                />
                <View style={styles.row}>
                    <TouchableOpacity
                        style={[styles.addBtn, { opacity: (!selectedTransferType || !selectedBranchId) ? 0.5 : 1 }]}
                        onPress={handleAddItem}
                        disabled={!selectedTransferType || !selectedBranchId}
                    >
                        <Text style={styles.btnText}>{editingItemIndex !== null ? 'Update' : 'Add'}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.clearBtn, { opacity: (!selectedTransferType || !selectedBranchId) ? 0.5 : 1 }]}
                        onPress={handleClearFields}
                        disabled={!selectedTransferType || !selectedBranchId}
                    >
                        <Text style={styles.btnText}>Clear</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {/* </ScrollView> */}


            {/* Table (following receiving screen pattern) */}
            <View style={styles.itemDetails_tableContainer}>
                <DataTable>
                    <DataTable.Header style={styles.itemDetails_tableHeader}>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Select</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Line Number</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item ID</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item Name</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Batch Number</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Nos</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Kgs</Text></DataTable.Title>
                        <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Remarks</Text></DataTable.Title>
                    </DataTable.Header>

                    {tableData.map((item, index) => (
                        <DataTable.Row
                            key={item.id}
                            onPress={() => handleItemRowClick(item, index)}
                            style={{ backgroundColor: editingItemIndex === index ? '#e6f3ff' : 'transparent' }}
                        >
                            <DataTable.Cell>
                                <CheckBox
                                    value={selectedRows.includes(item.id)}
                                    onValueChange={(newValue) => {
                                        if (newValue) {
                                            setSelectedRows([...selectedRows, item.id]);
                                        } else {
                                            setSelectedRows(selectedRows.filter(id => id !== item.id));
                                        }
                                    }}
                                />
                            </DataTable.Cell>
                            <DataTable.Cell>{item.lineNo}</DataTable.Cell>
                            <DataTable.Cell>{item.itemId}</DataTable.Cell>
                            <DataTable.Cell>{item.itemName}</DataTable.Cell>
                            <DataTable.Cell>{item.batch}</DataTable.Cell>
                            <DataTable.Cell>{item.nos}</DataTable.Cell>
                            <DataTable.Cell>{item.kgs}</DataTable.Cell>
                            <DataTable.Cell>{item.remarks}</DataTable.Cell>
                        </DataTable.Row>
                    ))}
                </DataTable>

                {/* Delete Button (following receiving screen pattern) */}
                <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
                    <Text style={styles.deleteBtnText}>Delete Selected Row</Text>
                </TouchableOpacity>
            </View>

            {/* View Indent Modal (following billing screen view bill popup pattern) */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={viewModalVisible}
                onRequestClose={() => setViewModalVisible(false)}
            >
                <View style={styles.dialog_overlay}>
                    <View style={styles.dialog_container}>
                        {/* Header */}
                        <Text style={styles.dialog_title}>View Indent</Text>

                        {/* Search and Date Range Selection in same row */}
                        <View style={{ flexDirection: 'row', gap: 8, marginBottom: 16 }}>
                            {/* Search Input */}
                            <TextInput
                                placeholder="Search..."
                                value={searchQuery}
                                onChangeText={(text) => {
                                    setSearchQuery(text);
                                }}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    flex: 1,
                                    fontSize: 14,
                                }}
                            />

                            {/* From Date */}
                            <TouchableOpacity
                                onPress={() => setShowFromDatePicker(true)}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    justifyContent: 'center',
                                    flex: 1,
                                }}
                            >
                                <Text style={{ fontSize: 12, color: '#666' }}>From Date</Text>
                                <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                    {fromDate ? fromDate.toLocaleDateString() : 'Select Date'}
                                </Text>
                            </TouchableOpacity>

                            {/* To Date */}
                            <TouchableOpacity
                                onPress={() => setShowToDatePicker(true)}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    justifyContent: 'center',
                                    flex: 1,
                                }}
                            >
                                <Text style={{ fontSize: 12, color: '#666' }}>To Date</Text>
                                <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                    {toDate ? toDate.toLocaleDateString() : 'Select Date'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* From Date Picker */}
                        {showFromDatePicker && (
                            <DateTimePicker
                                value={fromDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => handleDateChange(event, selectedDate, true)}
                            />
                        )}

                        {/* To Date Picker */}
                        {showToDatePicker && (
                            <DateTimePicker
                                value={toDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => handleDateChange(event, selectedDate, false)}
                            />
                        )}

                        {/* Indent List (following billing screen grid pattern) */}
                        <ScrollView style={styles.dialog_content}>
                            <View style={styles.dialog_grid}>
                                {loading && (
                                    <View style={{ width: '100%', alignItems: 'center', padding: 20 }}>
                                        <ActivityIndicator size="large" color="#0000ff" />
                                    </View>
                                )}
                                {!loading && filteredIndents.map((indent, index) => {
                                    // Calculate margin for proper 3-button layout
                                    const isThirdInRow = (index + 1) % 3 === 0;
                                    const marginRightValue = isThirdInRow ? 0 : '2%';

                                    return (
                                        <TouchableOpacity
                                            key={index}
                                            onPress={() => {
                                                handleIndentSelect(indent);
                                            }}
                                            style={{
                                                backgroundColor: '#FDC500',
                                                padding: 15,
                                                borderRadius: 12,
                                                width: '32%',
                                                aspectRatio: 1,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                marginBottom: 15,
                                                marginRight: marginRightValue,
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.25,
                                                shadowRadius: 3.84,
                                                elevation: 5,
                                            }}
                                        >
                                            <Text style={{
                                                color: '#000',
                                                textAlign: 'center',
                                                fontWeight: 'bold',
                                                fontSize: 16,
                                            }}>
                                                {indent.IndentID}
                                            </Text>
                                        </TouchableOpacity>
                                    );
                                })}
                                {!loading && filteredIndents.length === 0 && (
                                    <View style={{ width: '100%', alignItems: 'center', padding: 20 }}>
                                        <Text>No indent records found for the selected date range.</Text>
                                    </View>
                                )}
                            </View>
                        </ScrollView>

                        {/* Close X Button (following billing screen pattern) */}
                        <TouchableOpacity
                            onPress={() => setViewModalVisible(false)}
                            style={{
                                position: 'absolute',
                                top: 10,
                                right: 10,
                                width: 30,
                                height: 30,
                                backgroundColor: '#000',
                                borderRadius: 15,
                                justifyContent: 'center',
                                alignItems: 'center',
                                zIndex: 1000,
                            }}
                        >
                            <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>×</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

        </View>
    );
};

const styles = StyleSheet.create({

    cardItem: {
        backgroundColor: '#f9f9f9',
        padding: 16,
        marginVertical: 8,
        marginHorizontal: 10,
        borderRadius: 10,
        elevation: 3, // for Android
        shadowColor: '#000', // for iOS
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },

    cardItemText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },

    rowWrapper: {
        justifyContent: 'space-between',
        marginBottom: 10,
    },

    cardItem: {
        backgroundColor: '#FDC500',
        flex: 1,
        aspectRatio: 1, // Makes it square
        margin: 5,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 2,
    },

    cardItemText: {
        fontSize: 12,
        fontWeight: 'bold',
        textAlign: 'center',
        paddingHorizontal: 5,
    },


















    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)'
    },
    modalContent: { margin: 20, backgroundColor: '#fff', height: '900', padding: 20, borderRadius: 10 },
    locationName: { padding: 10, fontSize: 16 },
    closeModalText: { color: 'white', padding: 6, fontSize: 14, fontWeight: 'bold', },
    noBranchText: {
        textAlign: 'center',
        color: 'gray',
        marginTop: 20,
        fontSize: 16,
    },


    ModalSearchinput: {
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        borderRadius: 6,
        marginBottom: 10,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 10,
    },
    closeModalBtn: {
        backgroundColor: 'red',
        padding: 10,
        alignSelf: 'center',
        marginTop: 10,
        borderRadius: 8,

    },

    branchGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },

    branchCard: {
        width: '23%', // Slightly less than 25% to allow spacing
        aspectRatio: 1, // Makes it square
        marginBottom: 10,
        backgroundColor: '#f0f0f0',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FDC500',
        borderRadius: 8,
        elevation: 2,
        height: 23,
    },

    branchText: {
        fontSize: 14,
        textAlign: 'center',
    },






    container: {
        flex: 1,
    },



    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#EBEBEB',
        padding: 20,
    },

    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },
    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },


    section: {
        backgroundColor: '#e9e9e9',
        marginTop: 10,
        borderRadius: 10,
        padding: 10,
    },
    sectionTitle: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 5,
    },
    row: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: 10,
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    input: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 5,
        height: 50,
    },

    itemRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    itemInput: {
        flex: 1,
        width: '100%',
        borderWidth: 1,
        borderColor: '#ccc',
        height: 50,
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 10,
    },
    inputSmall: {
        height: 50,
        width: '32.5%',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
    },
    rightInputContainer: {
        flexDirection: 'row',
        flex: 1,
        alignItems: 'center',
        marginLeft: 10,

    },
    selectBtn: {
        backgroundColor: '#002b5c',
        paddingHorizontal: 16,
        borderRadius: 6,
        marginLeft: 5,
        justifyContent: 'center',
    },
    selectBtnText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    searchInputInline: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 10,
        height: 50,
    },
    searchBtn: {
        backgroundColor: '#002b5c',
        paddingHorizontal: 16,
        borderRadius: 6,
        justifyContent: 'center',
    },
    searchBtnText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    addBtn: {
        backgroundColor: 'green',
        padding: 14,
        borderRadius: 6,
        flex: 1,
        marginRight: 5,
        marginTop: 15,
        alignItems: 'center',
    },
    clearBtn: {
        marginTop: 15,
        backgroundColor: 'red',
        padding: 14,
        borderRadius: 6,
        flex: 1,
        marginLeft: 5,
        alignItems: 'center',
    },
    btnText: {
        color: '#fff',
        fontWeight: 'bold',
    },





    tableContainer: {
        marginTop: 10,
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 10,

    },

    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        paddingVertical: 12,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderColor: '#ccc',
    },
    tableHeaderText: {
        flex: 1,
        fontWeight: 'bold',
        // textAlign: 'center',
        color: 'white',
    },

    tableHeaderCell: {
        fontWeight: 'bold',
        // textAlign: 'center',
        color: 'white',
    },

    tableRow: {
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderColor: '#eee',
        alignItems: 'center',
    },

    tableCell: {
        paddingHorizontal: 5,
        // textAlign: 'center',
    },
    deleteBtn: {
        backgroundColor: 'red',
        padding: 10,
        marginTop: 10,
        borderRadius: 6,
        alignItems: 'center',
        marginBottom: 10,
    },
    deleteBtnText: {
        color: 'white',
        fontWeight: 'bold',
        paddingVertical: 5,
    },
    indentDetails_dropdown: {
        width: '50%',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        backgroundColor: 'white',
        marginBottom: 10,
    },

    // ==================== MODAL STYLES (from receiving screen) ====================
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '90%',
        maxHeight: '80%',
        alignSelf: 'center',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
    },
    modalCloseButton: {
        padding: 5,
    },
    modalCloseButtonText: {
        fontSize: 24,
        color: '#999',
        fontWeight: 'bold',
    },
    modalLoadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
    modalLoadingText: {
        fontSize: 16,
        color: '#666',
    },
    modalBranchList: {
        maxHeight: 400,
    },

    // ==================== DIALOG STYLES (from billing screen) ====================
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 16,
        width: '80%',
        maxHeight: '80%',
    },
    dialog_title: {
        fontSize: 15,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
    },
    dialog_content: {
        maxHeight: Dimensions.get('window').height * 0.6,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        paddingHorizontal: 5,
    },
    modalBranchItem: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        backgroundColor: '#F8F8F8',
        marginBottom: 5,
        borderRadius: 5,
    },
    modalBranchId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    modalBranchName: {
        fontSize: 14,
        color: '#666',
        marginTop: 2,
    },
    noResultsText: {
        textAlign: 'center',
        color: '#666',
        fontSize: 16,
        marginTop: 20,
        fontStyle: 'italic',
    },

    // ==================== TABLE STYLES (from receiving screen) ====================
    itemDetails_tableContainer: {
        marginTop: 10,
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 10,
    },
    itemDetails_tableHeader: {
        backgroundColor: '#002b5c',
    },
    itemDetails_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 12,
    },


});

export default Indent;