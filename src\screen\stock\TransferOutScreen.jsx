import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Modal,
    FlatList,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import { DataTable } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';

import { fetchBranchList } from '../../apiHandling/StockAPI/fetchBranchListAPI';
import { fetchBranchDetails } from '../../apiHandling/StockAPI/fetchBranchDetailsAPI';
import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { saveReceiving, fetchBusinessDate } from '../../apiHandling/StockAPI/saveReceivingAPI';
import { fetchTransferOutList } from '../../apiHandling/StockAPI/fetchTransferOutListAPI';

import AsyncStorage from '@react-native-async-storage/async-storage';

const TransferOutScreen = () => {
    const navigation = useNavigation();
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [selectedTransferType, setSelectedTransferType] = useState('');
    const [orderNumber, setOrderNumber] = useState('');
    const [invoiceNumber, setInvoiceNumber] = useState('');
    const [lotNumber, setLotNumber] = useState('');

    // Location & Vehicle Details states
    const [location, setLocation] = useState('');
    const [selectedBranchId, setSelectedBranchId] = useState('');
    const [isTransit, setIsTransit] = useState(false);
    const [selectedVehicleType, setSelectedVehicleType] = useState('');
    const [selectedAddress, setSelectedAddress] = useState('');
    const [distance, setDistance] = useState('');
    const [vehicleNumber, setVehicleNumber] = useState('');
    const [driverName, setDriverName] = useState('');
    const [remarks, setRemarks] = useState('');

    // Location Modal states
    const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);
    const [branchList, setBranchList] = useState([]);
    const [loadingBranches, setLoadingBranches] = useState(false);

    // Address states
    const [addressList, setAddressList] = useState([]);
    const [loadingAddresses, setLoadingAddresses] = useState(false);

    // Item Details states
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemBatch, setItemBatch] = useState('');
    const [selectedPort, setSelectedPort] = useState('');
    const [itemWeight, setItemWeight] = useState('');
    const [itemNos, setItemNos] = useState('');

    // Item management states
    const [itemList, setItemList] = useState([]);
    const [itemDetail, setItemDetail] = useState(null);
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');

    // Table states
    const [tableData, setTableData] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [editingItemIndex, setEditingItemIndex] = useState(null);

    // View modal states
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [transferOutList, setTransferOutList] = useState([]);
    const [filteredTransferOuts, setFilteredTransferOuts] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedViewBranchId, setSelectedViewBranchId] = useState('');
    const [viewBranchList, setViewBranchList] = useState([]);
    const [loadingViewBranches, setLoadingViewBranches] = useState(false);



    useEffect(() => {
        // Load item list when screen mounts
        loadItemList();
    }, []);

    // Validation helper functions
    const isSaveButtonDisabled = () => {
        return !selectedTransferType || !selectedBranchId || tableData.length === 0;
    };

    const isIndentDetailsDisabled = () => {
        return !selectedTransferType;
    };

    const isLocationDetailsDisabled = () => {
        return !selectedTransferType;
    };

    const isIndentFieldsDisabled = () => {
        return !selectedBranchId || tableData.length > 0;
    };

    const isVehicleFieldsDisabled = () => {
        return selectedVehicleType === 'Own';
    };

    const isItemDetailsDisabled = () => {
        return !selectedTransferType || !selectedBranchId;
    };

    // Load transfer out list when view modal parameters change
    useEffect(() => {
        if (viewModalVisible && selectedViewBranchId && fromDate && toDate) {
            loadTransferOutList();
        }
    }, [viewModalVisible, selectedViewBranchId, fromDate, toDate]);

    // Filter transfer outs when search query changes
    useEffect(() => {
        if (!searchQuery) {
            setFilteredTransferOuts(transferOutList);
        } else {
            const filtered = transferOutList.filter(transferOut =>
                transferOut.DocID.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (transferOut.ISO_Number && transferOut.ISO_Number.toLowerCase().includes(searchQuery.toLowerCase()))
            );
            setFilteredTransferOuts(filtered);
        }
    }, [searchQuery, transferOutList]);

    const loadItemList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const data = await fetchItemList(bearerToken, loginBranchID);
        setItemList(data);
    };

    // Item selection functions
    const handleItemSelectPress = () => {
        // Reset all item-related state
        setItemName('');
        setItemId('');
        setItemBatch('');
        setItemNos('');
        setItemWeight('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };

    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);

        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const detail = await fetchItemDetail(bearerToken, item.ItemID);
        setItemDetail(detail);

        if (!detail) {
            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);
            setItemNos('0');
            setItemWeight('0');
            setItemBatch('');
            return;
        }

        const { BatchEnabled, SellByWeight, AltQtyEnabled } = detail;

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);


        // Reset batch field - no longer fetching from API
        setItemBatch('');

        // Set fields based on flags regardless of batch
        if (SellByWeight && AltQtyEnabled) {
            setItemWeight('');
            setItemNos('');
        } else if (SellByWeight) {
            setItemWeight('');
            setItemNos('0');
        } else {
            setItemNos('');
            setItemWeight('0');
        }
    };



    // Table management functions
    const handleAddItem = () => {
        if (!itemName || !itemId) {
            Alert.alert('Error', 'Please select an item first');
            return;
        }

        if (batchEnabled && !itemBatch) {
            Alert.alert('Error', 'Please enter a batch number');
            return;
        }

        if (editingItemIndex !== null) {
            // Update existing item (following stock take screen pattern)
            const updatedItems = [...tableData];
            updatedItems[editingItemIndex] = {
                ...updatedItems[editingItemIndex],
                itemId,
                itemName,
                batch: itemBatch || '',
                nos: itemNos || '0',
                kgs: itemWeight || '0',
                remarks: remarks || '',
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                batchEnabled: batchEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || ''
            };
            setTableData(updatedItems);
            setEditingItemIndex(null);
        } else {
            // Check for duplicate items (same item ID)
            const isDuplicateItem = tableData.some(item => item.itemId === itemId);
            if (isDuplicateItem) {
                Alert.alert('Error', 'This item has already been added');
                return;
            }

            // Check for duplicate batch numbers (if batch is enabled and entered)
            if (batchEnabled && itemBatch) {
                const isDuplicateBatch = tableData.some(item =>
                    item.batch && item.batch.toLowerCase() === itemBatch.toLowerCase()
                );
                if (isDuplicateBatch) {
                    Alert.alert('Error', 'This batch number has already been used');
                    return;
                }
            }

            const newItem = {
                lineNo: String(tableData.length + 1).padStart(3, '0'),
                itemId,
                itemName,
                batch: itemBatch || '',
                nos: itemNos || '0',
                kgs: itemWeight || '0',
                remarks: remarks || '',
                id: Date.now(), // Unique row ID
                selected: false,
                // Store item details for save API
                sellByWeight: sellByWeight,
                altQtyEnabled: altQtyEnabled,
                batchEnabled: batchEnabled,
                itemFamilyID: itemDetail?.ItemFamilyID || '',
                stockGroupId: itemDetail?.StockGroupId || ''
            };

            setTableData(prev => [...prev, newItem]);
        }
        handleClearFields(); // Reset form after add/update
    };

    const handleClearFields = () => {
        setItemName('');
        setItemId('');
        setItemBatch('');
        setItemNos('');
        setItemWeight('');
        setRemarks('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSelectedPort(null);
        setItemDetail(null);
        setEditingItemIndex(null); // Reset editing state
    };

    // Function to handle item row click for editing (following stock take screen pattern)
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        setItemName(item.itemName);
        setItemId(item.itemId);
        setItemBatch(item.batch);
        setItemNos(item.nos);
        setItemWeight(item.kgs);
        setRemarks(item.remarks || '');
        setBatchEnabled(item.batchEnabled);
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);

        // Set item detail if available
        if (item.itemFamilyID || item.stockGroupId) {
            setItemDetail({
                ItemFamilyID: item.itemFamilyID,
                StockGroupId: item.stockGroupId,
                SellByWeight: item.sellByWeight,
                AltQtyEnabled: item.altQtyEnabled,
                BatchEnabled: item.batchEnabled
            });
        }
    };

    const handleResetAndReload = () => {
        // Reset all form states
        setSelectedScrollOption('');
        setSelectedTransferType('');
        setOrderNumber('');
        setInvoiceNumber('');
        setLotNumber('');

        // Reset location & vehicle details
        setLocation('');
        setSelectedBranchId('');
        setIsTransit(false);
        setSelectedVehicleType('');
        setSelectedAddress('');
        setDistance('');
        setVehicleNumber('');
        setDriverName('');

        // Reset item details
        handleClearFields();

        // Reset table data
        setTableData([]);
        setSelectedRows([]);

        // Reset modal states
        setModalVisible(false);
        setIsLocationModalVisible(false);

        // Reset lists
        setBranchList([]);
        setAddressList([]);

        // Reload item list
        loadItemList();
    };

    const deleteSelectedItems = () => {
        const filtered = tableData.filter(item => !selectedRows.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setTableData(reIndexed);
        setSelectedRows([]);
    };
    const getISTISOString = () => {
        const now = new Date();

        // Offset IST (+5:30) in milliseconds
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);

        // Format as ISO string (will still end in Z, but value is IST)
        return istTime.toISOString(); // Includes milliseconds and Z
    };

    const handleSave = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
        const loginUserID = user.userId;
        try {
            // Validation
            if (!selectedTransferType) {
                Alert.alert('Error', 'Please select transfer type');
                return;
            }
            if (!selectedBranchId) {
                Alert.alert('Error', 'Please select location');
                return;
            }
            if (tableData.length === 0) {
                Alert.alert('Error', 'Please add at least one item');
                return;
            }

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = getISTISOString();

            const payload = {
                trOutId: "",
                branchId: loginBranchID,
                transTypeId: "TROUT",
                tranSubTypeId: selectedTransferType,
                taxStructureCode: "STATEGST",
                businessDate: businessDate,
                trOrderId: orderNumber || "",
                remarks: "",
                toBranchId: selectedBranchId,
                isO_Number: invoiceNumber || "",
                delieveryMode: "",
                vehicleOwnerType: selectedVehicleType?.toLowerCase() || "own",
                vehicleOwnerBranchId: loginBranchID,
                deliveryStatusCode: "",
                driverName: driverName || "",
                deliveryAddressID: selectedAddress || "00001",
                kmDistance: parseInt(distance) || 0,
                refIndentId: "",
                vehicleNumber: vehicleNumber || "",
                deleted: "N",
                posted: true,
                isKISettled: true,
                createdUserId: loginUserID,
                createdDate: systemDate,
                modifiedUserId: "string",
                modifiedDate: "1753-01-01T00:00:00.000Z",
                deletedUserId: "string",
                deletedDate: "1753-01-01T00:00:00.000Z",
                postedUserID: "string",
                postedDate: "1753-01-01T00:00:00.000Z",
                trOutDetails: tableData.map(item => ({
                    lineNumber: item.lineNo,
                    itemName: item.itemName,
                    batchNumber: item.batch || "",
                    nos: item.nos || "",
                    kgs: item.kgs || "",
                    altQty: 0,
                    qty: parseFloat(item.kgs) || parseFloat(item.nos) || 0,
                    remarks: item.remarks || "",
                    stockQty: 0,
                    stockAltQty: 0,
                    wScale: 0,
                    sellByWeight: item.sellByWeight || false,
                    unitGrossWt: 0,
                    altQtyEnabled: item.altQtyEnabled || false,
                    itemID: item.itemId,
                    groupStockQty: 0,
                    itemStatusId: "OK",
                    binID: "OKBIN",
                    rate: 0,
                    taxCategoryId: "0000000002",
                    taxPercent: 0,
                    unitMRP: 0,
                    productAmount: 0,
                    totalAmount: 0,
                    itemFamilyId: item.itemFamilyID || "",
                    stockGroupId: item.stockGroupId || "",
                    batchEnabled: item.batchEnabled || false,
                    deleted: "N",
                    batchUseByDate: "1753-01-01T00:00:00.000Z",
                    createdUserId: loginUserID,
                    createdDate: systemDate,
                    modifiedUserId: "string",
                    modifiedDate: "1753-01-01T00:00:00.000Z",
                    deletedUserId: "string",
                    deletedDate: "1753-01-01T00:00:00.000Z",
                    dml: "i"
                }))
            };

            const result = await saveReceiving(bearerToken, payload);

            if (result.result === 1) {
                // Show success dialog with print option (following stock take screen pattern)
                Alert.alert(
                    'Success',
                    `Transfer out saved successfully\nTransfer Out ID: ${result.description}\n\nDo you want to print the transfer out?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                handleResetAndReload();
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                // Reset page first, then navigate
                                handleResetAndReload();
                                fetchLatestTransferOutAndNavigate(result.description);
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Save Failed', result.description || 'Unexpected response from server');
            }
        } catch (error) {
            console.error('Save error:', error);
            Alert.alert('Error', 'Failed to save receiving data');
        }
    };

    // Function to fetch latest transfer out and navigate to view screen (following stock take screen pattern)
    const fetchLatestTransferOutAndNavigate = async (trOutId) => {
        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Navigate to ViewTransferOutScreen with the saved transfer out details
            navigation.navigate('ViewTransferOutScreen', {
                trOutId: trOutId,
                docId: trOutId // Using trOutId as docId for now
            });
        } catch (error) {
            console.error('Error navigating to view transfer out:', error);
            Alert.alert('Error', 'Failed to navigate to view transfer out');
            handleResetAndReload();
        }
    };



    // Function to load branch list
    const loadBranchList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            setLoadingBranches(true);
            const branches = await fetchBranchList(bearerToken);
            setBranchList(branches);
        } catch (error) {
            console.error('Error loading branch list:', error);
            Alert.alert('Error', 'Failed to load branch list');
        } finally {
            setLoadingBranches(false);
        }
    };

    // Function to handle location select button press
    const handleLocationSelectPress = () => {
        setIsLocationModalVisible(true);
        if (branchList.length === 0) {
            loadBranchList();
        }
    };

    // Function to load branch details (addresses)
    const loadBranchDetails = async (branchId) => {
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            setLoadingAddresses(true);
            const addresses = await fetchBranchDetails(bearerToken, branchId);
            setAddressList(addresses);
        } catch (error) {
            console.error('Error loading branch details:', error);
            Alert.alert('Error', 'Failed to load branch addresses');
        } finally {
            setLoadingAddresses(false);
        }
    };



    // Function to handle branch selection
    const handleBranchSelection = async (branch) => {
        setSelectedBranchId(branch.branchID);
        setLocation(`${branch.branchID} - ${branch.branchName}`);
        setIsLocationModalVisible(false);

        // Load related data when branch is selected
        await loadBranchDetails(branch.branchID);
    };

    // Function to close location modal
    const closeLocationModal = () => {
        setIsLocationModalVisible(false);
    };

    // View modal functions
    const openViewModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            // Load branch list for view modal
            const branches = await fetchBranchList(bearerToken);
            setViewBranchList(branches);

            // Set default dates
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const nextDay = new Date(businessDateObj);
            nextDay.setDate(nextDay.getDate() + 1);

            setFromDate(businessDateObj);
            setToDate(nextDay);
            setSelectedViewBranchId('');
            setSearchQuery('');
            setViewModalVisible(true);
        } catch (err) {
            console.error('Error opening view modal:', err);
            Alert.alert('Error', 'Failed to load view modal');
        }
        setLoading(false);
    };

    const loadTransferOutList = async () => {
        if (!selectedViewBranchId || !fromDate || !toDate) return;

        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');

        try {
            const fromDateStr = fromDate.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = toDate.toISOString().split('T')[0].replace(/-/g, '');

            const transferOuts = await fetchTransferOutList(bearerToken, selectedViewBranchId, fromDateStr, toDateStr);

            // Sort by DocID
            const sortedTransferOuts = transferOuts.sort((a, b) => {
                const docIdA = parseInt(a.DocID) || 0;
                const docIdB = parseInt(b.DocID) || 0;
                return docIdA - docIdB;
            });

            setTransferOutList(sortedTransferOuts);
            setFilteredTransferOuts(sortedTransferOuts);
        } catch (err) {
            console.error('Error loading transfer out list:', err);
            Alert.alert('Error', 'Failed to load transfer out list');
        }
        setLoading(false);
    };

    const handleTransferOutSelect = (transferOut) => {
        setViewModalVisible(false);
        navigation.navigate('ViewTransferOutScreen', {
            trOutId: transferOut.TrOutID,
            docId: transferOut.DocID
        });
    };

    const handleDateChange = (event, selectedDate, isFromDate) => {
        if (isFromDate) {
            setShowFromDatePicker(false);
            if (selectedDate) {
                setFromDate(selectedDate);
            }
        } else {
            setShowToDatePicker(false);
            if (selectedDate) {
                setToDate(selectedDate);
            }
        }
    };
    return (
        <View style={styles.container}>
            {/* <Navbar/> */}
            <Navbar />
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>

                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Receiving Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>Transfer Out</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={[
                                            buttonStyle,
                                            option === 'Save' && isSaveButtonDisabled() && { opacity: 0.5 }
                                        ]}
                                        disabled={option === 'Save' && isSaveButtonDisabled()}
                                        onPress={() => {
                                            setSelectedScrollOption(option);
                                            if (option === 'Save') {
                                                handleSave();
                                            } else if (option === 'View') {
                                                openViewModal();
                                            } else if (option === 'New' || option === 'Cancel') {
                                                handleResetAndReload();
                                            }
                                        }}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
            {/* Indent Details Container */}
            <View style={[
                styles.indentDetails_container,
                isIndentDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Indent Details Title */}
                <View style={styles.indentDetails_row}>
                    <Text style={styles.indentDetails_title}>Indent details</Text>
                </View>

                {/* Row 2: Transfer Type Dropdown, Choose Order, Select Button */}
                <View style={styles.indentDetails_inputRow}>
                    <View style={styles.indentDetails_transferTypeContainer}>
                        <Dropdown
                            data={[
                                { label: 'IB Branch Transfer', value: 'IB ST' },

                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Transfer Type"
                            value={selectedTransferType}
                            onChange={(item) => setSelectedTransferType(item.value)}
                            style={styles.indentDetails_dropdown}
                        />
                    </View>

                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose order"
                        value={orderNumber}
                        onChangeText={setOrderNumber}
                        editable={!isIndentFieldsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.indentDetails_selectButton,
                            isIndentFieldsDisabled() && { opacity: 0.5 }
                        ]}
                        disabled={isIndentFieldsDisabled()}
                    >
                        <Text style={styles.indentDetails_selectButtonText}>Select</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Invoice, Lot */}
                <View style={styles.indentDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Invoice"
                        value={invoiceNumber}
                        onChangeText={setInvoiceNumber}
                        editable={!isIndentFieldsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.indentDetails_textField,
                            isIndentFieldsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Lot"
                        value={lotNumber}
                        onChangeText={setLotNumber}
                        editable={!isIndentFieldsDisabled()}
                    />
                </View>
            </View>

            {/* Location & Vehicle Details Container */}
            <View style={[
                styles.locationVehicle_container,
                isLocationDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Location & Vehicle Details Title */}
                <View style={styles.locationVehicle_row}>
                    <Text style={styles.locationVehicle_title}>Location & Vehicle details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Transit Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            isLocationDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose location"
                        value={location}
                        onChangeText={setLocation}
                        editable={!isLocationDetailsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_selectButton,
                            isLocationDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={handleLocationSelectPress}
                        disabled={isLocationDetailsDisabled()}
                    >
                        <Text style={styles.locationVehicle_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_transitButton,
                            { backgroundColor: isTransit ? '#02096A' : '#DEDDDD' },
                            isLocationDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={() => setIsTransit(!isTransit)}
                        disabled={isLocationDetailsDisabled()}
                    >
                        <Text style={[
                            styles.locationVehicle_buttonText,
                            { color: isTransit ? 'white' : 'black' }
                        ]}>
                            Transit
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Vehicle Type Dropdown, Address Dropdown, New Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Own', value: 'Own' },
                                { label: 'Transporter', value: 'Transporter' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select vehicle type"
                            value={selectedVehicleType}
                            onChange={(item) => setSelectedVehicleType(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={addressList.map(address => ({
                                label: `${address.AddressID ?? ''}  ${address.AreaName ?? ''}`,
                                value: address.AddressID ?? ''
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Address"
                            value={selectedAddress}
                            onChange={(item) => setSelectedAddress(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <TouchableOpacity style={styles.locationVehicle_newButton}>
                        <Text style={styles.locationVehicle_buttonText}>New</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 4: Distance, Vehicle Number, Driver */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            isLocationDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Distance"
                        value={distance}
                        onChangeText={setDistance}
                        keyboardType="numeric"
                        editable={!isLocationDetailsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            (isLocationDetailsDisabled() || isVehicleFieldsDisabled()) && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Vehicle number"
                        value={vehicleNumber}
                        onChangeText={setVehicleNumber}
                        editable={!isLocationDetailsDisabled() && !isVehicleFieldsDisabled()}
                    />

                    <TextInput
                        style={[
                            styles.locationVehicle_textField,
                            (isLocationDetailsDisabled() || isVehicleFieldsDisabled()) && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Driver name"
                        value={driverName}
                        onChangeText={setDriverName}
                        editable={!isLocationDetailsDisabled() && !isVehicleFieldsDisabled()}
                    />
                </View>


            </View>


            {/* Item Details Container */}
            <View style={[
                styles.itemDetails_container,
                isItemDetailsDisabled() && { opacity: 0.5 }
            ]}>
                {/* Row 1: Item Details Title */}
                <View style={styles.itemDetails_row}>
                    <Text style={styles.itemDetails_title}>Item details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Choose Batch, Select Button, Select Port */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            isItemDetailsDisabled() && { backgroundColor: '#f0f0f0' }
                        ]}
                        placeholder="Choose Item"
                        value={itemName}
                        onChangeText={setItemName}
                        editable={!isItemDetailsDisabled()}
                    />

                    <TouchableOpacity
                        style={[
                            styles.itemDetails_selectButton,
                            isItemDetailsDisabled() && { opacity: 0.5 }
                        ]}
                        onPress={handleItemSelectPress}
                        disabled={isItemDetailsDisabled()}
                    >
                        <Text style={styles.itemDetails_buttonText}>Select</Text>
                    </TouchableOpacity>

                    {/* Item Selection Modal */}
                    <Modal visible={modalVisible} transparent animationType="slide">
                        <View
                            style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: 'rgba(0, 0, 0, 0.3)',
                            }}
                        >
                            <View
                                style={{
                                    width: '90%',
                                    maxHeight: '80%',
                                    backgroundColor: 'white',
                                    borderRadius: 10,
                                    padding: 16,
                                }}
                            >
                                {/* Header */}
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        marginBottom: 16,
                                    }}
                                >
                                    <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Select Item</Text>
                                    <TouchableOpacity onPress={() => setModalVisible(false)}>
                                        <Text style={{ fontSize: 24, color: '#999' }}>×</Text>
                                    </TouchableOpacity>
                                </View>

                                {/* Search Input */}
                                <TextInput
                                    style={{
                                        borderWidth: 1,
                                        borderColor: '#ccc',
                                        borderRadius: 8,
                                        paddingHorizontal: 12,
                                        paddingVertical: 8,
                                        marginBottom: 16,
                                        fontSize: 16,
                                    }}
                                    placeholder="Search items..."
                                    value={searchText}
                                    onChangeText={setSearchText}
                                />

                                {/* Filtered FlatList */}
                                <FlatList
                                    data={itemList.filter(
                                        (item) =>
                                            item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                            item.ItemID.toLowerCase().includes(searchText.toLowerCase())
                                    )}
                                    keyExtractor={(item) => item.ItemID}
                                    numColumns={3}
                                    showsVerticalScrollIndicator={false}
                                    contentContainerStyle={{ paddingBottom: 16 }}
                                    renderItem={({ item }) => (
                                        <TouchableOpacity
                                            style={{
                                                flex: 1,
                                                margin: 4,
                                                padding: 12,
                                                backgroundColor: '#f0f0f0',
                                                borderRadius: 6,
                                                alignItems: 'center',
                                            }}
                                            onPress={() => handleItemSelect(item)}
                                        >
                                            <Text style={{ textAlign: 'center' }}>{item.ItemName}</Text>
                                        </TouchableOpacity>
                                    )}
                                />
                            </View>
                        </View>
                    </Modal>

                    {batchEnabled && (
                        <TextInput
                            style={styles.itemDetails_textField}
                            placeholder="Enter batch number"
                            value={itemBatch}
                            onChangeText={setItemBatch}
                        />
                    )}

                    <View style={styles.itemDetails_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Port 1', value: 'Port 1' },
                                { label: 'Port 2', value: 'Port 2' },
                                { label: 'Port 3', value: 'Port 3' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select port"
                            value={selectedPort}
                            onChange={(item) => setSelectedPort(item.value)}
                            style={styles.itemDetails_dropdown}
                        />
                    </View>
                </View>

                {/* Row 3: Wt(kg), Nos */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (sellByWeight) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Wt(kg)"
                        value={itemWeight}
                        onChangeText={setItemWeight}
                        keyboardType="numeric"
                        editable={sellByWeight}
                    />

                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (!sellByWeight || altQtyEnabled) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Nos"
                        value={itemNos}
                        onChangeText={setItemNos}
                        keyboardType="numeric"
                        editable={!sellByWeight || altQtyEnabled}
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Remarks"
                        value={remarks}
                        onChangeText={setRemarks}
                    />
                </View>

                {/* Row 4: Add Button, Remove Button */}
                <View style={styles.itemDetails_buttonRow}>
                    <TouchableOpacity
                        style={styles.itemDetails_addButton}
                        onPress={handleAddItem}
                    >
                        <Text style={styles.itemDetails_buttonText}>Add</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.itemDetails_removeButton}
                        onPress={handleClearFields}
                    >
                        <Text style={styles.itemDetails_buttonText}>Clear</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 5: Table */}
                <View style={styles.itemDetails_tableContainer}>
                    <DataTable>
                        <DataTable.Header style={styles.itemDetails_tableHeader}>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Select</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Line Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item ID</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item Name</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Batch Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Nos</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Kgs</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Remarks</Text></DataTable.Title>
                        </DataTable.Header>

                        {tableData.map((item, index) => (
                            <TouchableOpacity
                                key={item.id}
                                onPress={() => handleItemRowClick(item, index)}
                                style={{ opacity: editingItemIndex === index ? 0.7 : 1 }}
                            >
                                <DataTable.Row>
                                    <DataTable.Cell>
                                        <TouchableOpacity
                                            onPress={(e) => {
                                                e.stopPropagation(); // Prevent row click when selecting checkbox
                                                if (selectedRows.includes(item.id)) {
                                                    setSelectedRows(selectedRows.filter(id => id !== item.id));
                                                } else {
                                                    setSelectedRows([...selectedRows, item.id]);
                                                }
                                            }}
                                        >
                                            <CheckBox
                                                value={selectedRows.includes(item.id)}
                                                onValueChange={() => {}} // Handled by TouchableOpacity
                                            />
                                        </TouchableOpacity>
                                    </DataTable.Cell>
                                    <DataTable.Cell>{item.lineNo}</DataTable.Cell>
                                    <DataTable.Cell>{item.itemId}</DataTable.Cell>
                                    <DataTable.Cell>{item.itemName}</DataTable.Cell>
                                    <DataTable.Cell>{item.batch}</DataTable.Cell>
                                    <DataTable.Cell>{item.nos}</DataTable.Cell>
                                    <DataTable.Cell>{item.kgs}</DataTable.Cell>
                                    <DataTable.Cell>{item.remarks}</DataTable.Cell>
                                </DataTable.Row>
                            </TouchableOpacity>
                        ))}
                    </DataTable>
                </View>

                {/* Row 6: Delete Selected Row Button */}
                <View style={styles.itemDetails_deleteButtonContainer}>
                    <TouchableOpacity
                        style={styles.itemDetails_deleteButton}
                        onPress={deleteSelectedItems}
                    >
                        <Text style={styles.itemDetails_buttonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                </View>
            </View>






            {/* Location Selection Modal */}
            <Modal
                visible={isLocationModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={closeLocationModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Select Location</Text>
                            <TouchableOpacity
                                style={styles.modalCloseButton}
                                onPress={closeLocationModal}
                            >
                                <Text style={styles.modalCloseButtonText}>×</Text>
                            </TouchableOpacity>
                        </View>

                        {loadingBranches ? (
                            <View style={styles.modalLoadingContainer}>
                                <Text style={styles.modalLoadingText}>Loading branches...</Text>
                            </View>
                        ) : (
                            <FlatList
                                data={branchList}
                                keyExtractor={(item) => item.branchID}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        style={styles.modalBranchItem}
                                        onPress={() => handleBranchSelection(item)}
                                    >
                                        <Text style={styles.modalBranchId}>{item.branchID}</Text>
                                        <Text style={styles.modalBranchName}>{item.branchName}</Text>
                                    </TouchableOpacity>
                                )}
                                style={styles.modalBranchList}
                            />
                        )}
                    </View>
                </View>
            </Modal>

            {/* View Transfer Out Modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={viewModalVisible}
                onRequestClose={() => setViewModalVisible(false)}
            >
                <View style={styles.viewModalOverlay}>
                    <View style={styles.viewModalContainer}>
                        {/* Header */}
                        <View style={styles.viewModalHeader}>
                            <Text style={styles.viewModalTitle}>View Transfer Out</Text>
                            <TouchableOpacity
                                style={styles.viewModalCloseButton}
                                onPress={() => setViewModalVisible(false)}
                            >
                                <Text style={styles.viewModalCloseButtonText}>×</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Branch Selection */}
                        <View style={styles.viewModalSection}>
                            <Text style={styles.viewModalSectionTitle}>Select Branch:</Text>
                            <Dropdown
                                data={viewBranchList.map(branch => ({
                                    label: `${branch.branchID} - ${branch.branchName}`,
                                    value: branch.branchID
                                }))}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Branch"
                                value={selectedViewBranchId}
                                onChange={(item) => setSelectedViewBranchId(item.value)}
                                style={styles.viewModalDropdown}
                            />
                        </View>

                        {/* Date Selection */}
                        <View style={styles.viewModalDateSection}>
                            <TouchableOpacity
                                onPress={() => setShowFromDatePicker(true)}
                                style={styles.viewModalDateButton}
                            >
                                <Text style={styles.viewModalDateText}>
                                    {fromDate ? fromDate.toLocaleDateString() : 'From Date'}
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={() => setShowToDatePicker(true)}
                                style={styles.viewModalDateButton}
                            >
                                <Text style={styles.viewModalDateText}>
                                    {toDate ? toDate.toLocaleDateString() : 'To Date'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* Search Input */}
                        <View style={styles.viewModalSearchSection}>
                            <TextInput
                                placeholder="Search by Doc ID or ISO Number..."
                                value={searchQuery}
                                onChangeText={setSearchQuery}
                                style={styles.viewModalSearchInput}
                            />
                        </View>

                        {/* Transfer Out List */}
                        <ScrollView style={styles.viewModalListContainer}>
                            {loading && (
                                <View style={styles.viewModalLoadingContainer}>
                                    <ActivityIndicator size="large" color="#0000ff" />
                                </View>
                            )}
                            {!loading && filteredTransferOuts.length === 0 && selectedViewBranchId && (
                                <View style={styles.viewModalEmptyContainer}>
                                    <Text>No transfer out records found for the selected criteria.</Text>
                                </View>
                            )}
                            {!loading && filteredTransferOuts.map((transferOut, index) => (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => handleTransferOutSelect(transferOut)}
                                    style={styles.viewModalListItem}
                                >
                                    <Text style={styles.viewModalListItemText}>
                                        {transferOut.DocID}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </ScrollView>

                        {/* Date Pickers */}
                        {showFromDatePicker && (
                            <DateTimePicker
                                value={fromDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => handleDateChange(event, selectedDate, true)}
                            />
                        )}

                        {showToDatePicker && (
                            <DateTimePicker
                                value={toDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, selectedDate) => handleDateChange(event, selectedDate, false)}
                            />
                        )}
                    </View>
                </View>
            </Modal>

        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#02096A',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,

    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== INDENT DETAILS STYLES ====================
    indentDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    indentDetails_row: {
        marginBottom: 8,
    },
    indentDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    indentDetails_transferTypeContainer: {
        flex: 1,
        minWidth: 120,
    },
    indentDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    indentDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    indentDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    indentDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    indentDetails_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== LOCATION & VEHICLE DETAILS STYLES ====================
    locationVehicle_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 4,
        borderRadius: 10,
    },
    locationVehicle_row: {
        marginBottom: 8,
    },
    locationVehicle_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    locationVehicle_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    locationVehicle_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    locationVehicle_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    locationVehicle_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    locationVehicle_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_transitButton: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_newButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_removeButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== ITEM DETAILS STYLES ====================
    itemDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 8,
        borderRadius: 10,
    },
    itemDetails_row: {
        marginBottom: 8,
    },
    itemDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    itemDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    itemDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 100,
    },
    itemDetails_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    itemDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    itemDetails_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    itemDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_addButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#02720F',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_removeButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    itemDetails_tableHeader: {
        backgroundColor: '#041C44',
    },
    itemDetails_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_deleteButtonContainer: {
        alignItems: 'center',
        marginBottom: 8,
    },
    itemDetails_deleteButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        borderWidth: 1,
        borderColor: '#FDC500',
    },

    // ==================== MODAL STYLES ====================
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '80%',
        maxHeight: '70%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        paddingBottom: 10,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#02096A',
    },
    modalCloseButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#FF0000',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalCloseButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    modalLoadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
    modalLoadingText: {
        fontSize: 16,
        color: '#666',
    },
    modalBranchList: {
        maxHeight: 400,
    },
    modalBranchItem: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        backgroundColor: '#F8F8F8',
        marginBottom: 5,
        borderRadius: 8,
    },
    modalBranchId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#02096A',
        marginBottom: 5,
    },
    modalBranchName: {
        fontSize: 14,
        color: '#666',
    },
    modalSearchInput: {
        height: 40,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        marginBottom: 15,
        backgroundColor: 'white',
    },

    // ==================== VIEW MODAL STYLES ====================
    viewModalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    viewModalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '90%',
        maxHeight: '80%',
    },
    viewModalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        paddingBottom: 10,
    },
    viewModalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#02096A',
    },
    viewModalCloseButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#FF0000',
        justifyContent: 'center',
        alignItems: 'center',
    },
    viewModalCloseButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    viewModalSection: {
        marginBottom: 15,
    },
    viewModalSectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    viewModalDropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    viewModalDateSection: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
    },
    viewModalDateButton: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        justifyContent: 'center',
        marginHorizontal: 5,
    },
    viewModalDateText: {
        fontSize: 16,
        color: '#333',
    },
    viewModalSearchSection: {
        marginBottom: 15,
    },
    viewModalSearchInput: {
        height: 50,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        backgroundColor: 'white',
        fontSize: 16,
    },
    viewModalListContainer: {
        maxHeight: 300,
        marginBottom: 10,
    },
    viewModalLoadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
    viewModalEmptyContainer: {
        padding: 20,
        alignItems: 'center',
    },
    viewModalListItem: {
        padding: 15,
        backgroundColor: '#F0F0F0',
        borderRadius: 8,
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#E0E0E0',
    },
    viewModalListItemText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#02096A',
        textAlign: 'center',
    },
});

export default TransferOutScreen;
