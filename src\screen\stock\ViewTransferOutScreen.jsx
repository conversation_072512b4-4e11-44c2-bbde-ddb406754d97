import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchTransferOutDetail } from '../../apiHandling/StockAPI/fetchTransferOutDetailAPI';

const ViewTransferOutScreen = ({ route }) => {
    const navigation = useNavigation();
    const { trOutId, docId } = route.params;

    // State for transfer out data
    const [transferOutData, setTransferOutData] = useState(null);
    const [branchData, setBranchData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    // Fetch transfer out details
    const fetchTransferOutDetails = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Fetch transfer out details
            const transferOutResponse = await fetchTransferOutDetail(bearerToken, loginBranchID, trOutId);

            // Fetch branch details
            const branchDetailsResponse = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchdetails`,
                {
                    params: { Branchid: loginBranchID },
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const transferOutDetail = transferOutResponse || {};
            const branchDetail = branchDetailsResponse?.data?.[0] || {};

            setTransferOutData(transferOutDetail);
            setBranchData(branchDetail);
        } catch (error) {
            console.error('Error fetching transfer out details:', error);
            Alert.alert('Error', 'Failed to load transfer out details.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate totals
    const calculateTotals = () => {
        if (!transferOutData?.details) return { totalQty: 0 };

        let totalQty = 0;

        transferOutData.details.forEach(item => {
            totalQty += parseFloat(item.Qty) || 0;
        });

        return { totalQty };
    };

    // Bluetooth printer functions
    const scanForPrinters = async (showErrorAlert = false) => {
        setIsScanning(true);
        try {
            await BLEPrinter.init();
            const devices = await BLEPrinter.getDeviceList();

            const printerDevices = devices.map(device => ({
                label: device.device_name || device.inner_mac_address,
                value: device.inner_mac_address,
                device: device
            }));

            setAvailablePrinters(printerDevices);

            if (printerDevices.length === 0 && showErrorAlert) {
                Alert.alert('No Printers Found', 'No Bluetooth printers found. Please make sure your printer is discoverable and try again.');
            }
        } catch (error) {
            console.error('Error scanning for printers:', error);
            if (showErrorAlert) {
                Alert.alert('Error', 'Failed to scan for printers. Please check Bluetooth permissions.');
            }
        } finally {
            setIsScanning(false);
        }
    };

    const connectToPrinter = async (printerAddress) => {
        try {
            await BLEPrinter.connectPrinter(printerAddress);
            return true;
        } catch (error) {
            console.error('Error connecting to printer:', error);
            return false;
        }
    };

    const formatBusinessDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    };

    const formatPrintDate = () => {
        const now = new Date();
        return now.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        }) + ' ' + now.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
    };

    const printTransferOut = async () => {
        if (!selectedPrinter) {
            Alert.alert('No Printer Selected', 'Please select a printer first.');
            return;
        }

        if (!transferOutData || !branchData) {
            Alert.alert('No Data', 'Transfer out data not loaded.');
            return;
        }

        setIsPrinting(true);

        try {
            const connected = await connectToPrinter(selectedPrinter.value);
            if (!connected) {
                Alert.alert('Connection Failed', 'Failed to connect to the selected printer.');
                setIsPrinting(false);
                return;
            }

            const header = transferOutData.header?.[0] || {};
            const details = transferOutData.details || [];
            const totals = calculateTotals();

            // Build receipt content
            let receiptContent = '';
            
            // Header
            receiptContent += '[C]<b>ABIS EXPORTS INDIA PVT LTD</b>\n';
            receiptContent += `[C]${branchData.BranchName || ''}\n`;
            receiptContent += `[C]${branchData.Address1 || ''}\n`;
            if (branchData.Address2) receiptContent += `[C]${branchData.Address2}\n`;
            receiptContent += `[C]${branchData.City || ''} ${branchData.PinCode || ''}\n`;
            receiptContent += `[C]Phone: ${branchData.Phone || ''}\n`;
            receiptContent += '[C]<<TRANSFER OUT>>\n';
            receiptContent += '--------------------------------\n';
            
            // Transfer details
            receiptContent += `TONO :${header.TrOutID || docId}       TO Date: ${formatBusinessDate(header.BusinessDate)}\n`;
            receiptContent += `From Location :${branchData.BranchId || ''}/${branchData.BranchName || ''}\n`;
            receiptContent += `To Location   :${header.ToBranchId || ''}/${header.ToBranchName || ''}\n`;
            receiptContent += `Indent No     :${header.RefIndentID || ''}\n`;
            receiptContent += `Driver Name   :${header.DriverName || 'NIL'}\n`;
            receiptContent += `Vehicle No    :${header.VehicleNumber || 'NIL'}\n`;
            receiptContent += `Printed On : ${formatPrintDate()}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += 'ItemCode  Item Name\n';
            receiptContent += '                            Qty\n';
            receiptContent += '----------------------------------------\n';
            
            // Items
            details.forEach(item => {
                receiptContent += `${item.ItemID || ''}  ${item.ItemName || ''}\n`;
                receiptContent += `                             ${item.Qty || 0}\n`;
            });
            
            receiptContent += '----------------------------------------\n';
            receiptContent += `TOTAL :                      ${totals.totalQty}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += `Remarks : ${header.Remarks || ''}\n`;

            await BLEPrinter.printText(receiptContent);
            Alert.alert('Success', 'Transfer out printed successfully!');

        } catch (error) {
            console.error('Error printing:', error);
            Alert.alert('Print Error', 'Failed to print transfer out. Please try again.');
        } finally {
            setIsPrinting(false);
        }
    };

    useEffect(() => {
        fetchTransferOutDetails();
        scanForPrinters();
    }, []);

    if (loading) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <ActivityIndicator size="large" color="#0000ff" />
                <Text style={{ marginTop: 10, fontSize: 16, color: '#666' }}>Loading transfer out details...</Text>
            </View>
        );
    }

    if (!transferOutData || !transferOutData.header || !transferOutData.details) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>No transfer out data found.</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={{ color: 'blue', marginTop: 10 }}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const header = transferOutData.header[0];
    const { totalQty } = calculateTotals();

    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>

            <Text style={styles.title}>ABIS EXPORTS INDIA PVT LTD</Text>
            <Text style={styles.center}>{branchData?.BranchName || ''}</Text>
            <Text style={styles.center}>{branchData?.AreaName || ''}</Text>
            <Text style={styles.center}>{branchData?.PINCODE || ''}</Text>
            <Text style={styles.center}>PhoneNo :</Text>
            <Text style={styles.center}>{'<<TRANSFER OUT>>'}</Text>

            <View style={styles.rowSpaceBetween}>
                <Text>TONO : {header.TrOutID || docId}</Text>
                <Text>TO Date: {formatBusinessDate(header.BusinessDate)}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>From Location: {branchData?.BranchId}/{branchData?.BranchName}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>To Location: {header.ToBranchId || ''}/{header.ToBranchName || ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Indent No: {header.RefIndentID || ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Driver Name: {header.DriverName || 'NIL'}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Vehicle No: {header.VehicleNumber || 'NIL'}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Printed On: {formatPrintDate()}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={[styles.cell, styles.itemCodeCell]}>ItemCode</Text>
                    <Text style={[styles.cell, styles.itemNameCell]}>Item Name</Text>
                    <Text style={[styles.cell, styles.qtyCell]}>QTY</Text>
                </View>
                {transferOutData.details.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={[styles.cell, styles.itemCodeCell]}>{item.ItemID}</Text>
                        <Text style={[styles.cell, styles.itemNameCell]}>{item.ItemName}</Text>
                        <Text style={[styles.cell, styles.qtyCell]}>{parseFloat(item.Qty) || 0}</Text>
                    </View>
                ))}
            </View>

            <View style={styles.divider} />

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>TOTAL:</Text>
                <Text style={styles.bold}>{totalQty}</Text>
            </View>

            {header.Remarks && (
                <View style={styles.remarksContainer}>
                    <Text style={styles.bold}>Remarks:</Text>
                    <Text>{header.Remarks}</Text>
                </View>
            )}

            {/* Printer Controls */}
            <View style={styles.printerControlsContainer}>
                <View style={styles.printerRow}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                            data={availablePrinters}
                            maxHeight={300}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={item => {
                                setSelectedPrinter(item.value);
                            }}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={() => scanForPrinters(true)}
                        disabled={isScanning}
                    >
                        {isScanning ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <Icon name="refresh" size={20} color="#fff" />
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printTransferOut}
                        disabled={isPrinting || !selectedPrinter}
                    >
                        {isPrinting ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Icon name="print" size={20} color="#fff" />
                                <Text style={styles.buttonText}>Print</Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },
    container: {
        padding: 16,
        backgroundColor: '#fff',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginTop: 40,
    },
    center: {
        textAlign: 'center',
        marginVertical: 2,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginVertical: 4,
    },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 8,
    },
    table: {
        borderWidth: 1,
        borderColor: '#000',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#000',
        minHeight: 40,
        alignItems: 'center',
    },
    cell: {
        padding: 8,
        textAlign: 'center',
        borderRightWidth: 1,
        borderRightColor: '#000',
        fontSize: 12,
    },
    itemCodeCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    itemNameCell: {
        flex: 3,
        textAlign: 'left',
    },
    qtyCell: {
        flex: 1,
        textAlign: 'right',
        borderRightWidth: 0,
    },
    bold: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#000',
    },
    remarksContainer: {
        marginTop: 16,
        padding: 8,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
    },
    printerControlsContainer: {
        marginTop: 20,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default ViewTransferOutScreen;
