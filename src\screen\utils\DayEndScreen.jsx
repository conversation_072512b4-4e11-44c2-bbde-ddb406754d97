import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, branchId, branchName } from '../../globals';
import DateTimePicker from '@react-native-community/datetimepicker';

const DayEndScreen = () => {
  // UI control states
  const [selectedScrollOption, setSelectedScrollOption] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Form data states
  const [dayEndDate, setDayEndDate] = useState(new Date());
  const [documentNo, setDocumentNo] = useState('');
  const [cashOnHand, setCashOnHand] = useState('50000');
  const [expensesGroup, setExpensesGroup] = useState('');
  const [expensesHead, setExpensesHead] = useState('');
  const [budgetAmount, setBudgetAmount] = useState('');
  const [remark, setRemark] = useState('');
  const [paymentMode, setPaymentMode] = useState('');
  const [amount, setAmount] = useState('');
  const [totalAmount, setTotalAmount] = useState('0');

  // Validation states
  const [errors, setErrors] = useState({});

  // Table data
  const [tableData, setTableData] = useState([
    { id: 1, lineNumber: '001', bankAccountName: 'HDFC Bank', remark: 'Salary payment', amount: '25000', isChecked: false },
    { id: 2, lineNumber: '002', bankAccountName: 'SBI Bank', remark: 'Vendor payment', amount: '15000', isChecked: false }
  ]);

  // Format date for display
  const formatDate = (date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Handle date change
  const onDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDayEndDate(selectedDate);
    }
  };

  const toggleCheckbox = (id) => {
    const newData = tableData.map(item =>
      item.id === id ? { ...item, isChecked: !item.isChecked } : item
    );
    setTableData(newData);
  };

  const handleAddItem = () => {
    // Validate inputs
    const newErrors = {};

    if (!paymentMode) {
      newErrors.paymentMode = 'Payment mode is required';
    }

    if (!remark) {
      newErrors.remark = 'Remark is required';
    }

    if (!amount) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(Number(amount)) || Number(amount) <= 0) {
      newErrors.amount = 'Amount must be a positive number';
    }

    // Update errors state
    setErrors(newErrors);

    // If no errors, add the item
    if (Object.keys(newErrors).length === 0 && remark && amount) {
      const newItem = {
        id: Date.now(),
        lineNumber: (tableData.length + 1).toString().padStart(3, '0'),
        bankAccountName: paymentMode || 'Not specified',
        remark: remark,
        amount: amount,
        isChecked: false
      };
      setTableData([...tableData, newItem]);

      // Calculate new total
      const newTotal = tableData.reduce((sum, item) => sum + Number(item.amount), 0) + Number(amount);
      setTotalAmount(newTotal.toString());

      // Clear fields
      setRemark('');
      setAmount('');
      setPaymentMode('');
      setErrors({});
    } else {
      // Show alert for validation errors
      Alert.alert(
        "Validation Error",
        "Please fill in all required fields correctly.",
        [{ text: "OK" }]
      );
    }
  };

  const handleClear = () => {
    setRemark('');
    setAmount('');
    setPaymentMode('');
  };

  const handleDeleteSelected = () => {
    const newData = tableData.filter(item => !item.isChecked);
    setTableData(newData);

    // Recalculate total
    const newTotal = newData.reduce((sum, item) => sum + Number(item.amount), 0);
    setTotalAmount(newTotal.toString());

    // Renumber line numbers
    const renumberedData = newData.map((item, index) => ({
      ...item,
      lineNumber: (index + 1).toString().padStart(3, '0')
    }));
    setTableData(renumberedData);
  };

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Day End</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              const isSelected = selectedScrollOption === option;
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02720F' : '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                    onPress={() => setSelectedScrollOption(option)}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: isSelected ? 'white' : 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.paymentContainer}>
          {/* Section Title */}
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Day End Details</Text>
          </View>

          {/* Row 1: Date and Document No. */}
          <View style={styles.inputRow}>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.dateText}>{formatDate(dayEndDate)}</Text>
            </TouchableOpacity>

            <TextInput
              style={styles.textField}
              placeholder="Document Nos."
              value={documentNo}
              onChangeText={setDocumentNo}
            />
          </View>

          {/* Date Picker */}
          {showDatePicker && (
            <DateTimePicker
              value={dayEndDate}
              mode="date"
              display="default"
              onChange={onDateChange}
            />
          )}

          {/* Row 2: Cash on hand */}
          <View style={styles.inputRow}>
            <View style={styles.labelContainer}>
              <Text style={styles.inputLabel}>Cash on hand:</Text>
            </View>
            <TextInput
              style={[styles.textField, styles.readOnlyField]}
              value={cashOnHand}
              editable={false}
            />
          </View>

          {/* Section Title */}
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Expense Details</Text>
          </View>

          {/* Row 3: Expenses Group, Expenses Head */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Expense Group:</Text>
              <Dropdown
                data={[
                  { label: 'Group 1', value: 'Group 1' },
                  { label: 'Group 2', value: 'Group 2' },
                  { label: 'Group 3', value: 'Group 3' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Group"
                value={expensesGroup}
                onChange={(item) => setExpensesGroup(item.value)}
                style={[styles.dropdown, errors.expensesGroup && styles.inputError]}
              />
            </View>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Expense Head:</Text>
              <Dropdown
                data={[
                  { label: 'Expense 1', value: 'Expense 1' },
                  { label: 'Expense 2', value: 'Expense 2' },
                  { label: 'Expense 3', value: 'Expense 3' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Head"
                value={expensesHead}
                onChange={(item) => setExpensesHead(item.value)}
                style={[styles.dropdown, errors.expensesHead && styles.inputError]}
              />
            </View>
          </View>

          {/* Row 4: Budget Amount, Remark */}
          <View style={styles.inputRow}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Budget Amount:</Text>
              <TextInput
                style={[styles.textField, errors.budgetAmount && styles.inputError]}
                placeholder="Enter Amount"
                value={budgetAmount}
                onChangeText={setBudgetAmount}
                keyboardType="numeric"
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Remark:</Text>
              <TextInput
                style={[styles.textField, errors.remark && styles.inputError]}
                placeholder="Enter Remark"
                value={remark}
                onChangeText={setRemark}
              />
              {errors.remark && <Text style={styles.errorText}>{errors.remark}</Text>}
            </View>
          </View>

          {/* Row 5: Payment Mode, Amount */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Payment Mode:</Text>
              <Dropdown
                data={[
                  { label: 'Cash', value: 'Cash' },
                  { label: 'Credit Card', value: 'Credit Card' },
                  { label: 'Bank Transfer', value: 'Bank Transfer' },
                  { label: 'HDFC Bank', value: 'HDFC Bank' },
                  { label: 'SBI Bank', value: 'SBI Bank' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Mode"
                value={paymentMode}
                onChange={(item) => setPaymentMode(item.value)}
                style={[styles.dropdown, errors.paymentMode && styles.inputError]}
              />
              {errors.paymentMode && <Text style={styles.errorText}>{errors.paymentMode}</Text>}
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Amount:</Text>
              <TextInput
                style={[styles.textField, errors.amount && styles.inputError]}
                placeholder="Enter Amount"
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
              />
              {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}
            </View>
          </View>

          {/* Row 5: Add and Clear Buttons */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddItem}
            >
              <Text style={styles.buttonText}>Add</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClear}
            >
              <Text style={styles.buttonText}>Clear</Text>
            </TouchableOpacity>
          </View>

          {/* Section Title */}
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Transaction Details</Text>
          </View>

          {/* Row 6: Table */}
          <View style={styles.tableContainer}>
            <DataTable>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.checkboxColumn}></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Line No</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Bank Account</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Remark</Text></DataTable.Title>
                <DataTable.Title numeric><Text style={styles.tableHeaderText}>Amount</Text></DataTable.Title>
              </DataTable.Header>

              {tableData.length > 0 ? (
                tableData.map((item) => (
                  <DataTable.Row key={item.id} style={item.isChecked ? styles.selectedRow : null}>
                    <DataTable.Cell style={styles.checkboxColumn}>
                      <TouchableOpacity onPress={() => toggleCheckbox(item.id)}>
                        <View style={[styles.checkbox, item.isChecked && styles.checkboxChecked]}>
                          {item.isChecked && <Text style={styles.checkmark}>✓</Text>}
                        </View>
                      </TouchableOpacity>
                    </DataTable.Cell>
                    <DataTable.Cell>{item.lineNumber}</DataTable.Cell>
                    <DataTable.Cell>{item.bankAccountName}</DataTable.Cell>
                    <DataTable.Cell>{item.remark}</DataTable.Cell>
                    <DataTable.Cell numeric>{item.amount}</DataTable.Cell>
                  </DataTable.Row>
                ))
              ) : (
                <DataTable.Row>
                  <DataTable.Cell style={{ flex: 5, justifyContent: 'center' }}>
                    <Text style={styles.emptyTableText}>No transactions added yet</Text>
                  </DataTable.Cell>
                </DataTable.Row>
              )}
            </DataTable>
          </View>

          {/* Row 7: Delete Button and Total Amount */}
          <View style={styles.finalRow}>
            <TouchableOpacity
              style={[styles.deleteButton, tableData.filter(item => item.isChecked).length === 0 && styles.disabledButton]}
              onPress={handleDeleteSelected}
              disabled={tableData.filter(item => item.isChecked).length === 0}
            >
              <Text style={styles.buttonText}>Delete Selected</Text>
            </TouchableOpacity>

            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Total:</Text>
              <TextInput
                style={styles.totalField}
                value={totalAmount}
                editable={false}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Section Styles
  sectionTitleContainer: {
    backgroundColor: '#F0F0F0',
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginVertical: 10,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#02096A',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#02096A',
  },

  // Payment Screen Styles
  paymentContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  inputContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  labelContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingLeft: 5,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 5,
  },
  textField: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    marginHorizontal: 4,
  },
  readOnlyField: {
    backgroundColor: '#F5F5F5',
    color: '#666',
  },
  dropdownContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  dropdown: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  datePickerButton: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  inputError: {
    borderColor: 'red',
    borderWidth: 1,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 2,
    marginLeft: 5,
  },

  // Button Styles
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 15,
  },
  addButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  clearButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },

  // Table Styles
  tableContainer: {
    marginVertical: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  tableHeader: {
    backgroundColor: '#02096A',
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  selectedRow: {
    backgroundColor: '#E6F0FF',
  },
  emptyTableText: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    padding: 15,
  },
  checkboxColumn: {
    maxWidth: 40,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#02096A',
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#02096A',
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
  },

  // Footer Styles
  finalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 15,
    marginBottom: 10,
  },
  deleteButton: {
    height: 50,
    backgroundColor: '#FF3333',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    flex: 2,
    marginRight: 10,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  totalContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 10,
  },
  totalField: {
    flex: 1,
    height: 50,
    backgroundColor: '#F5F5F5',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    textAlign: 'right',
    fontWeight: 'bold',
    fontSize: 16,
  }
});

export default DayEndScreen;